# Demo13 - Spring Boot JWT Authentication Demo

这是一个使用 Spring Boot 和 JWT（JSON Web Token）实现的用户认证示例项目。

## 技术栈

- Spring Boot 2.7.17
- Spring Security
- MyBatis
- MySQL 8.0
- JWT (JJWT)
- Maven

## 功能特性

- 用户注册
- 用户登录（JWT认证）
- 角色基础权限控制
- 用户信息管理
- 安全配置
- 全局异常处理

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+

### 数据库配置

1. 创建数据库：

```sql
CREATE DATABASE school2;
```

2. 修改 `application.properties` 中的数据库配置：

```properties
spring.datasource.url=*******************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### 运行应用

1. 克隆项目：

```bash
git clone https://github.com/yourusername/demo13.git
cd demo13
```

2. 编译并运行：

```bash
mvn spring-boot:run
```

应用将在 http://localhost:8080 启动

## API 接口

### 认证接口

- 用户注册：`POST /api/auth/register`
- 用户登录：`POST /api/auth/login`

### 请求示例

#### 注册用户

```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "nickname": "Test User"
  }'
```

#### 用户登录

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

## 项目结构

```
demo13
├── src/main/java/com/example/demo13
│   ├── common          // 通用类
│   ├── config          // 配置类
│   ├── constant        // 常量类
│   ├── controller      // 控制器
│   ├── exception       // 异常处理
│   ├── filter         // 过滤器
│   ├── handler        // 处理器
│   ├── mapper         // MyBatis映射器
│   ├── model          // 实体类
│   ├── security       // 安全相关
│   ├── service        // 服务层
│   └── util           // 工具类
├── src/main/resources
│   ├── mapper         // MyBatis映射文件
│   ├── application.properties
│   └── schema.sql     // 数据库初始化脚本
└── pom.xml
```

## 安全说明

- 使用 BCrypt 进行密码加密
- JWT 用于无状态认证
- 实现了基于角色的访问控制
- 配置了安全响应头
- 实现了跨域资源共享（CORS）配置

## 贡献

欢迎提交 Issue 和 Pull Request。

## 许可证

[MIT License](LICENSE) 