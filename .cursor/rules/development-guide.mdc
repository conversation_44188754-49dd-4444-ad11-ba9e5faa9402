---
description: 
globs: 
alwaysApply: false
---
# 开发环境与原则指南

## 开发环境

- **操作系统**：Windows
- **终端工具**：Git PowerShell
- **代码提交**：使用Git进行版本控制
- **编码规范**：UTF-8编码

## 开发原则

### SOLID原则
- **单一职责原则（S）**：一个类只负责一个功能领域中的相应职责
- **开闭原则（O）**：软件实体应对扩展开放，对修改关闭
- **里氏替换原则（L）**：所有引用基类的地方必须能透明地使用其子类对象
- **接口隔离原则（I）**：客户端不应依赖它不需要的接口
- **依赖倒置原则（D）**：高层模块不应依赖低层模块，两者都应依赖抽象

### 其他核心原则
- **DRY（不要重复自己）**：避免代码重复，提取共用逻辑
- **KISS（保持简单）**：保持代码简单明了，避免不必要的复杂性
- **YAGNI（你不会需要它）**：不添加当前不需要的功能
- **OWASP最佳实践**：遵循安全编码标准，防止常见安全漏洞

### 工作方法
- **任务分解**：将大任务分解为小单元，便于实现和测试
- **逐步解决问题**：一次解决一个问题，而不是尝试同时解决多个问题

## 项目结构
本项目使用标准的Spring Boot分层架构：

- **controller层**：处理HTTP请求，返回响应
- **service层**：包含业务逻辑
- **mapper/repository层**：负责数据访问
- **model层**：数据模型和实体类
- **config层**：应用配置
- **utils层**：通用工具类

## 编码规范
- 所有Java代码必须遵循阿里巴巴Java开发手册规范
- 方法和变量命名使用驼峰命名法
- 所有业务方法必须有适当的注释
- 所有公共API必须有JavaDoc文档
- 异常必须被适当处理和记录


