---
description: 
globs: 
alwaysApply: false
---
# 项目架构指南

## 项目概述
本项目是一个基于Spring Boot的JWT认证系统，实现了用户注册、登录、权限管理等功能。

## 技术栈
- **框架**：Spring Boot
- **安全**：Spring Security + JWT
- **数据库**：MySQL
- **ORM**：MyBatis
- **构建工具**：Maven

## 核心模块

### 认证模块
认证模块负责用户的注册、登录和令牌管理：
- `AuthController` - 处理认证相关请求
- `AuthService` - 实现认证业务逻辑
- `JwtUtil` - JWT工具类，负责令牌的生成和验证

### 用户模块
用户模块负责用户信息的管理：
- `UserController` - 处理用户相关请求
- `UserService` - 实现用户业务逻辑
- `UserMapper` - 用户数据访问层

### 安全模块
安全模块负责权限控制和安全配置：
- `SecurityConfig` - Spring Security配置
- `JwtFilter` - JWT认证过滤器
- `CustomUserDetailsService` - 自定义用户详情服务

### 公共模块
公共模块包含通用的工具类和配置：
- `Result` - 统一响应结果
- `BusinessException` - 业务异常
- `GlobalExceptionHandler` - 全局异常处理
- `ResponseCode` - 响应状态码常量

## 数据模型
- `User` - 用户实体
- `Role` - 角色实体
- `UserRole` - 用户角色关联实体

## 接口规范
所有API遵循RESTful设计原则：
- GET - 获取资源
- POST - 创建资源
- PUT - 更新资源
- DELETE - 删除资源

## 异常处理
使用全局异常处理器统一处理异常：
- 业务异常 - 返回特定的业务错误码
- 系统异常 - 返回500错误码
- 参数验证异常 - 返回400错误码

## 安全措施
- 使用BCrypt加密存储密码
- 使用JWT实现无状态认证
- 实现基于角色的访问控制
- 配置安全响应头防止常见Web攻击

