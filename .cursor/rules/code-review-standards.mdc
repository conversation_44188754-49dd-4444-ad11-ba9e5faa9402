---
description: 
globs: 
alwaysApply: false
---
# 代码审查标准

## 代码质量检查点

### 代码结构
- 类和方法职责是否单一
- 代码是否遵循SOLID原则
- 是否有代码重复
- 命名是否清晰表达意图
- 代码层次是否清晰（控制器->服务->数据访问）

### 性能考量
- 数据库查询是否高效
- 是否存在不必要的循环或计算
- 是否有潜在的内存泄漏
- 是否使用了适当的缓存策略

### 安全审查
- 输入是否经过验证和净化
- 敏感数据是否加密
- 认证和授权是否正确实现
- SQL注入、XSS等漏洞是否防护

### 异常处理
- 是否有适当的异常处理
- 异常是否分类明确
- 是否记录了必要的日志
- 是否向用户提供适当的错误信息

### 测试覆盖
- 单元测试是否覆盖核心功能
- 测试是否包含边界条件
- 是否有集成测试
- 测试是否独立且可重复执行

### 文档质量
- 类和方法是否有清晰的注释
- 是否解释了复杂的业务逻辑
- 接口文档是否完整
- 是否包含使用示例

## 代码审查流程

### 提交前自检
1. 运行本地测试确保通过
2. 检查代码风格是否符合规范
3. 确保没有引入新的警告
4. 验证功能是否按预期工作

### 审查过程
1. 审查者关注代码逻辑和架构
2. 提出建设性意见
3. 讨论可能的改进
4. 确认修改是否符合要求

### 合并标准
1. 所有测试必须通过
2. 代码符合项目规范
3. 至少一名审查者批准
4. 没有未解决的关键问题

