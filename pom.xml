<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- parent: 定义项目的父项目, 这里使用 Spring Boot Starter Parent 来统一管理依赖版本 -->
    <!--当你的项目继承了 spring-boot-starter-parent 后，你在引入 Spring Boot 官方支持的依赖
    （如 spring-boot-starter-web, spring-data-jpa, lombok, jackson 等）时，
    你不再需要指定 <version> 标签！Maven 会自动从父 POM（spring-boot-starter-parent.pom文件） 中找到并使用那个预先定义好的、兼容的版本。-->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version> <!-- 从 build.gradle 同步 Spring Boot 版本 -->
        <relativePath/> <!-- relativePath为空时，Maven会从远程仓库查找父POM -->
    </parent>

    <!--当前项目的公司域名和项目名称-->
    <groupId>com.example</groupId>
    <artifactId>pure</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>pure</name>
    <description>Demo project for Spring Boot</description>

    <!-- properties: 定义项目中可复用的属性值 -->
    <properties>
        <!-- java.version: 指定项目使用的 Java 版本 -->
        <java.version>11</java.version>
        <!-- mybatis-spring-boot-starter.version: MyBatis Spring Boot Starter 版本 -->
        <mybatis-spring-boot-starter.version>2.3.1</mybatis-spring-boot-starter.version>
        <!-- mysql-connector-j.version: MySQL Connector/J 版本 -->
        <mysql-connector-j.version>8.0.33</mysql-connector-j.version>
        <!-- justauth.version: JustAuth 版本 (第三方登录 OAuth2) -->
        <justauth.version>1.16.7</justauth.version>
        <!-- simple-http.version: Simple HTTP 客户端版本 (JustAuth 依赖) -->
        <simple-http.version>1.0.5</simple-http.version>
        <!-- hutool.version: Hutool 工具类库版本 -->
        <hutool.version>5.8.20</hutool.version>
        <!-- springdoc.version: SpringDoc OpenAPI UI 版本 (API 文档) -->
        <springdoc.version>1.6.13</springdoc.version>
        <!-- ip2region.version: IP 地址定位库版本 -->
        <ip2region.version>2.7.0</ip2region.version>
        <!-- zxing.version: ZXing (二维码处理) 版本 -->
        <zxing.version>3.5.2</zxing.version>
    </properties>

    <!-- 阿里云镜像源配置 -->
    <repositories>
        <repository>
            <id>aliyunmaven</id>
            <name>Alibaba Cloud Maven Mirror</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled> <!-- 通常不建议从镜像使用快照版本 -->
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyunmaven-plugin</id>
            <name>Alibaba Cloud Maven Plugin Mirror</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!-- dependencies: 项目依赖列表 -->
    <dependencies>
        <!-- Spring Boot Starters: Spring Boot 启动器依赖 -->
        <!-- dependency: 定义一个项目依赖 -->
        <dependency>
            <!-- groupId: 依赖的项目组ID, artifactId: 项目的模块ID -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- Spring Boot Web 框架依赖 (如 Spring MVC, Tomcat), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <!-- Spring Boot Security 框架依赖 (安全认证和授权), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <!-- Spring Boot Validation 依赖 (数据校验), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <!-- Spring Boot AOP 依赖 (面向切面编程), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <!-- Spring Boot Actuator 依赖 (应用监控和管理), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <!-- Spring Boot Logging 依赖 (日志管理), 版本由父项目管理 -->
        </dependency>

        <!-- WebSocket支持: WebSocket Support -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <!-- Spring Boot WebSocket 依赖, 版本由父项目管理 -->
        </dependency>

        <!-- Database: 数据库相关依赖 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter.version}</version>
            <!-- MyBatis Spring Boot Starter 依赖 (MyBatis ORM 框架集成) -->
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql-connector-j.version}</version>
            <!-- scope: 依赖范围, runtime 表示运行时需要 -->
            <scope>runtime</scope>
            <!-- MySQL 数据库连接驱动 -->
        </dependency>

        <!-- 依赖名称: JJWT API (Java JWT: JSON Web Token API) -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>
        <!-- 依赖名称: JJWT Impl (Java JWT: JSON Web Token Implementation) -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>
        <!-- 依赖名称: JJWT Jackson (Java JWT: Jackson JSON Processor Integration) -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>

        <!-- JustAuth: 第三方 OAuth2 登录集成 -->
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
            <version>${justauth.version}</version>
            <!-- JustAuth 核心库 (第三方登录 OAuth2 解决方案) -->
        </dependency>
        <dependency>
            <groupId>com.xkcoding.http</groupId>
            <artifactId>simple-http</artifactId>
            <version>${simple-http.version}</version>
            <!-- Simple HTTP 客户端 (JustAuth 可能依赖) -->
        </dependency>

        <!-- Utils: 工具类依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <!-- scope: provided 表示编译和测试时需要，但不会打包到最终产物 -->
            <scope>provided</scope>
            <!-- Lombok 工具库 (简化 Java 代码), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
            <!-- Hutool Java 工具类库 (全量包) -->
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${springdoc.version}</version>
            <!-- SpringDoc OpenAPI UI 依赖 (自动生成 API 文档界面) -->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <!-- Apache Commons Lang3 工具库, 版本由父项目管理 (3.12.0) -->
        </dependency>
        <!-- 依赖名称: Google Guava (Google核心Java库) -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
        </dependency>

        <!-- 参数校验: org.hibernate.validator:hibernate-validator 已由 spring-boot-starter-validation 引入并管理版本 -->

        <!-- Test: 测试相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <!-- Spring Boot 测试启动器, 版本由父项目管理 -->
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                    <!-- exclusion: 排除传递性依赖, 这里排除 JUnit Vintage 引擎 -->
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
            <!-- Spring Security 测试支持, 版本由父项目管理 -->
        </dependency>

        <!-- Redis依赖: Redis Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <!-- Spring Boot Data Redis 依赖 (Redis 数据访问), 版本由父项目管理 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
            <!-- Spring Boot Cache 依赖 (缓存抽象), 版本由父项目管理 -->
        </dependency>

        <!-- Jackson JSR310支持: Jackson JSR310 Support for Java 8 Date/Time -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <!-- Jackson 对 Java 8 日期时间 API (JSR310) 的支持, 版本由父项目管理 -->
        </dependency>
        <!-- AWS SDK for Java (S3): Amazon Web Services Software Development Kit for Java (S3) -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>2.20.26</version> <!-- 请使用一个较新的版本 -->
        </dependency>

        <!-- Logback: ch.qos.logback:logback-classic 已由 spring-boot-starter-logging 引入并管理版本 -->

        <!-- IP2Region: IP 地址定位库 -->
        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>${ip2region.version}</version>
            <!-- IP 地址到地理位置的转换库 -->
        </dependency>

        <!-- 邮件发送依赖: Mail Sending Dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <!-- Spring Boot Mail 依赖 (邮件发送), 版本由父项目管理 -->
        </dependency>

        <!-- Thymeleaf模板引擎依赖: Thymeleaf Template Engine Dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <!-- Spring Boot Thymeleaf 依赖 (服务端模板引擎), 版本由父项目管理 -->
        </dependency>

        <!-- ZXing - 二维码生成和解析: ZXing - QR Code Generation and Parsing -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
            <!-- ZXing 核心库 (二维码处理) -->
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
            <!-- ZXing JavaSE 平台支持 -->
        </dependency>

        <!-- WebClient依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
    </dependencies>

    <!-- build: 项目构建配置 -->
    <build>
        <!-- plugins: 构建过程中使用的 Maven 插件列表 -->
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <!-- Spring Boot Maven 插件, 用于打包和运行 Spring Boot 应用 -->
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <!-- 从最终的 fat jar 中排除 Lombok, 因为它主要在编译期使用 -->
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <!-- Maven 编译器插件, 用于编译 Java 源代码 -->
                <configuration>
                    <!-- source: 指定源代码的 Java 版本 -->
                    <source>${java.version}</source>
                    <!-- target: 指定编译后 class 文件的 Java 版本 -->
                    <target>${java.version}</target>
                    <!-- encoding: 指定源文件编码 -->
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
