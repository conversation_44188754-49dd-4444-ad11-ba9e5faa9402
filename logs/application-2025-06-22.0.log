2025-06-22 10:00:41.402 [31mWAR<PERSON> [0;39m [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11h23m41s874ms273µs100ns).
2025-06-22 10:25:40.094 [34mINFO [0;39m [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-06-22 10:55:40.100 [34mINFO [0;39m [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
2025-06-22 11:25:40.112 [34mINFO [0;39m [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
2025-06-22 11:55:40.121 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
2025-06-22 12:03:16.560 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-22 12:03:16.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-22 12:03:16.561 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-22 12:03:16.561 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-22 12:03:16.566 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-22 12:03:16.566 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-22 12:03:16.712 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-22 12:03:16.716 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:03:16.716 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377]
2025-06-22 12:03:16.717 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1696330886 wrapping com.mysql.cj.jdbc.ConnectionImpl@2b54443b] will be managed by Spring
2025-06-22 12:03:16.717 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-22 12:03:16.718 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-22 12:03:16.723 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-22 12:03:16.726 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:03:16.726 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d]
2025-06-22 12:03:16.726 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@94227086 wrapping com.mysql.cj.jdbc.ConnectionImpl@1ef9b70d] will be managed by Spring
2025-06-22 12:03:16.726 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-22 12:03:16.727 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-22 12:03:16.734 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-22 12:03:16.735 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377]
2025-06-22 12:03:16.736 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-22 12:03:16.736 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377] from current transaction
2025-06-22 12:03:16.736 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-22 12:03:16.736 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-22 12:03:16.742 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-22 12:03:16.742 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d]
2025-06-22 12:03:16.742 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-22 12:03:16.742 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d] from current transaction
2025-06-22 12:03:16.743 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-22 12:03:16.743 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-22 12:03:16.748 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-22 12:03:16.749 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377]
2025-06-22 12:03:16.749 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-22 12:03:16.749 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377]
2025-06-22 12:03:16.750 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377]
2025-06-22 12:03:16.750 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3b8377]
2025-06-22 12:03:16.757 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-22 12:03:16.758 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d]
2025-06-22 12:03:16.758 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-22 12:03:16.758 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d]
2025-06-22 12:03:16.758 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d]
2025-06-22 12:03:16.758 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62a5fe6d]
2025-06-22 12:03:16.786 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-22 12:03:16.788 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-22 12:03:16.788 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-22 12:03:16.789 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-22 12:03:16.789 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-22 12:03:16.792 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:03:16.792 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a75b71] was not registered for synchronization because synchronization is not active
2025-06-22 12:03:16.792 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@689602442 wrapping com.mysql.cj.jdbc.ConnectionImpl@2b54443b] will not be managed by Spring
2025-06-22 12:03:16.792 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-22 12:03:16.792 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-22 12:03:16.800 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-22 12:03:16.801 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-22 12:03:16.801 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-22 12:03:16.801 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-22 12:03:16.802 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-22 12:03:16.804 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:03:16.804 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ceb72f1] was not registered for synchronization because synchronization is not active
2025-06-22 12:03:16.804 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-22 12:03:16.804 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a75b71]
2025-06-22 12:03:16.805 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1021874809 wrapping com.mysql.cj.jdbc.ConnectionImpl@1ef9b70d] will not be managed by Spring
2025-06-22 12:03:16.805 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-22 12:03:16.805 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-22 12:03:16.806 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-22 12:03:16.806 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-22T04: (truncated)...]
2025-06-22 12:03:16.808 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-22 12:03:16.809 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-22 12:03:16.819 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-22 12:03:16.819 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ceb72f1]
2025-06-22 12:03:16.820 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:03:16.820 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@44effdcf] was not registered for synchronization because synchronization is not active
2025-06-22 12:03:16.820 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1664381785 wrapping com.mysql.cj.jdbc.ConnectionImpl@1ef9b70d] will not be managed by Spring
2025-06-22 12:03:16.820 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-22 12:03:16.820 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-22 12:03:16.838 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-22 12:03:16.838 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@44effdcf]
2025-06-22 12:03:16.838 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:03:16.838 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd1e248] was not registered for synchronization because synchronization is not active
2025-06-22 12:03:16.838 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1189837240 wrapping com.mysql.cj.jdbc.ConnectionImpl@1ef9b70d] will not be managed by Spring
2025-06-22 12:03:16.838 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-22 12:03:16.839 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-22 12:03:16.854 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-22 12:03:16.854 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd1e248]
2025-06-22 12:03:16.854 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-22 12:03:16.855 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-22 12:03:16.855 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-22 12:03:16.855 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-22 12:14:19.366 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-22 12:14:19.366 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-22 12:14:19.367 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-22 12:14:19.367 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-22 12:14:19.367 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-22 12:14:19.367 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-22 12:14:19.488 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-22 12:14:19.492 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-22 12:14:19.492 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:14:19.492 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5276d4be]
2025-06-22 12:14:19.492 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@336009725 wrapping com.mysql.cj.jdbc.ConnectionImpl@2b54443b] will be managed by Spring
2025-06-22 12:14:19.492 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-22 12:14:19.493 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-22 12:14:19.495 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-22 12:14:19.497 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-22 12:14:19.497 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:14:19.497 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362f7933]
2025-06-22 12:14:19.497 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@675634021 wrapping com.mysql.cj.jdbc.ConnectionImpl@1ef9b70d] will be managed by Spring
2025-06-22 12:14:19.497 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-22 12:14:19.497 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-22 12:14:19.504 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-22 12:14:19.504 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5276d4be]
2025-06-22 12:14:19.504 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-22 12:14:19.504 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5276d4be]
2025-06-22 12:14:19.504 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5276d4be]
2025-06-22 12:14:19.504 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5276d4be]
2025-06-22 12:14:19.510 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-22 12:14:19.510 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362f7933]
2025-06-22 12:14:19.510 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-22 12:14:19.510 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362f7933]
2025-06-22 12:14:19.510 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362f7933]
2025-06-22 12:14:19.510 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362f7933]
2025-06-22 12:14:19.535 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-22 12:14:19.535 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-22 12:14:19.535 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-22 12:14:19.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-22 12:14:19.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-22 12:14:19.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:14:19.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40002246] was not registered for synchronization because synchronization is not active
2025-06-22 12:14:19.537 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1083924603 wrapping com.mysql.cj.jdbc.ConnectionImpl@2b54443b] will not be managed by Spring
2025-06-22 12:14:19.537 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-22 12:14:19.537 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-22 12:14:19.547 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-22 12:14:19.547 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40002246]
2025-06-22 12:14:19.547 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:14:19.547 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76586020] was not registered for synchronization because synchronization is not active
2025-06-22 12:14:19.548 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@931884735 wrapping com.mysql.cj.jdbc.ConnectionImpl@2b54443b] will not be managed by Spring
2025-06-22 12:14:19.548 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-22 12:14:19.548 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-22 12:14:19.551 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-22 12:14:19.553 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-22 12:14:19.553 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-22 12:14:19.553 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-22 12:14:19.553 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-22 12:14:19.554 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:14:19.554 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@759ac8be] was not registered for synchronization because synchronization is not active
2025-06-22 12:14:19.554 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1684174467 wrapping com.mysql.cj.jdbc.ConnectionImpl@1ef9b70d] will not be managed by Spring
2025-06-22 12:14:19.554 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-22 12:14:19.554 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-22 12:14:19.558 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-22 12:14:19.558 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76586020]
2025-06-22 12:14:19.559 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-22 12:14:19.559 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a6ad809] was not registered for synchronization because synchronization is not active
2025-06-22 12:14:19.559 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@260498948 wrapping com.mysql.cj.jdbc.ConnectionImpl@2b54443b] will not be managed by Spring
2025-06-22 12:14:19.559 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-22 12:14:19.559 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-22 12:14:19.568 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-22 12:14:19.568 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-22 12:14:19.568 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@759ac8be]
2025-06-22 12:14:19.568 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a6ad809]
2025-06-22 12:14:19.568 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-22 12:14:19.568 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-22 12:14:19.569 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-22 12:14:19.569 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-22T04: (truncated)...]
2025-06-22 12:14:19.569 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-22 12:14:19.569 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-22 12:14:19.569 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-22 12:14:19.569 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-22 12:25:40.125 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 12]
2025-06-22 12:55:40.125 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 13]
2025-06-22 13:25:40.170 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 14]
2025-06-22 13:55:40.182 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 15]
2025-06-22 14:25:40.185 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 16]
2025-06-22 14:55:40.191 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 17]
2025-06-22 15:25:40.199 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 18]
2025-06-22 15:55:40.202 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 19]
2025-06-22 16:25:40.206 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 20]
2025-06-22 16:55:40.211 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 21]
2025-06-22 17:19:55.207 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-22 17:19:55.208 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-22 17:19:55.208 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-22 17:19:55.208 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d94c50e]]
2025-06-22 17:19:55.209 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d94c50e]
2025-06-22 17:19:55.209 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d94c50e]
2025-06-22 17:19:55.209 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-22 17:19:55.209 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-22 17:19:55.209 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-22 17:19:55.963 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-22 17:19:55.972 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
