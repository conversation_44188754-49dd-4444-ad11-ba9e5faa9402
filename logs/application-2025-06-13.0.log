2025-06-13 00:09:51.668 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:09:51.668 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:09:51.669 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:09:51.719 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:09:51.726 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:09:51.726 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@620dabae]
2025-06-13 00:09:51.726 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1148248642 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:09:51.726 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:09:51.726 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:09:51.741 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:09:51.741 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@620dabae]
2025-06-13 00:09:51.741 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:09:51.741 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@620dabae]
2025-06-13 00:09:51.741 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@620dabae]
2025-06-13 00:09:51.741 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@620dabae]
2025-06-13 00:09:51.780 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:09:51.780 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:09:51.781 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:09:51.784 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:09:51.784 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:09:51.784 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:09:51.784 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:09:51.785 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:09:51.785 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:09:51.786 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:09:51.787 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:09:51.787 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:09:51.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:09:51.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:09:51.831 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:09:51.837 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:09:51.837 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24b545d7]
2025-06-13 00:09:51.837 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@391044796 wrapping com.mysql.cj.jdbc.ConnectionImpl@385fc14c] will be managed by Spring
2025-06-13 00:09:51.837 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:09:51.838 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:09:51.852 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:09:51.852 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24b545d7]
2025-06-13 00:09:51.853 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:09:51.853 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24b545d7]
2025-06-13 00:09:51.853 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24b545d7]
2025-06-13 00:09:51.853 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24b545d7]
2025-06-13 00:09:51.893 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:09:51.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-13 00:09:51.895 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:09:51.895 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-13 00:09:51.896 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:09:51.925 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:09:51.925 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779af6b2]
2025-06-13 00:09:51.925 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@287968255 wrapping com.mysql.cj.jdbc.ConnectionImpl@385fc14c] will be managed by Spring
2025-06-13 00:09:51.925 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:09:51.925 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-13 00:09:51.942 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.getComments : <==      Total: 11
2025-06-13 00:09:51.943 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779af6b2]
2025-06-13 00:09:51.943 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779af6b2]
2025-06-13 00:09:51.943 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779af6b2]
2025-06-13 00:09:51.943 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779af6b2]
2025-06-13 00:09:51.986 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:09:51.986 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:09:51.988 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:09:51.988 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:09:52.000 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:09:52.000 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:09:52.001 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:09:52.039 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:09:52.046 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:09:52.046 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630e6fde]
2025-06-13 00:09:52.046 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@420471088 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:09:52.046 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:09:52.046 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:09:52.059 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:09:52.060 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630e6fde]
2025-06-13 00:09:52.060 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:09:52.060 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630e6fde]
2025-06-13 00:09:52.060 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630e6fde]
2025-06-13 00:09:52.060 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630e6fde]
2025-06-13 00:09:52.093 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:09:52.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:09:52.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:09:52.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:09:52.095 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:09:52.095 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:09:52.096 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:09:52.096 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:09:52.097 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:09:52.099 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:09:52.099 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:10:02.804 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:02.804 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:10:02.805 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:02.842 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:10:02.847 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:02.847 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bf8cec9]
2025-06-13 00:10:02.847 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1931782501 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:10:02.847 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:10:02.847 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:10:02.852 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:10:02.852 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:10:02.853 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:10:02.859 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:10:02.859 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bf8cec9]
2025-06-13 00:10:02.859 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:10:02.859 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bf8cec9]
2025-06-13 00:10:02.859 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bf8cec9]
2025-06-13 00:10:02.859 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bf8cec9]
2025-06-13 00:10:02.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:10:02.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:10:02.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:02.893 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:10:02.893 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:02.894 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:02.894 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:10:02.895 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:10:02.895 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:10:02.897 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:10:02.897 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:10:02.898 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:10:02.901 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:02.901 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a32e0e2]
2025-06-13 00:10:02.901 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@926679137 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:10:02.901 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:10:02.901 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:10:02.916 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:10:02.916 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a32e0e2]
2025-06-13 00:10:02.917 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:10:02.917 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a32e0e2]
2025-06-13 00:10:02.917 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a32e0e2]
2025-06-13 00:10:02.917 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a32e0e2]
2025-06-13 00:10:02.959 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:10:02.959 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-13 00:10:02.959 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:10:02.959 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-13 00:10:02.960 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:10:02.988 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:02.988 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12f32a4a]
2025-06-13 00:10:02.988 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1988416088 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:10:02.988 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:10:02.988 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-13 00:10:03.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.getComments : <==      Total: 11
2025-06-13 00:10:03.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12f32a4a]
2025-06-13 00:10:03.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12f32a4a]
2025-06-13 00:10:03.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12f32a4a]
2025-06-13 00:10:03.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12f32a4a]
2025-06-13 00:10:03.044 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:10:03.045 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:10:03.046 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:10:03.046 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:10:03.052 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:03.053 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:10:03.053 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:03.085 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:10:03.089 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:03.089 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be61d72]
2025-06-13 00:10:03.089 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1571170852 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:10:03.089 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:10:03.089 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:10:03.112 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:10:03.113 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be61d72]
2025-06-13 00:10:03.113 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:10:03.113 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be61d72]
2025-06-13 00:10:03.113 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be61d72]
2025-06-13 00:10:03.113 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be61d72]
2025-06-13 00:10:03.155 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:10:03.155 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:10:03.155 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:03.155 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:10:03.156 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:03.156 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:03.157 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:10:03.158 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:10:03.158 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:10:03.159 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:10:03.160 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:10:31.290 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:31.290 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:10:31.291 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:31.325 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:10:31.325 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:10:31.325 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:10:31.337 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:10:31.341 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:31.341 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8223581]
2025-06-13 00:10:31.341 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@948115311 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:10:31.341 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:10:31.342 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:10:31.355 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:10:31.356 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8223581]
2025-06-13 00:10:31.356 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:10:31.356 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8223581]
2025-06-13 00:10:31.356 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8223581]
2025-06-13 00:10:31.356 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8223581]
2025-06-13 00:10:31.362 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:10:31.365 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:31.365 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f146470]
2025-06-13 00:10:31.365 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1310326412 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:10:31.365 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:10:31.366 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:10:31.377 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:10:31.377 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f146470]
2025-06-13 00:10:31.378 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:10:31.378 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f146470]
2025-06-13 00:10:31.378 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f146470]
2025-06-13 00:10:31.378 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f146470]
2025-06-13 00:10:31.399 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:10:31.400 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:10:31.400 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:31.400 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:10:31.401 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:31.401 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:31.401 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:10:31.402 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:10:31.402 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:10:31.404 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:10:31.404 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:10:31.410 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:10:31.410 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-13 00:10:31.411 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:10:31.411 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-13 00:10:31.411 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:10:31.433 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:31.433 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@762a5573]
2025-06-13 00:10:31.433 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@447447531 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:10:31.433 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:10:31.433 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-13 00:10:31.447 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.getComments : <==      Total: 11
2025-06-13 00:10:31.448 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@762a5573]
2025-06-13 00:10:31.448 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@762a5573]
2025-06-13 00:10:31.448 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@762a5573]
2025-06-13 00:10:31.448 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@762a5573]
2025-06-13 00:10:31.481 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:10:31.482 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:10:31.483 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:10:31.483 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:10:31.489 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:31.489 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:10:31.490 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:31.523 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:10:31.525 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:10:31.525 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e0eedd7]
2025-06-13 00:10:31.526 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1856053696 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:10:31.526 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:10:31.526 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:10:31.540 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:10:31.540 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e0eedd7]
2025-06-13 00:10:31.540 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:10:31.540 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e0eedd7]
2025-06-13 00:10:31.540 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e0eedd7]
2025-06-13 00:10:31.540 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e0eedd7]
2025-06-13 00:10:31.581 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:10:31.581 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:10:31.581 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:31.582 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:10:31.582 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:10:31.582 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:10:31.583 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:10:31.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:10:31.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:10:31.586 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:10:31.586 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:12:22.371 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:12:22.371 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:12:22.372 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:12:22.404 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:12:22.404 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:12:22.405 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:12:22.418 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:12:22.420 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:12:22.421 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@223ac850]
2025-06-13 00:12:22.421 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@781481483 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:12:22.421 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:12:22.421 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:12:22.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:12:22.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@223ac850]
2025-06-13 00:12:22.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:12:22.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@223ac850]
2025-06-13 00:12:22.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@223ac850]
2025-06-13 00:12:22.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@223ac850]
2025-06-13 00:12:22.448 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:12:22.450 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:12:22.450 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5522510a]
2025-06-13 00:12:22.451 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1790358494 wrapping com.mysql.cj.jdbc.ConnectionImpl@385fc14c] will be managed by Spring
2025-06-13 00:12:22.451 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:12:22.451 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:12:22.464 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:12:22.464 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5522510a]
2025-06-13 00:12:22.464 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:12:22.464 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5522510a]
2025-06-13 00:12:22.464 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5522510a]
2025-06-13 00:12:22.464 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5522510a]
2025-06-13 00:12:22.475 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:12:22.475 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:12:22.475 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:12:22.475 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:12:22.476 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:12:22.476 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:12:22.477 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:12:22.478 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:12:22.478 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:12:22.479 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:12:22.479 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:12:22.503 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:12:22.503 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-13 00:12:22.504 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:12:22.504 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-13 00:12:22.504 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:12:22.531 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:12:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d7b4c67]
2025-06-13 00:12:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1542817995 wrapping com.mysql.cj.jdbc.ConnectionImpl@385fc14c] will be managed by Spring
2025-06-13 00:12:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:12:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-13 00:12:22.547 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.getComments : <==      Total: 11
2025-06-13 00:12:22.547 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d7b4c67]
2025-06-13 00:12:22.547 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d7b4c67]
2025-06-13 00:12:22.547 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d7b4c67]
2025-06-13 00:12:22.547 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d7b4c67]
2025-06-13 00:12:22.591 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:12:22.591 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:12:22.593 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:12:22.593 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:12:22.600 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:12:22.600 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:12:22.600 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:12:22.637 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:12:22.638 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:12:22.639 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1584f2c]
2025-06-13 00:12:22.639 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@140690316 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:12:22.639 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:12:22.639 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:12:22.650 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:12:22.651 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1584f2c]
2025-06-13 00:12:22.651 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:12:22.651 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1584f2c]
2025-06-13 00:12:22.651 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1584f2c]
2025-06-13 00:12:22.651 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1584f2c]
2025-06-13 00:12:22.683 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:12:22.683 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:12:22.683 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:12:22.683 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:12:22.684 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:12:22.684 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:12:22.685 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:12:22.685 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:12:22.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:12:22.687 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:12:22.687 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:19:57.763 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:19:57.763 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:19:57.763 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:19:57.797 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:19:57.797 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:19:57.798 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:19:57.807 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:19:57.810 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:19:57.810 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ad62d20]
2025-06-13 00:19:57.810 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@364551431 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:19:57.810 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:19:57.810 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:19:57.823 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:19:57.824 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ad62d20]
2025-06-13 00:19:57.824 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:19:57.824 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ad62d20]
2025-06-13 00:19:57.824 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ad62d20]
2025-06-13 00:19:57.824 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ad62d20]
2025-06-13 00:19:57.834 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:19:57.837 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:19:57.837 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313a2d53]
2025-06-13 00:19:57.837 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@76865827 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:19:57.837 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:19:57.837 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:19:57.848 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:19:57.848 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313a2d53]
2025-06-13 00:19:57.848 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:19:57.849 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313a2d53]
2025-06-13 00:19:57.849 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313a2d53]
2025-06-13 00:19:57.849 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313a2d53]
2025-06-13 00:19:57.859 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:19:57.859 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:19:57.860 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:19:57.860 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:19:57.860 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:19:57.861 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:19:57.861 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:19:57.862 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:19:57.862 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:19:57.863 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:19:57.863 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:19:57.881 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:19:57.881 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-13 00:19:57.881 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:19:57.882 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-13 00:19:57.882 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:19:57.904 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:19:57.904 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fe4f85a]
2025-06-13 00:19:57.904 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2115376410 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:19:57.904 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:19:57.905 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-13 00:19:57.917 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.getComments : <==      Total: 11
2025-06-13 00:19:57.918 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fe4f85a]
2025-06-13 00:19:57.918 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fe4f85a]
2025-06-13 00:19:57.918 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fe4f85a]
2025-06-13 00:19:57.918 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fe4f85a]
2025-06-13 00:19:57.950 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:19:57.951 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:19:57.952 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:19:57.952 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:19:57.962 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:19:57.962 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:19:57.962 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:19:57.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:19:57.998 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:19:57.998 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42917819]
2025-06-13 00:19:57.999 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@466294806 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:19:57.999 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:19:57.999 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:19:58.013 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:19:58.014 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42917819]
2025-06-13 00:19:58.014 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:19:58.014 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42917819]
2025-06-13 00:19:58.014 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42917819]
2025-06-13 00:19:58.014 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42917819]
2025-06-13 00:19:58.053 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:19:58.054 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:19:58.054 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:19:58.054 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:19:58.055 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:19:58.055 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:19:58.056 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:19:58.057 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:19:58.057 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:19:58.058 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:19:58.059 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:21:53.153 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:21:53.153 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:21:53.154 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:21:53.182 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:21:53.182 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:21:53.182 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:21:53.198 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:21:53.202 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:21:53.202 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243e632c]
2025-06-13 00:21:53.202 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1156062928 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:21:53.202 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:21:53.202 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:21:53.216 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:21:53.216 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243e632c]
2025-06-13 00:21:53.217 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:21:53.217 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243e632c]
2025-06-13 00:21:53.217 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243e632c]
2025-06-13 00:21:53.217 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243e632c]
2025-06-13 00:21:53.222 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:21:53.225 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:21:53.225 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@467640f8]
2025-06-13 00:21:53.225 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@340119166 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:21:53.225 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:21:53.225 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:21:53.237 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:21:53.238 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@467640f8]
2025-06-13 00:21:53.238 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:21:53.238 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@467640f8]
2025-06-13 00:21:53.238 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@467640f8]
2025-06-13 00:21:53.238 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@467640f8]
2025-06-13 00:21:53.256 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:21:53.256 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:21:53.256 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:21:53.256 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:21:53.257 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:21:53.257 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:21:53.258 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:21:53.258 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:21:53.259 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:21:53.260 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:21:53.260 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:21:53.269 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:21:53.270 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-13 00:21:53.270 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-13 00:21:53.270 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-13 00:21:53.270 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:21:53.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:21:53.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a744b0]
2025-06-13 00:21:53.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@692617560 wrapping com.mysql.cj.jdbc.ConnectionImpl@2d0d3631] will be managed by Spring
2025-06-13 00:21:53.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:21:53.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-13 00:21:53.305 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.getComments : <==      Total: 11
2025-06-13 00:21:53.306 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a744b0]
2025-06-13 00:21:53.306 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a744b0]
2025-06-13 00:21:53.306 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a744b0]
2025-06-13 00:21:53.306 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a744b0]
2025-06-13 00:21:53.338 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:21:53.338 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:21:53.339 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:21:53.339 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:21:53.345 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:21:53.346 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:21:53.346 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:21:53.376 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:21:53.379 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:21:53.379 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d955f56]
2025-06-13 00:21:53.379 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1881239315 wrapping com.mysql.cj.jdbc.ConnectionImpl@7a08c0d4] will be managed by Spring
2025-06-13 00:21:53.379 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:21:53.379 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:21:53.393 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:21:53.393 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d955f56]
2025-06-13 00:21:53.393 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:21:53.393 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d955f56]
2025-06-13 00:21:53.393 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d955f56]
2025-06-13 00:21:53.393 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d955f56]
2025-06-13 00:21:53.433 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:21:53.433 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-13 00:21:53.433 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:21:53.433 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-13 00:21:53.433 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-13 00:21:53.434 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-13 00:21:53.434 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-13 00:21:53.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-13 00:21:53.435 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-13 00:21:53.436 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-13 00:21:53.437 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:24:25.773 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-13 00:25:07.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 00:25:07.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 00:25:07.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:25:07.888 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 00:25:07.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:25:07.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ba96f5f]
2025-06-13 00:25:07.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1880476267 wrapping com.mysql.cj.jdbc.ConnectionImpl@7da2bb0f] will be managed by Spring
2025-06-13 00:25:07.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 00:25:07.892 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 00:25:07.905 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 00:25:07.906 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ba96f5f]
2025-06-13 00:25:07.906 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 00:25:07.906 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ba96f5f]
2025-06-13 00:25:07.906 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ba96f5f]
2025-06-13 00:25:07.906 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ba96f5f]
2025-06-13 00:25:07.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 00:25:07.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 00:25:07.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 00:25:07.945 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 00:25:07.945 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 00:25:07.971 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 00:25:07.972 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@370f48df]
2025-06-13 00:25:07.972 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@25505321 wrapping com.mysql.cj.jdbc.ConnectionImpl@7da2bb0f] will be managed by Spring
2025-06-13 00:25:07.972 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 00:25:07.972 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.getComments : ==> Parameters: 562(Long), 0(Integer), 10(Integer)
2025-06-13 00:25:07.986 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.getComments : <==      Total: 10
2025-06-13 00:25:07.986 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@370f48df]
2025-06-13 00:25:07.986 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@370f48df]
2025-06-13 00:25:07.986 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@370f48df]
2025-06-13 00:25:07.986 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@370f48df]
2025-06-13 00:25:08.024 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 00:25:08.024 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 00:25:08.025 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 00:25:08.025 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 00:54:25.779 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-13 01:24:25.792 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-06-13 01:45:58.561 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 01:45:58.561 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 01:45:58.562 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 01:45:58.562 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@108fd5d5]]
2025-06-13 01:45:58.562 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@108fd5d5]
2025-06-13 01:45:58.562 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@108fd5d5]
2025-06-13 01:45:58.562 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 01:45:58.562 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:45:58.562 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:45:58.905 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 01:45:58.909 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 01:53:45.980 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 01:53:45.980 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 7852 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 01:53:45.983 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 01:53:45.983 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 01:53:46.925 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 01:53:46.927 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 01:53:46.955 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 01:53:47.039 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 01:53:47.040 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 01:53:47.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 01:53:47.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 01:53:47.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 01:53:47.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 01:53:47.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 01:53:47.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 01:53:47.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 01:53:47.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 01:53:47.533 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 01:53:47.538 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 01:53:47.538 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 01:53:47.538 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 01:53:47.622 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 01:53:47.622 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1594 ms
2025-06-13 01:53:47.825 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 01:53:47.838 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 01:53:47.843 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 01:53:47.849 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 01:53:47.855 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 01:53:47.859 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 01:53:47.864 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 01:53:47.868 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 01:53:47.873 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 01:53:47.881 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 01:53:47.892 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 01:53:47.897 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 01:53:47.901 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 01:53:47.906 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 01:53:47.919 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 01:53:48.303 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 01:53:48.839 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 01:53:48.839 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 01:53:49.094 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 01:53:49.096 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.156 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:53:49.156 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.157 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 01:53:49.158 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.159 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 01:53:49.159 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.159 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 01:53:49.159 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.159 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 01:53:49.159 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.160 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:53:49.160 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.163 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 01:53:49.163 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.163 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:53:49.163 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:53:49.252 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 01:53:49.254 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 01:53:49.258 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2db6ba81, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@10728fe8, org.springframework.security.web.context.SecurityContextPersistenceFilter@6ed7c178, org.springframework.security.web.header.HeaderWriterFilter@4714f447, org.springframework.security.web.authentication.logout.LogoutFilter@28ceb25e, com.example.pure.filter.JwtFilter@3b4f1eb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b657342, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15a2fddd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16fb9fdd, org.springframework.security.web.session.SessionManagementFilter@13dc383b, org.springframework.security.web.access.ExceptionTranslationFilter@64e3bc2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@c49e434]
2025-06-13 01:53:49.261 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 01:53:49.263 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 01:53:49.464 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 01:53:49.483 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 01:53:49.535 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 01:53:49.544 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 01:53:49.832 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 01:53:49.939 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 01:53:49.958 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 01:53:49.959 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 01:53:49.960 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 01:53:49.961 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@458704ee, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@45b2d17b, org.springframework.security.web.context.SecurityContextPersistenceFilter@29a50a11, org.springframework.security.web.header.HeaderWriterFilter@35277c6c, org.springframework.web.filter.CorsFilter@5243d730, org.springframework.security.web.authentication.logout.LogoutFilter@337fb1a5, com.example.pure.filter.JwtFilter@3b4f1eb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@74cff17c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a053795, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15afebcc, org.springframework.security.web.session.SessionManagementFilter@7a364e1c, org.springframework.security.web.access.ExceptionTranslationFilter@23ffc910, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@758655a9]
2025-06-13 01:53:49.991 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@60b71e8f, started on Fri Jun 13 01:53:46 CST 2025
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 01:53:50.002 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 01:53:50.003 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 01:53:50.003 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 01:53:50.003 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 01:53:50.003 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 01:53:50.003 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 01:53:50.003 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 01:53:50.005 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
2025-06-13 01:53:50.006 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 01:53:50.007 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 01:53:50.007 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 01:53:50.007 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 01:53:50.088 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 01:53:50.120 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 01:53:50.358 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 01:53:50.365 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 01:53:50.367 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 01:53:50.367 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 01:53:50.367 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 01:53:50.367 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6a4238ff]
2025-06-13 01:53:50.367 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6a4238ff]
2025-06-13 01:53:50.367 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6a4238ff]]
2025-06-13 01:53:50.367 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 01:53:50.367 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:53:50.367 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:53:50.379 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 4.794 seconds (JVM running for 5.477)
2025-06-13 01:53:54.696 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 01:53:54.696 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 01:53:54.696 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 01:53:54.696 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 01:53:54.697 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 01:53:54.698 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@188920fc
2025-06-13 01:53:54.699 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6a5621d1
2025-06-13 01:53:54.699 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 01:53:54.699 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-13 01:53:54.709 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 01:53:54.711 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 01:53:54.719 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 01:53:55.181 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 01:53:55.199 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 01:53:55.203 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.209 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@986209319 wrapping com.mysql.cj.jdbc.ConnectionImpl@7268929b] will be managed by Spring
2025-06-13 01:53:55.211 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-06-13 01:53:55.229 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: hao111(String)
2025-06-13 01:53:55.257 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-06-13 01:53:55.258 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3] from current transaction
2025-06-13 01:53:55.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : ==>  Preparing: SELECT * FROM user WHERE id = ?
2025-06-13 01:53:55.263 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : ==> Parameters: 8(Long)
2025-06-13 01:53:55.278 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : <==      Total: 1
2025-06-13 01:53:55.278 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.278 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3] from current transaction
2025-06-13 01:53:55.279 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 01:53:55.279 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 01:53:55.292 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 01:53:55.292 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.294 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 01:53:55.294 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.294 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.294 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38217ae3]
2025-06-13 01:53:55.393 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 01:53:55.398 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 01:53:55.399 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 01:53:55.402 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 01:53:55.405 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest)
2025-06-13 01:53:55.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 01:53:55.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54856dfc]
2025-06-13 01:53:55.470 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54856dfc]
2025-06-13 01:53:55.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54856dfc]
2025-06-13 01:53:55.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54856dfc]
2025-06-13 01:53:55.508 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-13 01:53:55.510 [1;31mERROR[0;39m [http-nio-8080-exec-1] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.binding.BindingException: Parameter 'userId' not found. Available parameters are [offset, limit, videoEpisodesId, param3, param1, param2]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy110.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy151.getComments(Unknown Source)
	at com.example.pure.service.impl.VideoCommentInteractionServiceImpl.getComments(VideoCommentInteractionServiceImpl.java:97)
	at com.example.pure.service.impl.VideoCommentInteractionServiceImpl$$FastClassBySpringCGLIB$$5cc61c57.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.VideoCommentInteractionServiceImpl$$EnhancerBySpringCGLIB$$92e2584c.getComments(<generated>)
	at com.example.pure.controller.VideoCommentInteractionController.getCommentsByVideoEpisodesId(VideoCommentInteractionController.java:47)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: org.apache.ibatis.binding.BindingException: Parameter 'userId' not found. Available parameters are [offset, limit, videoEpisodesId, param3, param1, param2]
	at org.apache.ibatis.binding.MapperMethod$ParamMap.get(MapperMethod.java:210)
	at org.apache.ibatis.reflection.wrapper.MapWrapper.get(MapWrapper.java:45)
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:116)
	at org.apache.ibatis.executor.BaseExecutor.createCacheKey(BaseExecutor.java:222)
	at org.apache.ibatis.executor.CachingExecutor.createCacheKey(CachingExecutor.java:149)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 125 common frames omitted
2025-06-13 01:53:55.520 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 01:53:55.527 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-12T17:53:55.513 (truncated)...]
2025-06-13 01:53:55.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.binding.BindingException: Parameter 'userId' not found. Available parameters are [offset, limit, videoEpisodesId, param3, param1, param2]]
2025-06-13 01:53:55.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-13 01:53:55.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 01:54:49.979 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 01:58:35.691 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 01:58:35.691 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 01:58:35.691 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 01:58:35.691 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6a4238ff]]
2025-06-13 01:58:35.691 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6a4238ff]
2025-06-13 01:58:35.691 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6a4238ff]
2025-06-13 01:58:35.691 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 01:58:35.691 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:58:35.691 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:58:35.981 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 01:58:35.990 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 01:58:40.732 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 01:58:40.732 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 25008 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 01:58:40.735 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 01:58:40.735 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 01:58:41.690 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 01:58:41.692 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 01:58:41.726 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 01:58:41.830 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 01:58:41.831 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 01:58:41.831 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 01:58:41.831 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 01:58:41.831 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 01:58:41.832 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 01:58:41.832 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 01:58:41.833 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 01:58:41.834 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 01:58:41.835 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 01:58:41.836 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 01:58:41.836 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 01:58:41.836 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 01:58:42.338 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 01:58:42.342 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 01:58:42.343 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 01:58:42.343 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 01:58:42.424 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 01:58:42.424 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1645 ms
2025-06-13 01:58:42.657 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 01:58:42.669 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 01:58:42.677 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 01:58:42.684 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 01:58:42.692 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 01:58:42.699 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 01:58:42.709 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 01:58:42.716 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 01:58:42.724 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 01:58:42.733 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 01:58:42.745 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 01:58:42.751 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 01:58:42.757 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 01:58:42.762 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 01:58:42.773 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 01:58:43.153 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 01:58:43.783 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 01:58:43.784 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 01:58:44.079 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 01:58:44.082 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.155 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 01:58:44.155 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.156 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:58:44.156 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.156 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 01:58:44.158 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.158 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 01:58:44.158 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.158 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 01:58:44.159 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.159 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:58:44.159 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.162 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 01:58:44.163 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.163 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:58:44.163 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:58:44.256 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 01:58:44.257 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 01:58:44.261 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@7957aa57, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b92ea9d, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d02a066, org.springframework.security.web.header.HeaderWriterFilter@31cd0efa, org.springframework.security.web.authentication.logout.LogoutFilter@60ba6631, com.example.pure.filter.JwtFilter@3cfab340, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@43a8bd35, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6fb219dd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6b3bc1f6, org.springframework.security.web.session.SessionManagementFilter@326dbe25, org.springframework.security.web.access.ExceptionTranslationFilter@41d53813, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6655f3cd]
2025-06-13 01:58:44.263 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 01:58:44.265 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 01:58:44.462 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 01:58:44.480 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 01:58:44.537 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 01:58:44.545 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 01:58:44.851 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 01:58:44.960 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 01:58:44.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 01:58:44.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 01:58:44.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 01:58:44.981 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1f229c40, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@215404a1, org.springframework.security.web.context.SecurityContextPersistenceFilter@4693d24f, org.springframework.security.web.header.HeaderWriterFilter@3663af34, org.springframework.web.filter.CorsFilter@391b03b, org.springframework.security.web.authentication.logout.LogoutFilter@75ef1392, com.example.pure.filter.JwtFilter@3cfab340, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@236a3e4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2987b699, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a977146, org.springframework.security.web.session.SessionManagementFilter@59aba3c3, org.springframework.security.web.access.ExceptionTranslationFilter@706690e1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@11fb1691]
2025-06-13 01:58:45.015 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@495083a0, started on Fri Jun 13 01:58:40 CST 2025
2025-06-13 01:58:45.026 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 01:58:45.027 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 01:58:45.030 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 01:58:45.030 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 01:58:45.030 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 01:58:45.030 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 01:58:45.030 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 01:58:45.030 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 01:58:45.031 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-13 01:58:45.031 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 01:58:45.032 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 01:58:45.032 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 01:58:45.032 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 01:58:45.111 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 01:58:45.145 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 01:58:45.397 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 01:58:45.405 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 01:58:45.406 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 01:58:45.407 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 01:58:45.407 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 01:58:45.407 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5978ba12]
2025-06-13 01:58:45.407 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5978ba12]
2025-06-13 01:58:45.407 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5978ba12]]
2025-06-13 01:58:45.407 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 01:58:45.407 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:58:45.407 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:58:45.419 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.117 seconds (JVM running for 5.807)
2025-06-13 01:58:49.551 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 01:58:49.552 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 01:58:49.552 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 01:58:49.552 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 01:58:49.552 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 01:58:49.553 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6cf53f3a
2025-06-13 01:58:49.554 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@71c343a0
2025-06-13 01:58:49.554 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 01:58:49.554 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-06-13 01:58:49.565 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 01:58:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 01:58:49.576 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 01:58:49.963 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 01:58:50.022 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 01:58:50.024 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b1e9265]
2025-06-13 01:58:50.029 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@17807975 wrapping com.mysql.cj.jdbc.ConnectionImpl@40355976] will be managed by Spring
2025-06-13 01:58:50.031 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 01:58:50.048 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 01:58:50.075 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 01:58:50.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b1e9265]
2025-06-13 01:58:50.077 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 01:58:50.077 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b1e9265]
2025-06-13 01:58:50.077 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b1e9265]
2025-06-13 01:58:50.077 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b1e9265]
2025-06-13 01:58:50.115 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 01:58:50.119 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 01:58:50.120 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 01:58:50.122 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 01:58:50.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 01:58:50.175 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 01:58:50.175 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c9dcc79]
2025-06-13 01:58:50.175 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1542285287 wrapping com.mysql.cj.jdbc.ConnectionImpl@40355976] will be managed by Spring
2025-06-13 01:58:50.175 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count, CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id LEFT JOIN video_comment_likes vcl ON vc.id = vcl.comment_id WHERE vc.video_episodes_id = ? GROUP BY vc.id ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 01:58:50.176 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getComments : ==> Parameters: 8(Long), 562(Long), 0(Integer), 10(Integer)
2025-06-13 01:58:50.196 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getComments : <==      Total: 10
2025-06-13 01:58:50.197 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c9dcc79]
2025-06-13 01:58:50.198 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c9dcc79]
2025-06-13 01:58:50.198 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c9dcc79]
2025-06-13 01:58:50.198 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c9dcc79]
2025-06-13 01:58:50.252 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 01:58:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 01:58:50.293 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 01:58:50.294 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 01:59:45.007 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 01:59:49.415 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 01:59:49.415 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 01:59:49.415 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 01:59:49.415 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5978ba12]]
2025-06-13 01:59:49.415 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5978ba12]
2025-06-13 01:59:49.415 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5978ba12]
2025-06-13 01:59:49.415 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 01:59:49.415 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:59:49.415 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 01:59:49.714 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 01:59:49.721 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 01:59:54.546 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 24676 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 01:59:54.546 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 01:59:54.549 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 01:59:54.549 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 01:59:55.599 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 01:59:55.602 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 01:59:55.676 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 0 Redis repository interfaces.
2025-06-13 01:59:55.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 01:59:55.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 01:59:55.867 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 01:59:55.869 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 01:59:55.870 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 01:59:55.870 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 01:59:55.870 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 01:59:55.870 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 01:59:55.871 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 01:59:55.871 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 01:59:55.871 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 01:59:55.871 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 01:59:55.871 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 01:59:55.871 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 01:59:55.872 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 01:59:55.872 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 01:59:55.872 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 01:59:55.872 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 01:59:55.873 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 01:59:55.873 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 01:59:55.873 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 01:59:55.873 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 01:59:55.874 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 01:59:55.874 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 01:59:55.874 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 01:59:55.874 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 01:59:55.874 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 01:59:55.874 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 01:59:55.875 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 01:59:55.875 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 01:59:55.875 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 01:59:56.498 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 01:59:56.504 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 01:59:56.505 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 01:59:56.506 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 01:59:56.609 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 01:59:56.609 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2015 ms
2025-06-13 01:59:56.864 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 01:59:56.890 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 01:59:56.906 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 01:59:56.919 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 01:59:56.933 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 01:59:56.942 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 01:59:56.953 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 01:59:56.964 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 01:59:56.975 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 01:59:56.988 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 01:59:57.003 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 01:59:57.014 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 01:59:57.022 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 01:59:57.032 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 01:59:57.051 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 01:59:57.476 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 01:59:58.192 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 01:59:58.192 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 01:59:58.460 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 01:59:58.462 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.557 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 01:59:58.557 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.558 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:59:58.558 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.558 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 01:59:58.558 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.559 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 01:59:58.560 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.561 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 01:59:58.561 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.561 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:59:58.561 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.566 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 01:59:58.567 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.570 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 01:59:58.570 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 01:59:58.696 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 01:59:58.697 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 01:59:58.703 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@3c2188f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@646bf8a6, org.springframework.security.web.context.SecurityContextPersistenceFilter@1f95158a, org.springframework.security.web.header.HeaderWriterFilter@27bf574b, org.springframework.security.web.authentication.logout.LogoutFilter@198c0f1c, com.example.pure.filter.JwtFilter@ba86c53, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@60d32006, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10820978, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a094f90, org.springframework.security.web.session.SessionManagementFilter@7253c53, org.springframework.security.web.access.ExceptionTranslationFilter@8dedec8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@25b8f9d2]
2025-06-13 01:59:58.706 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 01:59:58.708 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 01:59:58.942 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 01:59:58.965 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 01:59:59.026 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 01:59:59.040 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 01:59:59.347 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 01:59:59.453 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 01:59:59.476 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 01:59:59.476 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 01:59:59.476 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 01:59:59.476 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 01:59:59.476 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 01:59:59.477 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 01:59:59.478 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 01:59:59.479 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 01:59:59.479 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 01:59:59.479 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 01:59:59.479 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@360d3f47, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1693d505, org.springframework.security.web.context.SecurityContextPersistenceFilter@17063c32, org.springframework.security.web.header.HeaderWriterFilter@6d4c7248, org.springframework.web.filter.CorsFilter@3d195d15, org.springframework.security.web.authentication.logout.LogoutFilter@3369e2ce, com.example.pure.filter.JwtFilter@ba86c53, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@79c2bc34, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@77925f76, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@17b387ef, org.springframework.security.web.session.SessionManagementFilter@178ebac3, org.springframework.security.web.access.ExceptionTranslationFilter@c6c58ce, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@496c6d94]
2025-06-13 01:59:59.512 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@495083a0, started on Fri Jun 13 01:59:54 CST 2025
2025-06-13 01:59:59.522 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 01:59:59.523 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 01:59:59.526 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 01:59:59.527 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 01:59:59.527 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 01:59:59.527 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 01:59:59.527 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 01:59:59.527 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 01:59:59.527 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-06-13 01:59:59.528 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 01:59:59.529 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 01:59:59.529 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 01:59:59.529 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 01:59:59.695 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 01:59:59.735 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 02:00:00.039 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 02:00:00.049 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 02:00:00.051 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:00:00.051 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:00:00.051 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 02:00:00.051 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 02:00:00.052 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 02:00:00.052 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]]
2025-06-13 02:00:00.052 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 02:00:00.052 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:00:00.052 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:00:00.067 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 6.066 seconds (JVM running for 6.819)
2025-06-13 02:00:00.812 [34mINFO [0;39m [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 02:00:00.812 [34mINFO [0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 02:00:00.812 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 02:00:00.812 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 02:00:00.812 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 02:00:00.814 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2db739d9
2025-06-13 02:00:00.815 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@682394ae
2025-06-13 02:00:00.815 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 02:00:00.815 [34mINFO [0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-13 02:00:00.828 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:00:00.831 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 02:00:00.839 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:00:01.273 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 02:00:01.333 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:00:01.335 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12eae6e2]
2025-06-13 02:00:01.341 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1785159503 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a629344] will be managed by Spring
2025-06-13 02:00:01.342 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 02:00:01.360 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 02:00:01.386 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 02:00:01.387 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12eae6e2]
2025-06-13 02:00:01.388 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 02:00:01.388 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12eae6e2]
2025-06-13 02:00:01.388 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12eae6e2]
2025-06-13 02:00:01.388 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12eae6e2]
2025-06-13 02:00:01.422 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 02:00:01.426 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 02:00:01.427 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:00:01.429 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=10&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 02:00:01.431 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:00:01.479 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:00:01.479 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@167b19db]
2025-06-13 02:00:01.480 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@334516897 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a629344] will be managed by Spring
2025-06-13 02:00:01.480 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count, CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id LEFT JOIN video_comment_likes vcl ON vc.id = vcl.comment_id WHERE vc.video_episodes_id = ? GROUP BY vc.id ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 02:00:01.481 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : ==> Parameters: 8(Long), 562(Long), 0(Integer), 10(Integer)
2025-06-13 02:00:01.498 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : <==      Total: 10
2025-06-13 02:00:01.499 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@167b19db]
2025-06-13 02:00:01.499 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@167b19db]
2025-06-13 02:00:01.499 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@167b19db]
2025-06-13 02:00:01.499 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@167b19db]
2025-06-13 02:00:01.548 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 02:00:01.573 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 02:00:01.592 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 02:00:01.593 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 02:00:59.497 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 02:03:12.997 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:03:12.997 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 02:03:12.998 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:03:13.040 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 02:03:13.048 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:03:13.048 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@462cd39b]
2025-06-13 02:03:13.048 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1997950139 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a629344] will be managed by Spring
2025-06-13 02:03:13.048 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 02:03:13.049 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 02:03:13.061 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 02:03:13.062 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@462cd39b]
2025-06-13 02:03:13.062 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 02:03:13.062 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@462cd39b]
2025-06-13 02:03:13.062 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@462cd39b]
2025-06-13 02:03:13.062 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@462cd39b]
2025-06-13 02:03:13.099 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 02:03:13.099 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 02:03:13.100 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:03:13.100 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 02:03:13.101 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:03:13.124 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:03:13.124 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14206af0]
2025-06-13 02:03:13.124 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2142700414 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a629344] will be managed by Spring
2025-06-13 02:03:13.125 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count, CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id LEFT JOIN video_comment_likes vcl ON vc.id = vcl.comment_id WHERE vc.video_episodes_id = ? GROUP BY vc.id ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 02:03:13.125 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.getComments : ==> Parameters: 8(Long), 562(Long), 0(Integer), 13(Integer)
2025-06-13 02:03:13.141 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.getComments : <==      Total: 11
2025-06-13 02:03:13.141 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14206af0]
2025-06-13 02:03:13.142 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14206af0]
2025-06-13 02:03:13.142 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14206af0]
2025-06-13 02:03:13.142 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14206af0]
2025-06-13 02:03:13.175 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 02:03:13.176 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 02:03:13.179 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 02:03:13.179 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 02:30:59.506 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-13 02:37:30.350 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:37:30.350 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:37:30.351 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 02:37:30.351 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]]
2025-06-13 02:37:30.351 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 02:37:30.351 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 02:37:30.351 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 02:37:30.351 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:37:30.351 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:37:30.644 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 02:37:30.652 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 02:37:34.894 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 25952 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 02:37:34.894 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 02:37:34.898 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 02:37:34.898 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 02:37:35.772 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 02:37:35.773 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 02:37:35.804 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 02:37:35.889 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 02:37:35.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 02:37:35.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 02:37:35.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 02:37:35.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 02:37:35.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 02:37:35.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 02:37:35.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 02:37:35.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 02:37:35.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 02:37:35.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 02:37:35.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 02:37:35.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 02:37:35.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 02:37:35.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 02:37:35.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 02:37:36.395 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 02:37:36.400 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 02:37:36.400 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 02:37:36.401 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 02:37:36.486 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 02:37:36.487 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1546 ms
2025-06-13 02:37:36.700 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 02:37:36.710 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 02:37:36.716 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 02:37:36.723 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 02:37:36.733 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 02:37:36.738 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 02:37:36.744 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 02:37:36.750 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 02:37:36.755 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 02:37:36.763 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 02:37:36.773 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 02:37:36.779 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 02:37:36.783 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 02:37:36.787 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 02:37:36.798 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 02:37:37.149 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 02:37:37.679 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 02:37:37.679 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 02:37:37.929 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 02:37:37.931 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:37.998 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 02:37:38.000 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.001 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 02:37:38.001 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.001 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:37:38.001 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.001 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 02:37:38.002 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.002 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 02:37:38.002 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.002 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:37:38.002 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.006 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 02:37:38.006 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.007 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:37:38.007 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:37:38.096 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 02:37:38.097 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 02:37:38.101 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@6655f3cd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2484c868, org.springframework.security.web.context.SecurityContextPersistenceFilter@4cbb11e4, org.springframework.security.web.header.HeaderWriterFilter@6a013bdd, org.springframework.security.web.authentication.logout.LogoutFilter@6c2fea95, com.example.pure.filter.JwtFilter@2ae5580, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@647aa45c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3922b297, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19d13d04, org.springframework.security.web.session.SessionManagementFilter@8dedec8, org.springframework.security.web.access.ExceptionTranslationFilter@18c0f025, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@72641613]
2025-06-13 02:37:38.103 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 02:37:38.104 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 02:37:38.305 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 02:37:38.325 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 02:37:38.383 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 02:37:38.391 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 02:37:38.701 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 02:37:38.836 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 02:37:38.858 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 02:37:38.859 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 02:37:38.860 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 02:37:38.861 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 02:37:38.861 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@10df1f34, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6fe77a97, org.springframework.security.web.context.SecurityContextPersistenceFilter@5cc87de4, org.springframework.security.web.header.HeaderWriterFilter@5392aa2b, org.springframework.web.filter.CorsFilter@5cea47a1, org.springframework.security.web.authentication.logout.LogoutFilter@4693d24f, com.example.pure.filter.JwtFilter@2ae5580, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@706690e1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3663af34, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2d527346, org.springframework.security.web.session.SessionManagementFilter@174f0d06, org.springframework.security.web.access.ExceptionTranslationFilter@1a5a4f8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d7d968c]
2025-06-13 02:37:38.899 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@495083a0, started on Fri Jun 13 02:37:34 CST 2025
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 02:37:38.911 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 02:37:38.912 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 02:37:38.912 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 02:37:38.912 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 02:37:38.912 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 02:37:38.912 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 02:37:38.915 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 02:37:38.916 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 02:37:38.916 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 02:37:38.916 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 02:37:38.916 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 02:37:38.916 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 02:37:38.917 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-13 02:37:38.917 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 02:37:38.918 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 02:37:38.918 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 02:37:38.918 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 02:37:38.998 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 02:37:39.027 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 02:37:39.241 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 02:37:39.288 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 02:37:39.290 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:37:39.290 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:37:39.291 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 02:37:39.291 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45bbb3e8]
2025-06-13 02:37:39.291 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45bbb3e8]
2025-06-13 02:37:39.291 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45bbb3e8]]
2025-06-13 02:37:39.291 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 02:37:39.291 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:37:39.291 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:37:39.311 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 4.804 seconds (JVM running for 5.444)
2025-06-13 02:37:42.052 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 02:37:42.052 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 02:37:42.052 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 02:37:42.052 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 02:37:42.052 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 02:37:42.054 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6d6abd99
2025-06-13 02:37:42.054 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@1b3924f
2025-06-13 02:37:42.054 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 02:37:42.054 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-06-13 02:37:42.065 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:37:42.068 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 02:37:42.077 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:37:42.466 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 02:37:42.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:37:42.524 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10f29830]
2025-06-13 02:37:42.529 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@277257372 wrapping com.mysql.cj.jdbc.ConnectionImpl@7142f865] will be managed by Spring
2025-06-13 02:37:42.530 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 02:37:42.548 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 02:37:42.575 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 02:37:42.576 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10f29830]
2025-06-13 02:37:42.577 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 02:37:42.577 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10f29830]
2025-06-13 02:37:42.577 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10f29830]
2025-06-13 02:37:42.577 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10f29830]
2025-06-13 02:37:42.612 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 02:37:42.617 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 02:37:42.617 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:37:42.619 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 02:37:42.621 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:37:42.669 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:37:42.669 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76d1a7e6]
2025-06-13 02:37:42.669 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@609582020 wrapping com.mysql.cj.jdbc.ConnectionImpl@7142f865] will be managed by Spring
2025-06-13 02:37:42.669 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count, CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as like_status FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id LEFT JOIN video_comment_likes vcl ON vc.id = vcl.comment_id WHERE vc.video_episodes_id = ? GROUP BY vc.id ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 02:37:42.670 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getComments : ==> Parameters: 8(Long), 562(Long), 0(Integer), 13(Integer)
2025-06-13 02:37:42.689 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getComments : <==      Total: 11
2025-06-13 02:37:42.689 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76d1a7e6]
2025-06-13 02:37:42.690 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76d1a7e6]
2025-06-13 02:37:42.690 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76d1a7e6]
2025-06-13 02:37:42.690 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76d1a7e6]
2025-06-13 02:37:42.739 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 02:37:42.767 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 02:37:42.790 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 02:37:42.791 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 02:38:38.894 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 02:46:04.596 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:46:04.596 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:46:04.596 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 02:46:04.596 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45bbb3e8]]
2025-06-13 02:46:04.596 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45bbb3e8]
2025-06-13 02:46:04.596 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45bbb3e8]
2025-06-13 02:46:04.596 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 02:46:04.596 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:46:04.596 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:46:04.900 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 02:46:04.908 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 02:46:07.778 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 27900 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 02:46:07.778 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 02:46:07.781 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 02:46:07.782 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 02:46:08.625 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 02:46:08.626 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 02:46:08.657 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 02:46:08.757 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 02:46:08.758 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 02:46:08.758 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 02:46:08.758 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 02:46:08.758 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 02:46:08.759 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 02:46:08.760 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 02:46:08.761 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 02:46:08.762 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 02:46:08.762 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 02:46:08.762 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 02:46:08.762 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 02:46:08.762 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 02:46:08.762 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 02:46:08.763 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 02:46:08.763 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 02:46:08.763 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 02:46:08.763 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 02:46:08.763 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 02:46:09.232 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 02:46:09.236 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 02:46:09.237 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 02:46:09.237 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 02:46:09.322 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 02:46:09.323 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1502 ms
2025-06-13 02:46:09.545 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 02:46:09.555 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 02:46:09.560 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 02:46:09.566 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 02:46:09.572 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 02:46:09.576 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 02:46:09.582 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 02:46:09.587 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 02:46:09.592 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 02:46:09.599 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 02:46:09.611 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 02:46:09.616 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 02:46:09.621 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 02:46:09.626 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 02:46:09.636 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 02:46:10.005 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 02:46:10.559 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 02:46:10.560 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 02:46:10.843 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 02:46:10.846 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.917 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:46:10.917 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.918 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 02:46:10.918 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.918 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 02:46:10.918 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.919 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 02:46:10.920 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.921 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:46:10.921 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.921 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 02:46:10.921 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.925 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:46:10.925 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:10.925 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 02:46:10.925 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 02:46:11.018 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 02:46:11.020 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 02:46:11.024 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@1d432d56, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@147ad4bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@31cd0efa, org.springframework.security.web.header.HeaderWriterFilter@3b780b5c, org.springframework.security.web.authentication.logout.LogoutFilter@278c67a7, com.example.pure.filter.JwtFilter@79e16dd9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5a69b3cf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c460, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@bd703bb, org.springframework.security.web.session.SessionManagementFilter@5e0bba2f, org.springframework.security.web.access.ExceptionTranslationFilter@424ec990, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c538e62]
2025-06-13 02:46:11.026 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 02:46:11.029 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 02:46:11.231 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 02:46:11.248 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 02:46:11.300 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 02:46:11.308 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 02:46:11.598 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 02:46:11.697 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 02:46:11.716 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 02:46:11.716 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 02:46:11.716 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 02:46:11.716 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 02:46:11.717 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 02:46:11.718 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 02:46:11.719 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5cea47a1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2d527346, org.springframework.security.web.context.SecurityContextPersistenceFilter@39dce2df, org.springframework.security.web.header.HeaderWriterFilter@7698a3d9, org.springframework.web.filter.CorsFilter@451cef67, org.springframework.security.web.authentication.logout.LogoutFilter@57bfca3a, com.example.pure.filter.JwtFilter@79e16dd9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@662d3e85, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5598dff2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@11fb1691, org.springframework.security.web.session.SessionManagementFilter@4b62f1ba, org.springframework.security.web.access.ExceptionTranslationFilter@4902c584, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6fe77a97]
2025-06-13 02:46:11.751 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@495083a0, started on Fri Jun 13 02:46:07 CST 2025
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 02:46:11.762 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 02:46:11.763 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 02:46:11.763 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 02:46:11.763 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 02:46:11.763 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 02:46:11.763 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 02:46:11.765 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 02:46:11.765 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 02:46:11.765 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 02:46:11.765 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 02:46:11.765 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 02:46:11.765 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 02:46:11.766 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-13 02:46:11.766 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 02:46:11.767 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 02:46:11.767 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 02:46:11.767 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 02:46:11.839 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 02:46:11.871 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 02:46:12.113 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 02:46:12.121 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 02:46:12.123 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:46:12.123 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:46:12.123 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 02:46:12.123 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@520eb9f8]
2025-06-13 02:46:12.123 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@520eb9f8]
2025-06-13 02:46:12.123 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@520eb9f8]]
2025-06-13 02:46:12.124 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 02:46:12.124 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:46:12.124 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:46:12.136 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 4.85 seconds (JVM running for 5.64)
2025-06-13 02:46:14.795 [34mINFO [0;39m [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 02:46:14.795 [34mINFO [0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 02:46:14.795 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 02:46:14.795 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 02:46:14.795 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 02:46:14.797 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@522427cd
2025-06-13 02:46:14.798 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@69ebfc26
2025-06-13 02:46:14.798 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 02:46:14.798 [34mINFO [0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-13 02:46:14.811 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:46:14.814 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 02:46:14.822 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:46:15.233 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 02:46:15.293 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:46:15.296 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@618f11f1]
2025-06-13 02:46:15.301 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1891050591 wrapping com.mysql.cj.jdbc.ConnectionImpl@221e58f7] will be managed by Spring
2025-06-13 02:46:15.302 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 02:46:15.318 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 02:46:15.344 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 02:46:15.346 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@618f11f1]
2025-06-13 02:46:15.347 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 02:46:15.347 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@618f11f1]
2025-06-13 02:46:15.347 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@618f11f1]
2025-06-13 02:46:15.347 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@618f11f1]
2025-06-13 02:46:15.382 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 02:46:15.386 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 02:46:15.387 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:46:15.389 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 02:46:15.391 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:46:15.440 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:46:15.440 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e2ec709]
2025-06-13 02:46:15.440 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1398707907 wrapping com.mysql.cj.jdbc.ConnectionImpl@221e58f7] will be managed by Spring
2025-06-13 02:46:15.440 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count, CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as like_status MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN vcl.type END) AS userLikeType FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id LEFT JOIN video_comment_likes vcl ON vc.id = vcl.comment_id WHERE vc.video_episodes_id = ? GROUP BY vc.id ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 02:46:15.441 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : ==> Parameters: 8(Long), 8(Long), 562(Long), 0(Integer), 13(Integer)
2025-06-13 02:46:15.473 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e2ec709]
2025-06-13 02:46:15.505 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e2ec709]
2025-06-13 02:46:15.505 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e2ec709]
2025-06-13 02:46:15.540 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-13 02:46:15.542 [1;31mERROR[0;39m [http-nio-8080-exec-2] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6
### The error may exist in file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time,                up.username,up.avatar,             COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count,             COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count,             CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as like_status             MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN vcl.type END) AS userLikeType          FROM video_comments vc                 INNER JOIN user_profile up ON vc.user_id = up.id                 LEFT  JOIN  video_comment_likes vcl ON vc.id = vcl.comment_id         WHERE vc.video_episodes_id = ?             GROUP BY                 vc.id         ORDER BY vc.created_time DESC         LIMIT ?, ?
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy110.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy150.getComments(Unknown Source)
	at com.example.pure.service.impl.VideoCommentInteractionServiceImpl.getComments(VideoCommentInteractionServiceImpl.java:97)
	at com.example.pure.service.impl.VideoCommentInteractionServiceImpl$$FastClassBySpringCGLIB$$5cc61c57.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.VideoCommentInteractionServiceImpl$$EnhancerBySpringCGLIB$$ef1ae121.getComments(<generated>)
	at com.example.pure.controller.VideoCommentInteractionController.getCommentsByVideoEpisodesId(VideoCommentInteractionController.java:49)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy197.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 125 common frames omitted
2025-06-13 02:46:15.558 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 02:46:15.565 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-12T18:46:15.545 (truncated)...]
2025-06-13 02:46:15.577 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.jdbc.BadSqlGrammarException: <EOL><EOL>### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6<EOL><EOL>### The error may exist in file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]<EOL><EOL>### The error may involve defaultParameterMap<EOL><EOL>### The error occurred while setting parameters<EOL><EOL>### SQL: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time,                up.username,up.avatar,             COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count,             COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count,             CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as like_status             MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN vcl.type END) AS userLikeType          FROM video_comments vc                 INNER JOIN user_profile up ON vc.user_id = up.id                 LEFT  JOIN  video_comment_likes vcl ON vc.id = vcl.comment_id         WHERE vc.video_episodes_id = ?             GROUP BY                 vc.id         ORDER BY vc.created_time DESC         LIMIT ?, ?<EOL><EOL>### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6<EOL>; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'MAX(CASE WHEN vcl.user_id = 8 AND vcl.status = true THEN vcl.type END) AS userLi' at line 6]
2025-06-13 02:46:15.577 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-13 02:46:15.578 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 02:47:11.747 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 02:48:10.789 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:48:10.789 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:48:10.789 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 02:48:10.789 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@520eb9f8]]
2025-06-13 02:48:10.789 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@520eb9f8]
2025-06-13 02:48:10.789 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@520eb9f8]
2025-06-13 02:48:10.789 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 02:48:10.789 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:48:10.789 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:48:11.095 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 02:48:11.103 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 02:48:13.814 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 25076 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 02:48:13.814 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 02:48:13.816 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 02:48:13.817 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 02:48:14.889 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 02:48:14.892 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 02:48:14.931 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 02:48:15.097 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 02:48:15.099 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 02:48:15.101 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 02:48:15.101 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 02:48:15.102 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 02:48:15.102 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 02:48:15.102 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 02:48:15.102 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 02:48:15.102 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 02:48:15.102 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 02:48:15.103 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 02:48:15.103 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 02:48:15.103 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 02:48:15.103 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 02:48:15.103 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 02:48:15.103 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 02:48:15.104 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 02:48:15.104 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 02:48:15.104 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 02:48:15.104 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 02:48:15.104 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 02:48:15.104 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 02:48:15.105 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 02:48:15.105 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 02:48:15.105 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 02:48:15.105 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 02:48:15.105 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 02:48:15.105 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 02:48:15.106 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 02:48:15.777 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 02:48:15.782 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 02:48:15.783 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 02:48:15.783 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 02:48:15.881 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 02:48:15.882 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2029 ms
2025-06-13 02:48:16.198 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 02:48:16.214 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 02:48:16.221 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 02:48:16.229 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 02:48:16.239 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 02:48:16.245 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 02:48:16.254 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 02:48:16.259 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 02:48:16.269 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 02:48:16.281 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 02:48:16.298 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 02:48:16.306 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 02:48:16.315 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 02:48:16.321 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 02:48:16.338 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 02:48:16.783 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 02:48:17.396 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 02:48:17.397 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 02:48:17.687 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 02:48:17.689 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.756 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:48:17.757 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.758 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 02:48:17.758 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.758 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 02:48:17.759 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.759 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 02:48:17.759 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.760 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 02:48:17.760 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.760 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:48:17.760 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.763 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 02:48:17.764 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.764 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 02:48:17.764 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 02:48:17.855 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 02:48:17.856 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 02:48:17.862 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@20e3b165, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34d3bfca, org.springframework.security.web.context.SecurityContextPersistenceFilter@226e95e9, org.springframework.security.web.header.HeaderWriterFilter@18c0f025, org.springframework.security.web.authentication.logout.LogoutFilter@619854a3, com.example.pure.filter.JwtFilter@533d7c61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4cbb11e4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@326dbe25, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6655f3cd, org.springframework.security.web.session.SessionManagementFilter@1e836aed, org.springframework.security.web.access.ExceptionTranslationFilter@15a2fddd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5e66733b]
2025-06-13 02:48:17.864 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 02:48:17.865 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 02:48:18.049 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 02:48:18.066 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 02:48:18.115 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 02:48:18.123 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 02:48:18.467 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 02:48:18.605 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 02:48:18.628 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 02:48:18.629 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 02:48:18.630 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 02:48:18.631 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1f229c40, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@215404a1, org.springframework.security.web.context.SecurityContextPersistenceFilter@5392aa2b, org.springframework.security.web.header.HeaderWriterFilter@237d0625, org.springframework.web.filter.CorsFilter@391b03b, org.springframework.security.web.authentication.logout.LogoutFilter@3663af34, com.example.pure.filter.JwtFilter@533d7c61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@174f0d06, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5cc87de4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a977146, org.springframework.security.web.session.SessionManagementFilter@1a5a4f8, org.springframework.security.web.access.ExceptionTranslationFilter@337eeceb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@11fb1691]
2025-06-13 02:48:18.662 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@495083a0, started on Fri Jun 13 02:48:13 CST 2025
2025-06-13 02:48:18.673 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 02:48:18.673 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 02:48:18.674 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 02:48:18.677 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 02:48:18.677 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 02:48:18.677 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 02:48:18.677 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 02:48:18.677 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 02:48:18.678 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 02:48:18.678 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-06-13 02:48:18.679 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 02:48:18.679 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 02:48:18.679 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 02:48:18.679 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 02:48:18.827 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 02:48:18.871 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 02:48:19.151 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 02:48:19.163 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 02:48:19.166 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 02:48:19.166 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 02:48:19.166 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 02:48:19.166 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 02:48:19.166 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 02:48:19.166 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]]
2025-06-13 02:48:19.167 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 02:48:19.167 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:48:19.167 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 02:48:19.184 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.83 seconds (JVM running for 6.556)
2025-06-13 02:48:21.546 [34mINFO [0;39m [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 02:48:21.546 [34mINFO [0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 02:48:21.546 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 02:48:21.546 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 02:48:21.546 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 02:48:21.550 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@24b2752e
2025-06-13 02:48:21.551 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@47245fb6
2025-06-13 02:48:21.551 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 02:48:21.551 [34mINFO [0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 5 ms
2025-06-13 02:48:21.567 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:48:21.571 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 02:48:21.582 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:48:21.987 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 02:48:22.048 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:48:22.050 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d3799a7]
2025-06-13 02:48:22.055 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1308705651 wrapping com.mysql.cj.jdbc.ConnectionImpl@32aec250] will be managed by Spring
2025-06-13 02:48:22.057 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 02:48:22.072 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 02:48:22.098 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 02:48:22.099 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d3799a7]
2025-06-13 02:48:22.100 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 02:48:22.100 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d3799a7]
2025-06-13 02:48:22.100 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d3799a7]
2025-06-13 02:48:22.101 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d3799a7]
2025-06-13 02:48:22.135 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 02:48:22.141 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 02:48:22.141 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 02:48:22.144 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 02:48:22.147 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 02:48:22.202 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 02:48:22.202 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77ec6867]
2025-06-13 02:48:22.202 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1510353911 wrapping com.mysql.cj.jdbc.ConnectionImpl@32aec250] will be managed by Spring
2025-06-13 02:48:22.202 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id,vc.user_id,vc.parent_comment_id,vc.content,vc.created_time,vc.updated_time, up.username,up.avatar, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='like' THEN vcl.id END) AS likes_count, COUNT(DISTINCT CASE WHEN vcl.status = true AND vcl.type ='dislike' THEN vcl.id END) AS dislikes_count, CAST(IFNULL(MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as like_status, MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN vcl.type END) AS userLikeType FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id LEFT JOIN video_comment_likes vcl ON vc.id = vcl.comment_id WHERE vc.video_episodes_id = ? GROUP BY vc.id ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 02:48:22.204 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : ==> Parameters: 8(Long), 8(Long), 562(Long), 0(Integer), 13(Integer)
2025-06-13 02:48:22.225 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.V.getComments : <==      Total: 11
2025-06-13 02:48:22.225 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77ec6867]
2025-06-13 02:48:22.227 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77ec6867]
2025-06-13 02:48:22.227 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77ec6867]
2025-06-13 02:48:22.227 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77ec6867]
2025-06-13 02:48:22.280 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 02:48:22.321 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 02:48:22.338 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 02:48:22.340 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 02:49:18.658 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 03:19:18.673 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-13 03:49:14.900 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 03:49:14.900 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 03:49:14.900 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 03:49:14.901 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]]
2025-06-13 03:49:14.901 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 03:49:14.901 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@13e5c53c]
2025-06-13 03:49:14.901 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 03:49:14.901 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 03:49:14.901 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 03:49:15.303 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 03:49:15.309 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-13 03:49:20.535 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 21048 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-13 03:49:20.534 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-13 03:49:20.538 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-13 03:49:20.538 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-13 03:49:21.725 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-13 03:49:21.726 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-13 03:49:21.757 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-13 03:49:21.849 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-13 03:49:21.850 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-13 03:49:21.852 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-13 03:49:21.852 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-13 03:49:21.852 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-13 03:49:21.852 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-13 03:49:21.852 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-13 03:49:21.853 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-13 03:49:21.854 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-13 03:49:21.854 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-13 03:49:21.854 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-13 03:49:21.854 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-13 03:49:21.854 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-13 03:49:21.854 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-13 03:49:21.855 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-13 03:49:21.855 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-13 03:49:21.855 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-13 03:49:21.855 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-13 03:49:21.855 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-13 03:49:21.856 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-13 03:49:21.856 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-13 03:49:21.856 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-13 03:49:21.856 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-13 03:49:22.367 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-13 03:49:22.372 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-13 03:49:22.373 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 03:49:22.374 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-13 03:49:22.469 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-13 03:49:22.469 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1886 ms
2025-06-13 03:49:22.687 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-13 03:49:22.697 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-13 03:49:22.703 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-13 03:49:22.708 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-13 03:49:22.715 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-13 03:49:22.719 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-13 03:49:22.726 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-13 03:49:22.730 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-13 03:49:22.737 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-13 03:49:22.743 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-13 03:49:22.753 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-13 03:49:22.758 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-13 03:49:22.764 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-13 03:49:22.770 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-13 03:49:22.781 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-13 03:49:23.167 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-13 03:49:23.687 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-13 03:49:23.687 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-13 03:49:23.978 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-13 03:49:23.980 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.046 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-13 03:49:24.046 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.047 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-13 03:49:24.047 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.047 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-13 03:49:24.049 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.049 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 03:49:24.049 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.049 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-13 03:49:24.050 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.050 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-13 03:49:24.050 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.053 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-13 03:49:24.054 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.054 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-13 03:49:24.054 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-13 03:49:24.147 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-13 03:49:24.148 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-13 03:49:24.153 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@30d5fc1b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7957aa57, org.springframework.security.web.context.SecurityContextPersistenceFilter@3922b297, org.springframework.security.web.header.HeaderWriterFilter@52f118aa, org.springframework.security.web.authentication.logout.LogoutFilter@7352418c, com.example.pure.filter.JwtFilter@48f4264e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@72c1bf75, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@43a8bd35, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b92ea9d, org.springframework.security.web.session.SessionManagementFilter@4cbb11e4, org.springframework.security.web.access.ExceptionTranslationFilter@5e0bba2f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@34d3bfca]
2025-06-13 03:49:24.154 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-13 03:49:24.156 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-13 03:49:24.347 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-13 03:49:24.365 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-13 03:49:24.418 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-13 03:49:24.426 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-13 03:49:24.745 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-13 03:49:24.859 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-13 03:49:24.878 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-13 03:49:24.878 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-13 03:49:24.878 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-13 03:49:24.878 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-13 03:49:24.878 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-13 03:49:24.879 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-13 03:49:24.880 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-13 03:49:24.881 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@466cca41, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c3ae369, org.springframework.security.web.context.SecurityContextPersistenceFilter@59aba3c3, org.springframework.security.web.header.HeaderWriterFilter@706690e1, org.springframework.web.filter.CorsFilter@496c6d94, org.springframework.security.web.authentication.logout.LogoutFilter@2d36add1, com.example.pure.filter.JwtFilter@48f4264e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4693d24f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@236a3e4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@360d3f47, org.springframework.security.web.session.SessionManagementFilter@3663af34, org.springframework.security.web.access.ExceptionTranslationFilter@5cc87de4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@67730dab]
2025-06-13 03:49:24.913 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@495083a0, started on Fri Jun 13 03:49:20 CST 2025
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-13 03:49:24.924 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-13 03:49:24.925 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-13 03:49:24.927 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
2025-06-13 03:49:24.928 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-13 03:49:24.929 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-13 03:49:24.929 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-13 03:49:24.929 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-13 03:49:25.016 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-13 03:49:25.043 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-13 03:49:25.222 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-13 03:49:25.230 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 03:49:25.232 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 03:49:25.232 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 03:49:25.232 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-13 03:49:25.232 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@48dc9950]
2025-06-13 03:49:25.232 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@48dc9950]
2025-06-13 03:49:25.232 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@48dc9950]]
2025-06-13 03:49:25.232 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-13 03:49:25.232 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 03:49:25.232 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 03:49:25.244 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.184 seconds (JVM running for 5.875)
2025-06-13 03:49:40.308 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 03:49:40.308 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-13 03:49:40.308 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-13 03:49:40.308 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-13 03:49:40.308 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-13 03:49:40.310 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@69ccb1d4
2025-06-13 03:49:40.311 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@1e154301
2025-06-13 03:49:40.311 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-13 03:49:40.311 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-13 03:49:40.322 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 03:49:40.325 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-13 03:49:40.335 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 03:49:40.776 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-13 03:49:40.793 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 03:49:40.797 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.802 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1348420103 wrapping com.mysql.cj.jdbc.ConnectionImpl@28fcc0fe] will be managed by Spring
2025-06-13 03:49:40.804 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-06-13 03:49:40.823 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: hao111(String)
2025-06-13 03:49:40.856 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-06-13 03:49:40.857 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.862 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782] from current transaction
2025-06-13 03:49:40.862 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : ==>  Preparing: SELECT * FROM user WHERE id = ?
2025-06-13 03:49:40.862 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : ==> Parameters: 8(Long)
2025-06-13 03:49:40.878 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : <==      Total: 1
2025-06-13 03:49:40.878 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.878 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782] from current transaction
2025-06-13 03:49:40.879 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-13 03:49:40.879 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-13 03:49:40.893 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-13 03:49:40.893 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.894 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-13 03:49:40.895 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.895 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.895 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c68782]
2025-06-13 03:49:40.990 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-13 03:49:40.995 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=] with attributes [authenticated]
2025-06-13 03:49:40.995 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=
2025-06-13 03:49:40.997 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/?pageNum=1&pageSize=13&videoEpisodesId=562&orderBy=createdTime&orderDirection=desc&offset=", parameters={masked}
2025-06-13 03:49:40.999 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-13 03:49:41.056 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-13 03:49:41.056 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e]
2025-06-13 03:49:41.056 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@36190196 wrapping com.mysql.cj.jdbc.ConnectionImpl@28fcc0fe] will be managed by Spring
2025-06-13 03:49:41.056 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getBasicComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id, vc.user_id, vc.parent_comment_id, vc.content, vc.created_time, vc.updated_time, up.username, up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-13 03:49:41.057 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getBasicComments : ==> Parameters: 562(Long), 0(Integer), 13(Integer)
2025-06-13 03:49:41.075 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getBasicComments : <==      Total: 11
2025-06-13 03:49:41.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e]
2025-06-13 03:49:41.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e] from current transaction
2025-06-13 03:49:41.088 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getCommentsLikeData : ==>  Preparing: SELECT vcl.comment_id, COUNT(CASE WHEN vcl.type = 'like' AND vcl.status = true THEN 1 END) as likes_count, COUNT(CASE WHEN vcl.type = 'dislike' AND vcl.status = true THEN 1 END) as dislikes_count, MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN vcl.type END) as user_like_type FROM video_comment_likes vcl WHERE vcl.comment_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) GROUP BY vcl.comment_id
2025-06-13 03:49:41.089 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getCommentsLikeData : ==> Parameters: 8(Long), 29(Long), 28(Long), 15(Long), 14(Long), 13(Long), 12(Long), 11(Long), 10(Long), 9(Long), 7(Long), 1(Long)
2025-06-13 03:49:41.103 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.V.getCommentsLikeData : <==      Total: 2
2025-06-13 03:49:41.103 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e]
2025-06-13 03:49:41.105 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e]
2025-06-13 03:49:41.105 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e]
2025-06-13 03:49:41.105 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b79e04e]
2025-06-13 03:49:41.151 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-13 03:49:41.173 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-13 03:49:41.187 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-13 03:49:41.188 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-13 03:50:24.905 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-13 03:57:49.099 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-13 03:57:49.099 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-13 03:57:49.099 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-13 03:57:49.099 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@48dc9950]]
2025-06-13 03:57:49.099 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@48dc9950]
2025-06-13 03:57:49.099 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@48dc9950]
2025-06-13 03:57:49.099 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-13 03:57:49.099 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 03:57:49.099 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-13 03:57:49.549 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-13 03:57:49.559 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
