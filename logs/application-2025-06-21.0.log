2025-06-21 00:04:21.623 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-21 00:34:21.627 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-Http<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-06-21 01:04:21.641 [34mINFO [0;39m [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-06-21 01:15:06.392 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-21 01:15:06.393 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-21 01:15:06.393 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-21 01:15:06.393 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33b4c775]]
2025-06-21 01:15:06.393 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33b4c775]
2025-06-21 01:15:06.393 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33b4c775]
2025-06-21 01:15:06.393 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-21 01:15:06.393 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 01:15:06.393 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 01:15:08.836 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-21 01:15:08.843 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-21 18:49:19.225 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 11724 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-21 18:49:19.225 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-21 18:49:19.229 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-21 18:49:19.229 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-21 18:49:20.733 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 18:49:20.734 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 18:49:20.769 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-21 18:49:20.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-21 18:49:20.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-21 18:49:20.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-21 18:49:20.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-21 18:49:20.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-21 18:49:20.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-21 18:49:20.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-21 18:49:20.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-21 18:49:20.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-21 18:49:20.898 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-21 18:49:20.898 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-21 18:49:20.898 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-21 18:49:20.898 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-21 18:49:20.898 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-21 18:49:20.898 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-21 18:49:20.899 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-21 18:49:20.899 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-21 18:49:20.899 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-21 18:49:20.899 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-21 18:49:20.900 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-21 18:49:20.900 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-21 18:49:20.900 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-21 18:49:20.900 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-21 18:49:20.900 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-21 18:49:22.603 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-21 18:49:22.610 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-21 18:49:22.611 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-21 18:49:22.611 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-21 18:49:22.806 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-21 18:49:22.806 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3525 ms
2025-06-21 18:49:23.242 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-21 18:49:23.260 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-21 18:49:23.269 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-21 18:49:23.280 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-21 18:49:23.291 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-21 18:49:23.297 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-21 18:49:23.305 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-21 18:49:23.313 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-21 18:49:23.322 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-21 18:49:23.333 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-21 18:49:23.351 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-21 18:49:23.359 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-21 18:49:23.366 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-21 18:49:23.372 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-21 18:49:23.388 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-21 18:49:23.938 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-21 18:49:24.754 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-21 18:49:24.755 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-21 18:49:25.102 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-21 18:49:25.105 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.176 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-21 18:49:25.176 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.177 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-21 18:49:25.178 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.178 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-21 18:49:25.180 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.180 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-21 18:49:25.180 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.180 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-21 18:49:25.181 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.181 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-21 18:49:25.181 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.185 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-21 18:49:25.185 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.186 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-21 18:49:25.186 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-21 18:49:25.297 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-21 18:49:25.299 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-21 18:49:25.304 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@7901a5ab, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7f9d40b3, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d216ee8, org.springframework.security.web.header.HeaderWriterFilter@201b52f7, org.springframework.security.web.authentication.logout.LogoutFilter@eb6ccbc, com.example.pure.filter.JwtFilter@1ab21633, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@268e30d4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c50709a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@ca2a03f, org.springframework.security.web.session.SessionManagementFilter@110318a7, org.springframework.security.web.access.ExceptionTranslationFilter@36c45149, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2fc0ad21]
2025-06-21 18:49:25.306 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-21 18:49:25.310 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-21 18:49:25.508 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-21 18:49:25.541 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-21 18:49:25.622 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-21 18:49:25.633 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-21 18:49:26.064 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-21 18:49:26.203 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-21 18:49:26.225 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-21 18:49:26.226 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-21 18:49:26.227 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-21 18:49:26.228 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@dd3e1e3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2686a801, org.springframework.security.web.context.SecurityContextPersistenceFilter@40d04cf8, org.springframework.security.web.header.HeaderWriterFilter@5d67bf4d, org.springframework.web.filter.CorsFilter@7878459f, org.springframework.security.web.authentication.logout.LogoutFilter@12478b4e, com.example.pure.filter.JwtFilter@1ab21633, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@75507e68, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@152d2a58, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f6a5cc9, org.springframework.security.web.session.SessionManagementFilter@31de8099, org.springframework.security.web.access.ExceptionTranslationFilter@5c0d3715, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@643a73fa]
2025-06-21 18:49:26.272 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7674a051, started on Sat Jun 21 18:49:19 CST 2025
2025-06-21 18:49:26.287 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-21 18:49:26.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-21 18:49:26.292 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-21 18:49:26.293 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-21 18:49:26.293 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-21 18:49:26.293 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-21 18:49:26.293 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-21 18:49:26.293 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-21 18:49:26.293 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
2025-06-21 18:49:26.294 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-21 18:49:26.295 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-21 18:49:26.295 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-21 18:49:26.295 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-21 18:49:26.376 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-21 18:49:26.404 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-21 18:49:26.640 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-21 18:49:26.661 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-21 18:49:26.665 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-21 18:49:26.666 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-21 18:49:26.666 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-21 18:49:26.666 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ef812b]
2025-06-21 18:49:26.666 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ef812b]
2025-06-21 18:49:26.667 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ef812b]]
2025-06-21 18:49:26.667 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-21 18:49:26.667 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 18:49:26.667 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 18:49:26.686 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 7.995 seconds (JVM running for 9.207)
2025-06-21 18:49:26.812 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 18:49:26.813 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-21 18:49:26.813 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-21 18:49:26.813 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-21 18:49:26.813 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-21 18:49:26.816 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@b8e7e61
2025-06-21 18:49:26.817 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@42499eb8
2025-06-21 18:49:26.818 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-21 18:49:26.818 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 5 ms
2025-06-21 18:49:26.839 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:49:26.844 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:49:26.857 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:49:27.771 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:49:27.795 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:27.799 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806]
2025-06-21 18:49:27.805 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@926344883 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:49:27.807 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-21 18:49:27.827 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: hao111(String)
2025-06-21 18:49:27.865 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-21 18:49:27.876 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806]
2025-06-21 18:49:27.878 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:49:27.878 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806] from current transaction
2025-06-21 18:49:27.879 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:49:27.879 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:49:27.895 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:49:27.895 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806]
2025-06-21 18:49:27.897 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:49:27.897 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806]
2025-06-21 18:49:27.898 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806]
2025-06-21 18:49:27.898 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aaad806]
2025-06-21 18:49:28.061 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:49:28.068 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-21 18:49:28.068 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:49:28.071 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-21 18:49:28.074 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:49:28.099 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:49:31.106 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-21 18:49:31.120 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-21 18:49:31.125 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-21 18:49:31.160 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-21 18:49:31.162 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:49:53.819 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:49:53.819 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:49:53.820 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:49:53.942 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:49:53.963 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:49:53.963 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:53.963 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ed5e962]
2025-06-21 18:49:53.963 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2082940808 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:49:53.963 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:49:53.963 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:49:53.978 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:49:53.979 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ed5e962]
2025-06-21 18:49:53.979 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:49:53.979 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ed5e962]
2025-06-21 18:49:53.979 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ed5e962]
2025-06-21 18:49:53.979 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ed5e962]
2025-06-21 18:49:54.020 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:49:54.021 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-21 18:49:54.021 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:49:54.021 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-21 18:49:54.022 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:49:54.023 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:49:54.023 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-21 18:49:54.024 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-21 18:49:54.024 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-21 18:49:54.027 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-21 18:49:54.027 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:49:54.136 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:49:54.136 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:49:54.136 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:49:54.137 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:49:54.137 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:49:54.137 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:49:54.257 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:49:54.274 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:49:54.275 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:54.275 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccb2c1f]
2025-06-21 18:49:54.275 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1620004931 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:49:54.275 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:49:54.275 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:49:54.288 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:49:54.291 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:49:54.291 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:49:54.291 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:54.291 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52c0e2b0]
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@332825080 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccb2c1f]
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccb2c1f]
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccb2c1f]
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccb2c1f]
2025-06-21 18:49:54.292 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:49:54.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:49:54.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52c0e2b0]
2025-06-21 18:49:54.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:49:54.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52c0e2b0]
2025-06-21 18:49:54.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52c0e2b0]
2025-06-21 18:49:54.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52c0e2b0]
2025-06-21 18:49:54.335 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:49:54.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:49:54.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:49:54.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:49:54.337 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:49:54.344 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:54.345 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e25dfc7] was not registered for synchronization because synchronization is not active
2025-06-21 18:49:54.357 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:49:54.358 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:49:54.358 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:49:54.358 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:49:54.359 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:49:54.361 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:54.361 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@906109885 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:49:54.361 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f828a94] was not registered for synchronization because synchronization is not active
2025-06-21 18:49:54.361 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:49:54.361 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@809762156 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:49:54.362 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:49:54.362 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 8(Long)
2025-06-21 18:49:54.362 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 8(Long)
2025-06-21 18:49:54.379 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:49:54.379 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f828a94]
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e25dfc7]
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e866407] was not registered for synchronization because synchronization is not active
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@495464444 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:49:54.380 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 8(Long), 10(Integer), 0(Integer)
2025-06-21 18:49:54.386 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:49:54.395 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:49:54.396 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e866407]
2025-06-21 18:49:54.396 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:49:54.396 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:49:54.396 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e4245f] was not registered for synchronization because synchronization is not active
2025-06-21 18:49:54.397 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1603263576 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:49:54.397 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:49:54.397 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 8(Long)
2025-06-21 18:49:54.407 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:49:54.407 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:49:54.414 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:49:54.414 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e4245f]
2025-06-21 18:49:54.416 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:49:54.422 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:49:54.430 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:49:54.430 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:50:26.256 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-21 18:50:39.646 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/auth/refresh
2025-06-21 18:50:39.646 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:50:39.646 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:50:39.771 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:50:39.773 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:50:39.773 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:39.773 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3744d90f]
2025-06-21 18:50:39.774 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@55988267 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:39.774 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:50:39.774 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:50:39.789 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:50:39.790 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3744d90f]
2025-06-21 18:50:39.790 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:50:39.790 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3744d90f]
2025-06-21 18:50:39.790 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3744d90f]
2025-06-21 18:50:39.790 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3744d90f]
2025-06-21 18:50:39.832 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:50:39.833 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/refresh] with attributes [permitAll]
2025-06-21 18:50:39.833 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/auth/refresh
2025-06-21 18:50:39.833 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/api/auth/refresh", parameters={}
2025-06-21 18:50:39.833 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:50:39.900 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.pure.controller.AuthController : 刷新令牌
2025-06-21 18:50:39.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.service.impl.AuthServiceImpl : 处理令牌刷新请求
2025-06-21 18:50:39.931 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:50:39.934 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:50:39.934 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:39.934 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17f5ad6c]
2025-06-21 18:50:39.934 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@345722866 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:39.935 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:50:39.935 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:50:39.949 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:50:39.949 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17f5ad6c]
2025-06-21 18:50:39.949 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:50:39.950 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17f5ad6c]
2025-06-21 18:50:39.950 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17f5ad6c]
2025-06-21 18:50:39.950 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17f5ad6c]
2025-06-21 18:50:40.006 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.AuthServiceImpl : 用户令牌创建成功: hao111
2025-06-21 18:50:40.300 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.DeviceServiceImpl : 通过refreshToken移除设备成功: Key - user:devices:refreshToken:hao111
2025-06-21 18:50:40.310 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:hao111, DeviceId - 86a1e032-bb04-46a7-87bc-5f9d98791e30
2025-06-21 18:50:40.318 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:hao111, DeviceId - 855009d6-34b0-43fd-b360-43a0416629bb
2025-06-21 18:50:40.318 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.AuthServiceImpl : 用户 hao111 刷新令牌后，Access Token 设备数超限，已移除最早的设备: $2a$10$YIP3sKaRXqAnxfMy3vggTOQb2tH0GiBT/KjQ.gjywVrZaXuaA4R9u
2025-06-21 18:50:40.323 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:hao111, DeviceId - ee280b6c-0fe1-4426-8809-7d30e8153e91
2025-06-21 18:50:40.358 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 8
2025-06-21 18:50:40.358 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:40.358 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32ff9ee1]
2025-06-21 18:50:40.360 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:40.360 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-06-21 18:50:40.361 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.primary.UserMapper.update : ==> Parameters: hao111(String), $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-21 18:50:40.3428721(Timestamp), $2a$10$7qU3tKtHu4TAHvewiwgXxet26zZGf0RyuYj8d3xkW9RxatJnbbOZ2(String), 2025-07-05 18:50:40.0(Timestamp), 8(Long)
2025-06-21 18:50:40.397 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-21 18:50:40.397 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32ff9ee1]
2025-06-21 18:50:40.399 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 8
2025-06-21 18:50:40.399 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32ff9ee1]
2025-06-21 18:50:40.399 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32ff9ee1]
2025-06-21 18:50:40.399 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32ff9ee1]
2025-06-21 18:50:40.436 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.AuthServiceImpl : 令牌刷新成功: hao111
2025-06-21 18:50:40.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:40.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d354727] was not registered for synchronization because synchronization is not active
2025-06-21 18:50:40.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1445109730 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:50:40.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-06-21 18:50:40.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: hao111(String)
2025-06-21 18:50:40.470 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-06-21 18:50:40.470 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d354727]
2025-06-21 18:50:40.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:40.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4f7af125]
2025-06-21 18:50:40.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@375504250 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:40.499 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:50:40.499 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 8(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户hao111执行了刷新令牌(String), 2025-06-21T18:50:40.482408(LocalDateTime)
2025-06-21 18:50:40.529 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:50:40.534 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4f7af125]
2025-06-21 18:50:40.534 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=8, summary=用户hao111执行了刷新令牌
2025-06-21 18:50:40.534 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4f7af125]
2025-06-21 18:50:40.534 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4f7af125]
2025-06-21 18:50:40.534 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4f7af125]
2025-06-21 18:50:40.567 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:50:40.574 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=令牌刷新成功, success=true, data=TokenResponse(username=hao111, accessToken=eyJhb (truncated)...]
2025-06-21 18:50:40.578 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:50:40.578 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:50:40.713 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/user/UserWithUserProfile
2025-06-21 18:50:40.713 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:50:40.714 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:50:40.821 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:50:40.823 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:50:40.823 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:40.823 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6d5d31]
2025-06-21 18:50:40.823 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@759240780 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:40.824 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:50:40.824 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:50:40.838 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:50:40.838 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6d5d31]
2025-06-21 18:50:40.838 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:50:40.838 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6d5d31]
2025-06-21 18:50:40.838 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6d5d31]
2025-06-21 18:50:40.838 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6d5d31]
2025-06-21 18:50:40.880 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:50:40.881 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user/UserWithUserProfile] with attributes [authenticated]
2025-06-21 18:50:40.881 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/user/UserWithUserProfile
2025-06-21 18:50:40.881 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/user/UserWithUserProfile", parameters={}
2025-06-21 18:50:40.881 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:50:40.886 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.UserController] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:50:40.927 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.service.impl.UserServiceImpl : 获取用户DTO, username: hao111
2025-06-21 18:50:40.929 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:40.929 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc]
2025-06-21 18:50:40.929 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@713804408 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:40.929 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : ==>  Preparing: SELECT u.id AS u_id, u.username AS u_username, u.created_time AS u_created_time, u.updated_time AS u_updated_time, up.email AS up_email, up.nickname AS up_nickname, up.avatar AS up_avatar, up.phone AS up_phone, up.description AS up_description FROM user u LEFT JOIN user_profile up ON u.id = up.id WHERE u.username = ?
2025-06-21 18:50:40.929 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : ==> Parameters: hao111(String)
2025-06-21 18:50:40.946 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : <==      Total: 1
2025-06-21 18:50:40.946 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc]
2025-06-21 18:50:40.947 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc] from current transaction
2025-06-21 18:50:40.947 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findRolesByUserId : ==>  Preparing: SELECT r.id, r.name, r.created_time, r.updated_time FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:50:40.947 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findRolesByUserId : ==> Parameters: 8(Long)
2025-06-21 18:50:40.962 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findRolesByUserId : <==      Total: 1
2025-06-21 18:50:40.962 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc]
2025-06-21 18:50:40.962 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc]
2025-06-21 18:50:40.962 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc]
2025-06-21 18:50:40.962 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@104f3fbc]
2025-06-21 18:50:41.013 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.pure.controller.UserController : 获取用户信息：hao111
2025-06-21 18:50:41.032 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:41.032 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.032 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@370973915 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:41.032 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-21 18:50:41.032 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 8(Long), GET_USER_WITH_USER_PROFILE(String), 2025-06-21(String)
2025-06-21 18:50:41.048 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-06-21 18:50:41.048 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.050 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d] from current transaction
2025-06-21 18:50:41.050 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-21 18:50:41.050 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 8(Long), GET_USER_WITH_USER_PROFILE(String), 2025-06-20(String)
2025-06-21 18:50:41.065 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 1
2025-06-21 18:50:41.066 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.066 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d] from current transaction
2025-06-21 18:50:41.066 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.AccessLogMapper.insert : ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, created_time, updated_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:50:41.067 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.AccessLogMapper.insert : ==> Parameters: 8(Long), GET_USER_WITH_USER_PROFILE(String), 2(Integer), 2025-06-21(LocalDate), 2025-06-21T18:50:41.066515900(LocalDateTime), 2025-06-21T18:50:41.066515900(LocalDateTime), 127.0.0.1(String)
2025-06-21 18:50:41.103 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.AccessLogMapper.insert : <==    Updates: 1
2025-06-21 18:50:41.104 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.104 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.impl.AccessLogServiceImpl : Created new access log for user: 8, type: GET_USER_WITH_USER_PROFILE, count: 2
2025-06-21 18:50:41.104 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.104 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.104 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b24b67d]
2025-06-21 18:50:41.182 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:50:41.183 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6328970a]
2025-06-21 18:50:41.183 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1997395269 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:50:41.183 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:50:41.195 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 8(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户hao111执行了操作的名称还未定义(String), 2025-06-21T18:50:41.162520800(LocalDateTime)
2025-06-21 18:50:41.225 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:50:41.235 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6328970a]
2025-06-21 18:50:41.235 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=8, summary=用户hao111执行了操作的名称还未定义
2025-06-21 18:50:41.236 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6328970a]
2025-06-21 18:50:41.236 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6328970a]
2025-06-21 18:50:41.236 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6328970a]
2025-06-21 18:50:41.327 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:50:41.444 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=查询用户名获取全部资料成功, success=true, data=UserWithUserProfileDTO(id=8, username=hao (truncated)...]
2025-06-21 18:50:41.506 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:50:41.506 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:52:12.480 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:52:12.480 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:52:12.480 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:52:12.480 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:52:12.481 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:52:12.481 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:52:12.619 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:52:12.619 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:52:12.621 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:52:12.621 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1896f5b6]
2025-06-21 18:52:12.622 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1473257019 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a02ce3]
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1178893043 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:12.622 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:12.637 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:52:12.638 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a02ce3]
2025-06-21 18:52:12.638 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:52:12.638 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a02ce3]
2025-06-21 18:52:12.638 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a02ce3]
2025-06-21 18:52:12.638 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a02ce3]
2025-06-21 18:52:12.642 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:52:12.642 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1896f5b6]
2025-06-21 18:52:12.643 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:52:12.643 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1896f5b6]
2025-06-21 18:52:12.643 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1896f5b6]
2025-06-21 18:52:12.643 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1896f5b6]
2025-06-21 18:52:12.681 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:52:12.682 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:52:12.682 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:52:12.682 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:52:12.682 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:52:12.682 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:12.683 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@238e8df9] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:12.683 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@15777727 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:52:12.683 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:52:12.683 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:12.704 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:52:12.704 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:52:12.704 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:52:12.704 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:52:12.704 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@238e8df9]
2025-06-21 18:52:12.704 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:52:12.705 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:52:12.705 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:52:12.705 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:52:12.706 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:12.706 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@582ce1f3] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:12.706 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1175947471 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:52:12.706 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:52:12.706 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 8(Long)
2025-06-21 18:52:12.706 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:52:12.707 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:52:12.723 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:52:12.724 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@582ce1f3]
2025-06-21 18:52:12.724 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:12.724 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2265eb82] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:12.724 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@502904132 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:52:12.724 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:52:12.725 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 8(Long), 10(Integer), 0(Integer)
2025-06-21 18:52:12.743 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:52:12.743 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2265eb82]
2025-06-21 18:52:12.743 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:12.743 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d16b239] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:12.743 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1881309154 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:52:12.743 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:52:12.744 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:12.760 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:52:12.761 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d16b239]
2025-06-21 18:52:12.761 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:52:12.762 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:52:12.763 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:52:12.763 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:52:29.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:52:29.128 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:52:29.128 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:52:29.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:52:29.128 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:52:29.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:52:29.139 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:52:29.139 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:52:29.140 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:52:29.245 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:52:29.247 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:52:29.247 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.247 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6cc994f9]
2025-06-21 18:52:29.247 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@876864971 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:52:29.247 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:52:29.247 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:29.254 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:52:29.255 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:52:29.255 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.255 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c7f5257]
2025-06-21 18:52:29.255 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@428896691 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:52:29.256 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:52:29.256 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:29.257 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:52:29.258 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:52:29.258 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.258 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b9ef1df]
2025-06-21 18:52:29.258 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@285074908 wrapping com.mysql.cj.jdbc.ConnectionImpl@67ead261] will be managed by Spring
2025-06-21 18:52:29.258 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:52:29.258 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:29.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:52:29.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6cc994f9]
2025-06-21 18:52:29.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:52:29.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6cc994f9]
2025-06-21 18:52:29.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6cc994f9]
2025-06-21 18:52:29.262 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6cc994f9]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b9ef1df]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c7f5257]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b9ef1df]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c7f5257]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b9ef1df]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c7f5257]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b9ef1df]
2025-06-21 18:52:29.271 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c7f5257]
2025-06-21 18:52:29.302 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:52:29.302 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:52:29.302 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:52:29.302 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:52:29.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:52:29.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@363e4ee3] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:29.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1461018884 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:52:29.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:52:29.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:29.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:52:29.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-21 18:52:29.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:52:29.307 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-21 18:52:29.307 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:52:29.307 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:52:29.308 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-21 18:52:29.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-21 18:52:29.309 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-21 18:52:29.310 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-21 18:52:29.310 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@363e4ee3]
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:52:29.317 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a5d8f09] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1225147855 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 8(Long)
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:52:29.318 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:52:29.334 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:52:29.334 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a5d8f09]
2025-06-21 18:52:29.334 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.334 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bb66004] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:29.335 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1491912065 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:52:29.335 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:52:29.335 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 8(Long), 10(Integer), 0(Integer)
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bb66004]
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@710bbf1b] was not registered for synchronization because synchronization is not active
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1136662913 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:52:29.350 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 8(Long)
2025-06-21 18:52:29.366 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:52:29.367 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@710bbf1b]
2025-06-21 18:52:29.367 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:52:29.367 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:52:29.368 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:52:29.368 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:56:52.513 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:56:52.513 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:56:52.513 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:56:52.513 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:56:52.513 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:56:52.513 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:56:52.528 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:56:52.528 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:56:52.529 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:56:52.635 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:56:52.637 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:56:52.637 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.637 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b4e083e]
2025-06-21 18:56:52.637 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1420560946 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:56:52.637 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:56:52.638 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:56:52.650 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:56:52.651 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:56:52.651 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b4e083e]
2025-06-21 18:56:52.651 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:56:52.651 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b4e083e]
2025-06-21 18:56:52.651 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b4e083e]
2025-06-21 18:56:52.651 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b4e083e]
2025-06-21 18:56:52.652 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:56:52.652 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.652 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efb526a]
2025-06-21 18:56:52.653 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@45878013 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:56:52.653 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:56:52.653 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:56:52.657 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-21 18:56:52.660 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 8,password: $2a$10$iEe1CiR4tvcIqTExW3ZqfegIReojjASMH07BOz.B4KobXpZ1Z3Wda
2025-06-21 18:56:52.660 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.660 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31410d2b]
2025-06-21 18:56:52.660 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1060933487 wrapping com.mysql.cj.jdbc.ConnectionImpl@67ead261] will be managed by Spring
2025-06-21 18:56:52.660 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:56:52.660 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-21 18:56:52.669 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:56:52.669 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efb526a]
2025-06-21 18:56:52.669 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:56:52.669 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efb526a]
2025-06-21 18:56:52.669 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efb526a]
2025-06-21 18:56:52.669 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efb526a]
2025-06-21 18:56:52.674 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:56:52.675 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31410d2b]
2025-06-21 18:56:52.675 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-21 18:56:52.675 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31410d2b]
2025-06-21 18:56:52.675 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31410d2b]
2025-06-21 18:56:52.675 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31410d2b]
2025-06-21 18:56:52.693 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:56:52.693 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:56:52.693 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:56:52.694 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:56:52.694 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:56:52.694 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.694 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a6ab19c] was not registered for synchronization because synchronization is not active
2025-06-21 18:56:52.694 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1268538277 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:56:52.694 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:56:52.695 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 8(Long)
2025-06-21 18:56:52.708 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:56:52.708 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a6ab19c]
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:56:52.709 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:56:52.710 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:56:52.710 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:56:52.710 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:56:52.710 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-21 18:56:52.711 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-21 18:56:52.711 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-21 18:56:52.713 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-21 18:56:52.713 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:56:52.715 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-21 18:56:52.715 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:56:52.715 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:56:52.715 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:56:52.716 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:56:52.716 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.716 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@da780e8] was not registered for synchronization because synchronization is not active
2025-06-21 18:56:52.717 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1777532262 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:56:52.717 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:56:52.717 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 8(Long)
2025-06-21 18:56:52.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:56:52.734 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@da780e8]
2025-06-21 18:56:52.734 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.734 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f24e0b] was not registered for synchronization because synchronization is not active
2025-06-21 18:56:52.734 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1914003048 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:56:52.734 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:56:52.734 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 8(Long), 10(Integer), 0(Integer)
2025-06-21 18:56:52.751 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:56:52.751 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f24e0b]
2025-06-21 18:56:52.751 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:56:52.752 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4856657f] was not registered for synchronization because synchronization is not active
2025-06-21 18:56:52.752 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1130358888 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will not be managed by Spring
2025-06-21 18:56:52.752 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:56:52.752 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 8(Long)
2025-06-21 18:56:52.768 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:56:52.769 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4856657f]
2025-06-21 18:56:52.770 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:56:52.770 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:56:52.770 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:56:52.771 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:10.265 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/github
2025-06-21 18:57:10.265 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:10.266 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-06-21 18:57:10.266 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 18:57:10.266 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/github] with attributes [permitAll]
2025-06-21 18:57:10.267 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/github
2025-06-21 18:57:10.267 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/github", parameters={}
2025-06-21 18:57:10.268 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-06-21 18:57:10.287 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://github.com/login/oauth/authorize?response_type=code&client_id=Ov23liowOuqP1FEcOBXl&redirect_uri=http://localhost:8080/oauth/callback/github&state=97008bd17a129b836bd9ef4b88555f38&scope=
2025-06-21 18:57:10.287 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-06-21 18:57:10.288 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:10.881 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/github?code=ade605d4b54f3db6f0cf&state=97008bd17a129b836bd9ef4b88555f38
2025-06-21 18:57:10.881 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:10.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-06-21 18:57:10.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 18:57:10.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/github?code=ade605d4b54f3db6f0cf&state=97008bd17a129b836bd9ef4b88555f38] with attributes [permitAll]
2025-06-21 18:57:10.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/github?code=ade605d4b54f3db6f0cf&state=97008bd17a129b836bd9ef4b88555f38
2025-06-21 18:57:10.883 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/github?code=ade605d4b54f3db6f0cf&state=97008bd17a129b836bd9ef4b88555f38", parameters={masked}
2025-06-21 18:57:10.883 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-06-21 18:57:10.888 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.pure.controller.OAuth2Controller : 收到github的回调: me.zhyd.oauth.model.AuthCallback@6cda7234
2025-06-21 18:57:12.244 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@f1fb8c4
2025-06-21 18:57:12.275 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.OAuth2ServiceImpl : Processing OAuth2 user: Haohao268826
2025-06-21 18:57:12.276 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:12.276 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.277 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@964543898 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:57:12.277 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-06-21 18:57:12.277 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: Haohao268826(String)
2025-06-21 18:57:12.292 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-06-21 18:57:12.292 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.292 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02] from current transaction
2025-06-21 18:57:12.292 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.findOAuth2InfoByUserIdAndProvider : ==>  Preparing: SELECT * FROM o_auth2 WHERE user_id = ? AND provider = ?
2025-06-21 18:57:12.292 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.findOAuth2InfoByUserIdAndProvider : ==> Parameters: 23(Long), GITHUB(String)
2025-06-21 18:57:12.309 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.findOAuth2InfoByUserIdAndProvider : <==      Total: 1
2025-06-21 18:57:12.309 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.309 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02] from current transaction
2025-06-21 18:57:12.310 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.updateOAuth2Info : ==>  Preparing: UPDATE o_auth2 SET access_token = ?, access_token_expire_in = ?, refresh_token = ?, refresh_token_expire_in = ?, updated_time = NOW() WHERE user_id = ? AND provider = ?
2025-06-21 18:57:12.310 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.updateOAuth2Info : ==> Parameters: ****************************************(String), 0(Integer), null, 0(Integer), 23(Long), GITHUB(String)
2025-06-21 18:57:12.342 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.updateOAuth2Info : <==    Updates: 1
2025-06-21 18:57:12.342 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.344 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02] from current transaction
2025-06-21 18:57:12.344 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserWithPasswordByUserId : ==>  Preparing: SELECT * FROM user WHERE id = ?
2025-06-21 18:57:12.344 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserWithPasswordByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:12.360 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserWithPasswordByUserId : <==      Total: 1
2025-06-21 18:57:12.361 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.514 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 77d423fd-772e-4928-abb2-5e7a4b628f09
2025-06-21 18:57:12.520 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - 0e6d7457-2721-4053-a054-2dd2f0508429
2025-06-21 18:57:12.520 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.OAuth2ServiceImpl : 用户 Haohao268826 的 Access Token 设备数超限，已移除最早的设备: $2a$10$lzs6sySAeSVXo9p6TFYPzeD4mCHuU8uIOCRySQevmjEFxNoqh2N2u
2025-06-21 18:57:12.523 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:refreshToken:Haohao268826, DeviceId - 504e93c1-db91-42e1-87a0-600c0b47a409
2025-06-21 18:57:12.528 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - 0e6d7457-2721-4053-a054-2dd2f0508429
2025-06-21 18:57:12.528 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.OAuth2ServiceImpl : 用户 Haohao268826 的 Refresh Token 设备数超限，已移除最早的设备: $2a$10$9F4Lx0PC.SdJmo8vlGz/u.kTFPxQYWd1x47FRSL1IdOFihnMZfPmG
2025-06-21 18:57:12.528 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-06-21 18:57:12.528 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02] from current transaction
2025-06-21 18:57:12.529 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-06-21 18:57:12.529 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-21 18:57:12.5288271(Timestamp), $2a$10$Omh37UJEGnBf8Vwh4s7XIecThHvxdybza6N5CYtDr7qK2/.nkctOa(String), 2025-07-05 18:57:12.0(Timestamp), 23(Long)
2025-06-21 18:57:12.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-21 18:57:12.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-06-21 18:57:12.559 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.service.impl.OAuth2ServiceImpl : Successfully processed OAuth2 user: Haohao268826
2025-06-21 18:57:12.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c16b02]
2025-06-21 18:57:12.600 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-06-21 18:57:12.600 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:14.082 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:57:14.082 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:14.083 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:57:14.209 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:14.210 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.210 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf]
2025-06-21 18:57:14.210 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@947952397 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:57:14.210 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-21 18:57:14.210 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-21 18:57:14.229 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-21 18:57:14.230 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf]
2025-06-21 18:57:14.230 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:14.230 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf] from current transaction
2025-06-21 18:57:14.230 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:14.230 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:14.246 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:14.246 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf]
2025-06-21 18:57:14.247 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:14.247 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf]
2025-06-21 18:57:14.247 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf]
2025-06-21 18:57:14.247 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba60bbf]
2025-06-21 18:57:14.281 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:57:14.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:57:14.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:14.281 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:14.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:57:14.281 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:57:14.296 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:14.297 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-21 18:57:14.297 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:57:14.297 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-21 18:57:14.299 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-21 18:57:14.300 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-21 18:57:14.300 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-21 18:57:14.301 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-21 18:57:14.301 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [InputStream resource [resource loaded through InputStream] (range: 0-220760)]
2025-06-21 18:57:14.304 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-21 18:57:14.304 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:14.424 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:14.427 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:14.427 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.427 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@125cba5e]
2025-06-21 18:57:14.427 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:14.427 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@661009827 wrapping com.mysql.cj.jdbc.ConnectionImpl@67ead261] will be managed by Spring
2025-06-21 18:57:14.427 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:14.428 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:14.430 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:14.430 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.430 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@752b90e8]
2025-06-21 18:57:14.430 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1636080329 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:57:14.430 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:14.430 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:14.441 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:14.442 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@125cba5e]
2025-06-21 18:57:14.442 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:14.442 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@125cba5e]
2025-06-21 18:57:14.442 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@125cba5e]
2025-06-21 18:57:14.442 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@125cba5e]
2025-06-21 18:57:14.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:14.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@752b90e8]
2025-06-21 18:57:14.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:14.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@752b90e8]
2025-06-21 18:57:14.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@752b90e8]
2025-06-21 18:57:14.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@752b90e8]
2025-06-21 18:57:14.482 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:14.483 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:57:14.483 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:57:14.483 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:57:14.484 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:57:14.484 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.484 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8c1c8ba] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:14.484 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@9263251 wrapping com.mysql.cj.jdbc.ConnectionImpl@67ead261] will not be managed by Spring
2025-06-21 18:57:14.484 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:57:14.485 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:14.488 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:14.489 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:57:14.489 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:57:14.489 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:57:14.489 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:57:14.490 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.490 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a1c7986] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:14.490 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1980915444 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:57:14.490 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:57:14.491 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-21 18:57:14.498 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:57:14.499 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8c1c8ba]
2025-06-21 18:57:14.499 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:14.499 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:57:14.500 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:14.501 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:14.506 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:57:14.506 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a1c7986]
2025-06-21 18:57:14.506 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.506 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68790e6f] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:14.506 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@837851823 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:57:14.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:57:14.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-21 18:57:14.521 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:57:14.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68790e6f]
2025-06-21 18:57:14.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:14.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@486821b0] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:14.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1757566116 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will not be managed by Spring
2025-06-21 18:57:14.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:57:14.523 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:14.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:57:14.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@486821b0]
2025-06-21 18:57:14.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:14.539 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:57:14.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:14.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:26.349 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing POST /api/auth/refresh
2025-06-21 18:57:26.349 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:26.349 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:57:26.470 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:26.471 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:26.472 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:26.472 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522bd9b3]
2025-06-21 18:57:26.472 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2062766067 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:57:26.472 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:26.472 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:26.488 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:26.489 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522bd9b3]
2025-06-21 18:57:26.489 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:26.489 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522bd9b3]
2025-06-21 18:57:26.489 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522bd9b3]
2025-06-21 18:57:26.489 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522bd9b3]
2025-06-21 18:57:26.535 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:26.535 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/refresh] with attributes [permitAll]
2025-06-21 18:57:26.535 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured POST /api/auth/refresh
2025-06-21 18:57:26.536 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : POST "/api/auth/refresh", parameters={}
2025-06-21 18:57:26.536 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:57:26.540 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.pure.controller.AuthController : 刷新令牌
2025-06-21 18:57:26.540 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 处理令牌刷新请求
2025-06-21 18:57:26.575 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:26.577 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:26.577 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:26.577 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@226754a2]
2025-06-21 18:57:26.577 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@659924783 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:57:26.577 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:26.577 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:26.594 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:26.594 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@226754a2]
2025-06-21 18:57:26.595 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:26.595 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@226754a2]
2025-06-21 18:57:26.595 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@226754a2]
2025-06-21 18:57:26.595 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@226754a2]
2025-06-21 18:57:26.647 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 用户令牌创建成功: Haohao268826
2025-06-21 18:57:26.864 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 通过refreshToken移除设备成功: Key - user:devices:refreshToken:Haohao268826
2025-06-21 18:57:26.866 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 58a4ae82-8453-49ca-b33f-dd3b35f36a85
2025-06-21 18:57:26.869 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - d2184166-204e-45e7-b156-81f653374c85
2025-06-21 18:57:26.869 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 用户 Haohao268826 刷新令牌后，Access Token 设备数超限，已移除最早的设备: $2a$10$qEgGPW48B5z8nrSwvSZSo.OjU01KwUk.wfqZqKI90vryvu7M6C.a2
2025-06-21 18:57:26.876 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - 9c11efe2-5b01-4218-9f0d-ba0688aaf7d5
2025-06-21 18:57:26.893 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-06-21 18:57:26.893 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:26.893 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e947060]
2025-06-21 18:57:26.894 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:57:26.894 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-06-21 18:57:26.894 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-21 18:57:26.877459(Timestamp), $2a$10$RefXsj30gRcips9nyr9e5.BuNsO.rlVk78EQPP28yDCMAwhgJN4hq(String), 2025-07-05 18:57:26.0(Timestamp), 23(Long)
2025-06-21 18:57:26.927 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-21 18:57:26.927 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e947060]
2025-06-21 18:57:26.927 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-06-21 18:57:26.927 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e947060]
2025-06-21 18:57:26.927 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e947060]
2025-06-21 18:57:26.927 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e947060]
2025-06-21 18:57:26.964 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 令牌刷新成功: Haohao268826
2025-06-21 18:57:26.980 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:26.980 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6048bce2]
2025-06-21 18:57:26.980 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2116373749 wrapping com.mysql.cj.jdbc.ConnectionImpl@167e0fd1] will be managed by Spring
2025-06-21 18:57:26.980 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:57:26.980 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了刷新令牌(String), 2025-06-21T18:57:26.966385600(LocalDateTime)
2025-06-21 18:57:27.012 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:57:27.012 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6048bce2]
2025-06-21 18:57:27.012 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了刷新令牌
2025-06-21 18:57:27.012 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6048bce2]
2025-06-21 18:57:27.012 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6048bce2]
2025-06-21 18:57:27.012 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6048bce2]
2025-06-21 18:57:27.045 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:27.046 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=令牌刷新成功, success=true, data=TokenResponse(username=Haohao268826, accessToken (truncated)...]
2025-06-21 18:57:27.047 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:27.047 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:27.155 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/user/UserWithUserProfile
2025-06-21 18:57:27.155 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:27.156 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:57:27.275 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:27.277 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34772529]
2025-06-21 18:57:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1029391256 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:57:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:27.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:27.292 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34772529]
2025-06-21 18:57:27.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:27.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34772529]
2025-06-21 18:57:27.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34772529]
2025-06-21 18:57:27.293 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34772529]
2025-06-21 18:57:27.335 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:27.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user/UserWithUserProfile] with attributes [authenticated]
2025-06-21 18:57:27.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/user/UserWithUserProfile
2025-06-21 18:57:27.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/user/UserWithUserProfile", parameters={}
2025-06-21 18:57:27.336 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:57:27.337 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.UserController] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:27.370 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.service.impl.UserServiceImpl : 获取用户DTO, username: Haohao268826
2025-06-21 18:57:27.371 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:27.371 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05]
2025-06-21 18:57:27.371 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1847692506 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:57:27.371 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : ==>  Preparing: SELECT u.id AS u_id, u.username AS u_username, u.created_time AS u_created_time, u.updated_time AS u_updated_time, up.email AS up_email, up.nickname AS up_nickname, up.avatar AS up_avatar, up.phone AS up_phone, up.description AS up_description FROM user u LEFT JOIN user_profile up ON u.id = up.id WHERE u.username = ?
2025-06-21 18:57:27.372 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : ==> Parameters: Haohao268826(String)
2025-06-21 18:57:27.387 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : <==      Total: 1
2025-06-21 18:57:27.387 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05]
2025-06-21 18:57:27.387 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05] from current transaction
2025-06-21 18:57:27.387 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findRolesByUserId : ==>  Preparing: SELECT r.id, r.name, r.created_time, r.updated_time FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:27.387 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findRolesByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:27.403 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findRolesByUserId : <==      Total: 1
2025-06-21 18:57:27.403 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05]
2025-06-21 18:57:27.404 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05]
2025-06-21 18:57:27.404 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05]
2025-06-21 18:57:27.404 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f3c4a05]
2025-06-21 18:57:27.447 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.pure.controller.UserController : 获取用户信息：Haohao268826
2025-06-21 18:57:27.463 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:27.463 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.463 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1927717228 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:57:27.463 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-21 18:57:27.463 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2025-06-21(String)
2025-06-21 18:57:27.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-06-21 18:57:27.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.480 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12] from current transaction
2025-06-21 18:57:27.480 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-21 18:57:27.481 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2025-06-20(String)
2025-06-21 18:57:27.496 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 1
2025-06-21 18:57:27.496 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.497 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12] from current transaction
2025-06-21 18:57:27.497 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.AccessLogMapper.insert : ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, created_time, updated_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:57:27.497 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.AccessLogMapper.insert : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2(Integer), 2025-06-21(LocalDate), 2025-06-21T18:57:27.496359900(LocalDateTime), 2025-06-21T18:57:27.497359600(LocalDateTime), 127.0.0.1(String)
2025-06-21 18:57:27.526 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.AccessLogMapper.insert : <==    Updates: 1
2025-06-21 18:57:27.527 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.527 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.impl.AccessLogServiceImpl : Created new access log for user: 23, type: GET_USER_WITH_USER_PROFILE, count: 2
2025-06-21 18:57:27.527 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.527 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.527 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@172a7a12]
2025-06-21 18:57:27.576 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:27.576 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66ea6267]
2025-06-21 18:57:27.576 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1881595376 wrapping com.mysql.cj.jdbc.ConnectionImpl@7783ad53] will be managed by Spring
2025-06-21 18:57:27.577 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:57:27.577 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了操作的名称还未定义(String), 2025-06-21T18:57:27.562043300(LocalDateTime)
2025-06-21 18:57:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:57:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66ea6267]
2025-06-21 18:57:27.606 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了操作的名称还未定义
2025-06-21 18:57:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66ea6267]
2025-06-21 18:57:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66ea6267]
2025-06-21 18:57:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66ea6267]
2025-06-21 18:57:27.637 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:27.637 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=查询用户名获取全部资料成功, success=true, data=UserWithUserProfileDTO(id=23, username=Ha (truncated)...]
2025-06-21 18:57:27.638 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:27.638 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:32.382 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-21 18:57:32.382 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-21 18:57:32.382 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-21 18:57:32.382 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ef812b]]
2025-06-21 18:57:32.382 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ef812b]
2025-06-21 18:57:32.382 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2ef812b]
2025-06-21 18:57:32.382 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-21 18:57:32.382 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 18:57:32.382 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 18:57:32.864 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-21 18:57:32.881 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-21 18:57:37.973 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 12488 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-21 18:57:37.975 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-21 18:57:37.976 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-21 18:57:37.977 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-21 18:57:39.224 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 18:57:39.226 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 18:57:39.259 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-21 18:57:39.361 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-21 18:57:39.362 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-21 18:57:39.363 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-21 18:57:39.363 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-21 18:57:39.363 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-21 18:57:39.363 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-21 18:57:39.364 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-21 18:57:39.364 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-21 18:57:39.364 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-21 18:57:39.364 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-21 18:57:39.364 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-21 18:57:39.365 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-21 18:57:39.366 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-21 18:57:39.366 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-21 18:57:39.366 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-21 18:57:39.366 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-21 18:57:39.366 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-21 18:57:39.366 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-21 18:57:39.367 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-21 18:57:39.367 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-21 18:57:39.367 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-21 18:57:39.367 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-21 18:57:39.367 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-21 18:57:39.975 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-21 18:57:39.983 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-21 18:57:39.984 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-21 18:57:39.984 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-21 18:57:40.144 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-21 18:57:40.144 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2128 ms
2025-06-21 18:57:40.476 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-21 18:57:40.488 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-21 18:57:40.495 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-21 18:57:40.502 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-21 18:57:40.512 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-21 18:57:40.518 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-21 18:57:40.525 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-21 18:57:40.532 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-21 18:57:40.541 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-21 18:57:40.549 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-21 18:57:40.562 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-21 18:57:40.569 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-21 18:57:40.575 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-21 18:57:40.581 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-21 18:57:40.595 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-21 18:57:41.019 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-21 18:57:41.674 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-21 18:57:41.675 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-21 18:57:41.968 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-21 18:57:41.971 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.053 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-21 18:57:42.053 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.054 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-21 18:57:42.056 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.056 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-21 18:57:42.056 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.057 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-21 18:57:42.057 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.057 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-21 18:57:42.057 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.057 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-21 18:57:42.057 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.061 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-21 18:57:42.061 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.061 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-21 18:57:42.061 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:42.179 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-21 18:57:42.181 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-21 18:57:42.187 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@37ab1b10, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55d87b73, org.springframework.security.web.context.SecurityContextPersistenceFilter@6761f75b, org.springframework.security.web.header.HeaderWriterFilter@46c9ee28, org.springframework.security.web.authentication.logout.LogoutFilter@62c42a3, com.example.pure.filter.JwtFilter@423762ae, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c7443f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@74e175c7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@618e7761, org.springframework.security.web.session.SessionManagementFilter@862624f, org.springframework.security.web.access.ExceptionTranslationFilter@63896cf7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@637c8632]
2025-06-21 18:57:42.190 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-21 18:57:42.194 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-21 18:57:42.390 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-21 18:57:42.419 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-21 18:57:42.497 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-21 18:57:42.506 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-21 18:57:42.955 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-21 18:57:43.100 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-21 18:57:43.122 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-21 18:57:43.123 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-21 18:57:43.124 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-21 18:57:43.125 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-21 18:57:43.125 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-21 18:57:43.125 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@755033c5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@229c4d34, org.springframework.security.web.context.SecurityContextPersistenceFilter@70f3bf00, org.springframework.security.web.header.HeaderWriterFilter@53f1fcc2, org.springframework.web.filter.CorsFilter@5b49b1df, org.springframework.security.web.authentication.logout.LogoutFilter@22ea6051, com.example.pure.filter.JwtFilter@423762ae, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@49770ef9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15bcecf9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@689eab53, org.springframework.security.web.session.SessionManagementFilter@6a261998, org.springframework.security.web.access.ExceptionTranslationFilter@4c6eaa65, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@54cce500]
2025-06-21 18:57:43.168 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3c1e3314, started on Sat Jun 21 18:57:38 CST 2025
2025-06-21 18:57:43.184 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-21 18:57:43.184 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-21 18:57:43.184 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-21 18:57:43.184 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-21 18:57:43.184 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-21 18:57:43.184 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-21 18:57:43.185 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-21 18:57:43.188 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-21 18:57:43.189 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-21 18:57:43.189 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-21 18:57:43.189 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-21 18:57:43.190 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-21 18:57:43.190 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-21 18:57:43.190 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-06-21 18:57:43.191 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-21 18:57:43.191 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-21 18:57:43.191 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-21 18:57:43.191 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-21 18:57:43.376 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-21 18:57:43.444 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-21 18:57:43.741 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-21 18:57:43.751 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-21 18:57:43.753 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-21 18:57:43.753 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-21 18:57:43.753 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-21 18:57:43.753 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d94c50e]
2025-06-21 18:57:43.753 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d94c50e]
2025-06-21 18:57:43.753 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5d94c50e]]
2025-06-21 18:57:43.754 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-21 18:57:43.754 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 18:57:43.754 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-21 18:57:43.767 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 6.195 seconds (JVM running for 6.95)
2025-06-21 18:57:44.773 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 18:57:44.773 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-21 18:57:44.774 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-21 18:57:44.774 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-21 18:57:44.774 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-21 18:57:44.776 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@3b2a12ac
2025-06-21 18:57:44.776 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@23d5b6d7
2025-06-21 18:57:44.776 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-21 18:57:44.776 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-21 18:57:44.789 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/auth/refresh
2025-06-21 18:57:44.792 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:44.801 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:57:45.375 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:45.420 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:45.425 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:45.428 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@333fb66f]
2025-06-21 18:57:45.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@220940782 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:45.435 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:45.450 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:45.475 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:45.476 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@333fb66f]
2025-06-21 18:57:45.477 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:45.477 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@333fb66f]
2025-06-21 18:57:45.477 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@333fb66f]
2025-06-21 18:57:45.477 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@333fb66f]
2025-06-21 18:57:45.512 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:45.516 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/refresh] with attributes [permitAll]
2025-06-21 18:57:45.516 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/auth/refresh
2025-06-21 18:57:45.518 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/auth/refresh", parameters={}
2025-06-21 18:57:45.520 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:57:45.579 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.pure.controller.AuthController : 刷新令牌
2025-06-21 18:57:45.579 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 处理令牌刷新请求
2025-06-21 18:57:45.604 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:45.607 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:45.607 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:45.607 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65532fbc]
2025-06-21 18:57:45.608 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1841798264 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:45.608 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:45.608 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:45.621 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:45.621 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65532fbc]
2025-06-21 18:57:45.621 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:45.622 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65532fbc]
2025-06-21 18:57:45.622 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65532fbc]
2025-06-21 18:57:45.622 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65532fbc]
2025-06-21 18:57:45.692 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户令牌创建成功: Haohao268826
2025-06-21 18:57:46.071 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 通过refreshToken移除设备成功: Key - user:devices:refreshToken:Haohao268826
2025-06-21 18:57:46.082 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 820af072-3ccb-4e26-bc72-e0ac17af0acc
2025-06-21 18:57:46.098 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - 3e6b48b2-2007-493a-baf7-07ca3b441596
2025-06-21 18:57:46.098 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户 Haohao268826 刷新令牌后，Access Token 设备数超限，已移除最早的设备: $2a$10$lBhKxl.nA5wGHQBF/maZ/O1U6AXmBC9wHR1MVfsI0qg0OUD9aqpb.
2025-06-21 18:57:46.103 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - 863080bb-0b72-4df2-a6be-3696fdb35447
2025-06-21 18:57:46.135 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-06-21 18:57:46.136 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:46.136 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6db13efa]
2025-06-21 18:57:46.148 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:46.149 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-06-21 18:57:46.150 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-21 18:57:46.123961(Timestamp), $2a$10$3zQLNKrwsaXbXjSyiKfXS.gCMcO6wsXE6tAeOeLj1KZmm6J/U6mSy(String), 2025-07-05 18:57:45.0(Timestamp), 23(Long)
2025-06-21 18:57:46.176 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-21 18:57:46.177 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6db13efa]
2025-06-21 18:57:46.178 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-06-21 18:57:46.178 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6db13efa]
2025-06-21 18:57:46.178 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6db13efa]
2025-06-21 18:57:46.178 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6db13efa]
2025-06-21 18:57:46.208 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 令牌刷新成功: Haohao268826
2025-06-21 18:57:46.249 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:46.249 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41dff83]
2025-06-21 18:57:46.249 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@94484703 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:46.249 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:57:46.250 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了刷新令牌(String), 2025-06-21T18:57:46.234959900(LocalDateTime)
2025-06-21 18:57:46.273 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:57:46.276 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41dff83]
2025-06-21 18:57:46.276 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了刷新令牌
2025-06-21 18:57:46.276 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41dff83]
2025-06-21 18:57:46.276 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41dff83]
2025-06-21 18:57:46.276 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41dff83]
2025-06-21 18:57:46.307 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:46.324 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=令牌刷新成功, success=true, data=TokenResponse(username=Haohao268826, accessToken (truncated)...]
2025-06-21 18:57:46.337 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:46.338 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:46.439 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/user/UserWithUserProfile
2025-06-21 18:57:46.439 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:46.440 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:57:46.542 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:46.544 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:46.544 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:46.544 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2072bf8e]
2025-06-21 18:57:46.544 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1166929769 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:46.544 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:46.545 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:46.557 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:46.557 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2072bf8e]
2025-06-21 18:57:46.558 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:46.558 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2072bf8e]
2025-06-21 18:57:46.558 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2072bf8e]
2025-06-21 18:57:46.558 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2072bf8e]
2025-06-21 18:57:46.594 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:46.595 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user/UserWithUserProfile] with attributes [authenticated]
2025-06-21 18:57:46.595 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/user/UserWithUserProfile
2025-06-21 18:57:46.595 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/user/UserWithUserProfile", parameters={}
2025-06-21 18:57:46.596 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:57:46.602 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.UserController] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:57:46.634 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.service.impl.UserServiceImpl : 获取用户DTO, username: Haohao268826
2025-06-21 18:57:46.639 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:46.639 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56e444db]
2025-06-21 18:57:46.639 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@577274493 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:46.639 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findRolesByUserId : ==>  Preparing: SELECT r.id, r.name, r.created_time, r.updated_time FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:46.640 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findRolesByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:46.653 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findRolesByUserId : <==      Total: 1
2025-06-21 18:57:46.653 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56e444db]
2025-06-21 18:57:46.654 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56e444db]
2025-06-21 18:57:46.654 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56e444db]
2025-06-21 18:57:46.654 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56e444db]
2025-06-21 18:57:46.687 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.pure.controller.UserController : 获取用户信息：Haohao268826
2025-06-21 18:57:46.704 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:46.704 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9dd570]
2025-06-21 18:57:46.704 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1842050568 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:46.704 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-21 18:57:46.704 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2025-06-21(String)
2025-06-21 18:57:46.717 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 1
2025-06-21 18:57:46.717 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9dd570]
2025-06-21 18:57:46.718 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9dd570]
2025-06-21 18:57:46.718 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9dd570]
2025-06-21 18:57:46.718 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9dd570]
2025-06-21 18:57:46.764 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:46.764 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@438e0ece]
2025-06-21 18:57:46.764 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1997697842 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:46.764 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:57:46.764 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了操作的名称还未定义(String), 2025-06-21T18:57:46.752437700(LocalDateTime)
2025-06-21 18:57:46.800 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:57:46.801 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@438e0ece]
2025-06-21 18:57:46.801 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了操作的名称还未定义
2025-06-21 18:57:46.801 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@438e0ece]
2025-06-21 18:57:46.801 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@438e0ece]
2025-06-21 18:57:46.801 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@438e0ece]
2025-06-21 18:57:46.827 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:46.843 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=查询用户名获取全部资料成功, success=true, data=UserWithUserProfileDTO(id=23, username=Ha (truncated)...]
2025-06-21 18:57:46.848 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:46.848 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:54.639 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/github
2025-06-21 18:57:54.639 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:54.641 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-06-21 18:57:54.641 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 18:57:54.641 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/github] with attributes [permitAll]
2025-06-21 18:57:54.641 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/github
2025-06-21 18:57:54.642 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/github", parameters={}
2025-06-21 18:57:54.643 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-06-21 18:57:54.665 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://github.com/login/oauth/authorize?response_type=code&client_id=Ov23liowOuqP1FEcOBXl&redirect_uri=http://localhost:8080/oauth/callback/github&state=427f280aa461c384e3a1cbf977a7f1ec&scope=
2025-06-21 18:57:54.666 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-06-21 18:57:54.666 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:55.222 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/github?code=2bd07dfe5848a42afef3&state=427f280aa461c384e3a1cbf977a7f1ec
2025-06-21 18:57:55.222 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:55.223 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-06-21 18:57:55.223 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-21 18:57:55.224 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/github?code=2bd07dfe5848a42afef3&state=427f280aa461c384e3a1cbf977a7f1ec] with attributes [permitAll]
2025-06-21 18:57:55.224 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/github?code=2bd07dfe5848a42afef3&state=427f280aa461c384e3a1cbf977a7f1ec
2025-06-21 18:57:55.224 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/github?code=2bd07dfe5848a42afef3&state=427f280aa461c384e3a1cbf977a7f1ec", parameters={masked}
2025-06-21 18:57:55.225 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-06-21 18:57:55.230 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.pure.controller.OAuth2Controller : 收到github的回调: me.zhyd.oauth.model.AuthCallback@456685f
2025-06-21 18:57:56.818 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@1ea8cdbf
2025-06-21 18:57:56.843 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.service.impl.OAuth2ServiceImpl : Found cached OAuth2 user information
2025-06-21 18:57:56.862 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-06-21 18:57:56.862 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:58.887 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:57:58.887 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing POST /api/auth/refresh
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:57:58.888 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:57:59.007 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:59.009 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:59.009 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:59.009 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:59.010 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.010 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b2ff5d4]
2025-06-21 18:57:59.010 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1344202902 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:59.010 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:59.010 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.011 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51889c08]
2025-06-21 18:57:59.011 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37ccad25]
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@321569751 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1991533729 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will be managed by Spring
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.011 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.021 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:59.021 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51889c08]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b2ff5d4]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b2ff5d4]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51889c08]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b2ff5d4]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51889c08]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b2ff5d4]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51889c08]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37ccad25]
2025-06-21 18:57:59.022 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:59.023 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37ccad25]
2025-06-21 18:57:59.023 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37ccad25]
2025-06-21 18:57:59.023 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37ccad25]
2025-06-21 18:57:59.054 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:59.054 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:59.054 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/refresh] with attributes [permitAll]
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured POST /api/auth/refresh
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : POST "/api/auth/refresh", parameters={}
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:57:59.055 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:57:59.062 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.062 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.063 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b7c35ce] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:59.063 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2af86a18] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:59.063 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1517697747 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:57:59.063 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:57:59.063 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.064 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.pure.controller.AuthController : 刷新令牌
2025-06-21 18:57:59.064 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.service.impl.AuthServiceImpl : 处理令牌刷新请求
2025-06-21 18:57:59.065 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1397090405 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:57:59.065 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:57:59.065 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-21 18:57:59.077 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:57:59.077 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:57:59.077 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2af86a18]
2025-06-21 18:57:59.077 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b7c35ce]
2025-06-21 18:57:59.078 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.078 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11404d17] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:59.078 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:59.078 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1840945562 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:57:59.078 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:57:59.078 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:57:59.079 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-21 18:57:59.081 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:59.081 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:59.090 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:59.090 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:57:59.091 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11404d17]
2025-06-21 18:57:59.092 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.092 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccd6a22] was not registered for synchronization because synchronization is not active
2025-06-21 18:57:59.092 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1601613401 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:57:59.093 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:57:59.093 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:59.093 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.093 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.093 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1da98cf1]
2025-06-21 18:57:59.093 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@314454143 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will be managed by Spring
2025-06-21 18:57:59.093 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:59.094 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.106 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:57:59.106 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ccd6a22]
2025-06-21 18:57:59.107 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:59.108 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1da98cf1]
2025-06-21 18:57:59.108 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:59.108 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:59.108 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1da98cf1]
2025-06-21 18:57:59.108 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1da98cf1]
2025-06-21 18:57:59.108 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1da98cf1]
2025-06-21 18:57:59.115 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:57:59.118 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:59.119 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:59.149 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.AuthServiceImpl : 用户令牌创建成功: Haohao268826
2025-06-21 18:57:59.531 [31mWARN [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.DeviceServiceImpl : 通过refreshToken移除设备失败，未找到匹配的token: Key - user:devices:refreshToken:Haohao268826
2025-06-21 18:57:59.531 [31mWARN [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.AuthServiceImpl : 未找到与旧刷新令牌关联的设备信息: Haohao268826
2025-06-21 18:57:59.534 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 0e6d7457-2721-4053-a054-2dd2f0508429
2025-06-21 18:57:59.536 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - 0f6b0cea-10cb-4857-bcc6-397d06188750
2025-06-21 18:57:59.536 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.AuthServiceImpl : 用户 Haohao268826 刷新令牌后，Access Token 设备数超限，已移除最早的设备: $2a$10$WwPP/rGAppKmNHPyOk.7C.QZrSn1gwJagiflwaDzkg4Rhl0GP9tyW
2025-06-21 18:57:59.537 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:refreshToken:Haohao268826, DeviceId - 1dd60a3b-17ed-400c-82cb-82e50a7dce28
2025-06-21 18:57:59.539 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - b498e7e3-9386-46c7-8f1f-61f98f091ba2
2025-06-21 18:57:59.539 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.AuthServiceImpl : 用户 Haohao268826 刷新令牌后，Refresh Token 设备数超限，已移除最早的设备: $2a$10$Asq0E6TDcVxFP9AMBfg7UubxsAthtE5p37sT2XzAwuCauFgZUksam
2025-06-21 18:57:59.551 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-06-21 18:57:59.552 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.552 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f1f9027]
2025-06-21 18:57:59.552 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will be managed by Spring
2025-06-21 18:57:59.552 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-06-21 18:57:59.553 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-21 18:57:59.5400331(Timestamp), $2a$10$zrFA1y8TgjkaRJV.XxFvx.Xp/SSHMUzmNc7eLT9UXboresErKnoja(String), 2025-07-05 18:57:59.0(Timestamp), 23(Long)
2025-06-21 18:57:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-21 18:57:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f1f9027]
2025-06-21 18:57:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-06-21 18:57:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f1f9027]
2025-06-21 18:57:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f1f9027]
2025-06-21 18:57:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f1f9027]
2025-06-21 18:57:59.602 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.service.impl.AuthServiceImpl : 令牌刷新成功: Haohao268826
2025-06-21 18:57:59.615 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.615 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a399a4a]
2025-06-21 18:57:59.615 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1136368551 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will be managed by Spring
2025-06-21 18:57:59.615 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:57:59.615 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了刷新令牌(String), 2025-06-21T18:57:59.603069200(LocalDateTime)
2025-06-21 18:57:59.638 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:57:59.638 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a399a4a]
2025-06-21 18:57:59.638 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了刷新令牌
2025-06-21 18:57:59.638 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a399a4a]
2025-06-21 18:57:59.638 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a399a4a]
2025-06-21 18:57:59.638 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a399a4a]
2025-06-21 18:57:59.662 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:57:59.662 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=令牌刷新成功, success=true, data=TokenResponse(username=Haohao268826, accessToken (truncated)...]
2025-06-21 18:57:59.663 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:57:59.663 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:57:59.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/user/UserWithUserProfile
2025-06-21 18:57:59.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:57:59.769 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:57:59.880 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:57:59.882 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:57:59.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:57:59.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3df3eeb9]
2025-06-21 18:57:59.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@721215531 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:57:59.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:57:59.883 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:57:59.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:57:59.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3df3eeb9]
2025-06-21 18:57:59.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:57:59.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3df3eeb9]
2025-06-21 18:57:59.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3df3eeb9]
2025-06-21 18:57:59.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3df3eeb9]
2025-06-21 18:58:00.152 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:58:00.152 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user/UserWithUserProfile] with attributes [authenticated]
2025-06-21 18:58:00.152 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/user/UserWithUserProfile
2025-06-21 18:58:00.152 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/user/UserWithUserProfile", parameters={}
2025-06-21 18:58:00.152 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-06-21 18:58:00.153 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.UserController] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-21 18:58:00.175 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.service.impl.UserServiceImpl : 获取用户DTO, username: Haohao268826
2025-06-21 18:58:00.177 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:58:00.177 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d2c607d]
2025-06-21 18:58:00.177 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@865816463 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:58:00.177 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findRolesByUserId : ==>  Preparing: SELECT r.id, r.name, r.created_time, r.updated_time FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:58:00.177 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findRolesByUserId : ==> Parameters: 23(Long)
2025-06-21 18:58:00.189 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findRolesByUserId : <==      Total: 1
2025-06-21 18:58:00.189 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d2c607d]
2025-06-21 18:58:00.189 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d2c607d]
2025-06-21 18:58:00.190 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d2c607d]
2025-06-21 18:58:00.190 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d2c607d]
2025-06-21 18:58:00.220 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.pure.controller.UserController : 获取用户信息：Haohao268826
2025-06-21 18:58:00.266 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:58:00.266 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6079c111]
2025-06-21 18:58:00.266 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2131202350 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:58:00.266 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-21 18:58:00.267 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了操作的名称还未定义(String), 2025-06-21T18:58:00.256251200(LocalDateTime)
2025-06-21 18:58:00.288 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-21 18:58:00.288 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6079c111]
2025-06-21 18:58:00.288 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了操作的名称还未定义
2025-06-21 18:58:00.288 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6079c111]
2025-06-21 18:58:00.288 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6079c111]
2025-06-21 18:58:00.288 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6079c111]
2025-06-21 18:58:00.312 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:58:00.313 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=查询用户名获取全部资料成功, success=true, data=UserWithUserProfileDTO(id=23, username=Ha (truncated)...]
2025-06-21 18:58:00.314 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:58:00.314 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:58:43.150 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-21 18:59:39.945 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 18:59:39.945 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 18:59:39.945 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:39.945 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:39.946 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:59:39.946 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:59:40.073 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:40.073 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:40.076 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2357d59b]
2025-06-21 18:59:40.076 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@301323946 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3056584c]
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1279806272 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:40.076 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:40.088 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:40.089 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2357d59b]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3056584c]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2357d59b]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2357d59b]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2357d59b]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3056584c]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3056584c]
2025-06-21 18:59:40.090 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3056584c]
2025-06-21 18:59:40.122 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:40.122 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 18:59:40.123 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a572a] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1817571318 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e5a8152] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1068983802 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 18:59:40.124 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-21 18:59:40.135 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 18:59:40.135 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 18:59:40.136 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e5a8152]
2025-06-21 18:59:40.136 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@63a572a]
2025-06-21 18:59:40.136 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:40.136 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ef1b165] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:40.136 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@71033848 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:40.137 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:40.137 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 18:59:40.137 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T10: (truncated)...]
2025-06-21 18:59:40.137 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-21 18:59:40.138 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:40.138 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:40.148 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 18:59:40.149 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ef1b165]
2025-06-21 18:59:40.149 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:40.149 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ce5c8cb] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:40.149 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2139895467 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:40.149 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 18:59:40.150 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:40.162 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 18:59:40.162 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ce5c8cb]
2025-06-21 18:59:40.163 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:40.163 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 18:59:40.164 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:40.164 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:42.953 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:42.953 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-21 18:59:42.954 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:42.954 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:42.954 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:42.954 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:43.077 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:43.077 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:43.078 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:43.078 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:43.078 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:43.078 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:43.078 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17688fa4]
2025-06-21 18:59:43.078 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ffeb439]
2025-06-21 18:59:43.078 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1176881830 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 18:59:43.079 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:43.079 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@554073954 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:43.079 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:43.079 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:43.079 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ffeb439]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17688fa4]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17688fa4]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ffeb439]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ffeb439]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17688fa4]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ffeb439]
2025-06-21 18:59:43.096 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17688fa4]
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-21 18:59:43.128 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-21 18:59:43.129 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:43.129 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:43.132 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:43.132 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ee421a3] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:43.133 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1944675825 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:43.133 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-21 18:59:43.133 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-21 18:59:43.147 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-21 18:59:43.148 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ee421a3]
2025-06-21 18:59:43.148 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:43.149 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-21 18:59:43.151 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:43.151 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:43.173 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:43.173 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a96d26b] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:43.174 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@583004495 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:43.174 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-21 18:59:43.174 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-21 18:59:43.192 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-21 18:59:43.192 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a96d26b]
2025-06-21 18:59:43.192 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:43.193 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d801b71] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:43.193 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1202826889 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:43.193 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-21 18:59:43.193 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-21 18:59:43.205 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-21 18:59:43.205 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d801b71]
2025-06-21 18:59:43.206 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:43.216 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 18:59:43.219 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:43.219 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:45.847 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-21 18:59:45.847 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:45.847 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:45.847 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:45.847 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:45.847 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:45.971 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:45.971 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:45.974 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:45.974 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:45.974 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c772ffe]
2025-06-21 18:59:45.974 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1041353788 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 18:59:45.974 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:45.975 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:45.976 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:45.976 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:45.976 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5de8b257]
2025-06-21 18:59:45.976 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1864211422 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:45.977 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:45.977 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:45.986 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:45.987 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c772ffe]
2025-06-21 18:59:45.987 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:45.987 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c772ffe]
2025-06-21 18:59:45.987 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c772ffe]
2025-06-21 18:59:45.987 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c772ffe]
2025-06-21 18:59:45.988 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:45.989 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5de8b257]
2025-06-21 18:59:45.989 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:45.989 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5de8b257]
2025-06-21 18:59:45.989 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5de8b257]
2025-06-21 18:59:45.989 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5de8b257]
2025-06-21 18:59:46.019 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:46.019 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:46.020 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53873508] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1283208634 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:46.021 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21ee4321] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:46.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1871163991 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:46.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-21 18:59:46.022 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-21 18:59:46.035 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-21 18:59:46.036 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53873508]
2025-06-21 18:59:46.036 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:46.036 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-21 18:59:46.037 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21ee4321]
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37fc50e] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1463796185 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-21 18:59:46.038 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-21 18:59:46.049 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-21 18:59:46.050 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37fc50e]
2025-06-21 18:59:46.050 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:46.050 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 18:59:46.054 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:46.054 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:50.430 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:50.430 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-21 18:59:50.430 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:50.430 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:50.430 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:50.430 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:50.547 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:50.547 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:50.550 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:50.550 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a26b33d]
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbea2]
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1803413786 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@63430152 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will be managed by Spring
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:50.550 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:50.562 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:50.562 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbea2]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a26b33d]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a26b33d]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbea2]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a26b33d]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbea2]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a26b33d]
2025-06-21 18:59:50.563 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbea2]
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-21 18:59:50.595 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e10048] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1430376304 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will not be managed by Spring
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-21 18:59:50.596 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-21 18:59:50.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:50.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e600e10] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:50.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1057084665 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:50.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-21 18:59:50.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-21 18:59:50.609 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e600e10]
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e10048]
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7cbcfccd] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@314736068 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:50.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-21 18:59:50.611 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:50.612 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:50.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-21 18:59:50.622 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7cbcfccd]
2025-06-21 18:59:50.623 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:50.623 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 18:59:50.625 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:50.625 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:52.843 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-21 18:59:52.843 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:52.843 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:52.955 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:52.957 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:52.957 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:52.957 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d5759a2]
2025-06-21 18:59:52.957 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@64212302 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:52.957 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:52.957 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:52.970 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:52.971 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d5759a2]
2025-06-21 18:59:52.971 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:52.971 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d5759a2]
2025-06-21 18:59:52.971 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d5759a2]
2025-06-21 18:59:52.971 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d5759a2]
2025-06-21 18:59:53.005 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:53.006 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-21 18:59:53.006 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-21 18:59:53.006 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-21 18:59:53.007 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:53.007 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:53.007 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6359c542] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:53.007 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2101730282 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:53.007 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-21 18:59:53.007 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-21 18:59:53.020 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-21 18:59:53.020 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6359c542]
2025-06-21 18:59:53.021 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:53.021 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-21 18:59:53.022 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:53.023 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:53.148 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:53.149 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:53.149 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:53.247 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:53.249 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:53.249 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:53.249 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b29dd3b]
2025-06-21 18:59:53.249 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2079997759 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:53.249 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:53.249 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:53.261 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:53.262 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b29dd3b]
2025-06-21 18:59:53.262 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:53.262 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b29dd3b]
2025-06-21 18:59:53.262 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b29dd3b]
2025-06-21 18:59:53.262 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b29dd3b]
2025-06-21 18:59:53.296 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:53.297 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-21 18:59:53.297 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:53.297 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-21 18:59:53.297 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:53.298 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:53.298 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3331eba7] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:53.298 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@815611027 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:53.299 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-21 18:59:53.299 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-21 18:59:53.313 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-21 18:59:53.313 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3331eba7]
2025-06-21 18:59:53.313 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:53.313 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d954bf9] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:53.313 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@835056959 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:53.314 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-21 18:59:53.314 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-21 18:59:53.325 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-21 18:59:53.326 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d954bf9]
2025-06-21 18:59:53.326 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:53.326 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 18:59:53.329 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:53.329 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:56.438 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-21 18:59:56.438 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:56.438 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:56.438 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 18:59:56.438 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:56.438 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:56.562 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:56.562 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 18:59:56.564 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:56.564 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a30e23c]
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@224191e7]
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@813580301 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@171706144 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:56.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@224191e7]
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a30e23c]
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a30e23c]
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@224191e7]
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a30e23c]
2025-06-21 18:59:56.575 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@224191e7]
2025-06-21 18:59:56.576 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a30e23c]
2025-06-21 18:59:56.576 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@224191e7]
2025-06-21 18:59:56.608 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:56.608 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 18:59:56.609 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-21 18:59:56.609 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-21 18:59:56.609 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-21 18:59:56.609 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 18:59:56.609 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-21 18:59:56.609 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34c09d6d] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1753421646 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-21 18:59:56.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-21 18:59:56.611 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:56.611 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@448212c7] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:56.611 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1093921607 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:56.611 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-21 18:59:56.611 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34c09d6d]
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@448212c7]
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@130c5429] was not registered for synchronization because synchronization is not active
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1196189861 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 18:59:56.623 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-21 18:59:56.624 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-21 18:59:56.624 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-21 18:59:56.625 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:56.625 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 18:59:56.634 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-21 18:59:56.634 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@130c5429]
2025-06-21 18:59:56.635 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 18:59:56.635 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 18:59:56.637 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 18:59:56.637 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:00:05.300 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3
2025-06-21 19:00:05.300 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 19:00:05.301 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithEpisodes(String, CustomUserDetails)
2025-06-21 19:00:05.407 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 19:00:05.408 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 19:00:05.408 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.408 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e9964d9]
2025-06-21 19:00:05.409 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@7939037 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 19:00:05.409 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 19:00:05.409 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 19:00:05.420 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 19:00:05.420 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e9964d9]
2025-06-21 19:00:05.420 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 19:00:05.420 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e9964d9]
2025-06-21 19:00:05.420 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e9964d9]
2025-06-21 19:00:05.420 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e9964d9]
2025-06-21 19:00:05.451 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 19:00:05.452 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3] with attributes [permitAll]
2025-06-21 19:00:05.452 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3
2025-06-21 19:00:05.452 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3", parameters={masked}
2025-06-21 19:00:05.452 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithEpisodes(String, CustomUserDetails)
2025-06-21 19:00:05.453 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.453 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16807689] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:05.453 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@917897394 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 19:00:05.453 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-21 19:00:05.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 你与我最后的战场，亦或是世界起始的圣战 第二季(String)
2025-06-21 19:00:05.465 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-21 19:00:05.465 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16807689]
2025-06-21 19:00:05.466 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.466 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6a0334] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:05.466 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@907797489 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 19:00:05.466 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : ==>  Preparing: SELECT ve.id, ve.video_info_id, ve.number, ve.play_url, ve.duration, ve.created_time, ve.updated_time, COUNT(DISTINCT CASE WHEN vel.status = true THEN vel.id END) AS likes_count, CAST(IFNULL(MAX(CASE WHEN vel.user_id = ? AND vel.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status FROM video_episodes ve LEFT JOIN video_episodes_likes vel ON ve.id = vel.video_episode_id WHERE ve.video_info_id = ? GROUP BY ve.id ORDER BY ve.number ASC
2025-06-21 19:00:05.466 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : ==> Parameters: 23(Long), 9(Long)
2025-06-21 19:00:05.479 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : <==      Total: 8
2025-06-21 19:00:05.479 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c6a0334]
2025-06-21 19:00:05.480 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.480 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59721d12] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:05.480 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1050248165 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 19:00:05.480 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : ==>  Preparing: SELECT vt.name,vt.id FROM video_type vt JOIN video_info_type_link vitl ON vt.id =vitl.type_id WHERE vitl.video_info_id =?
2025-06-21 19:00:05.480 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : ==> Parameters: 9(Long)
2025-06-21 19:00:05.491 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : <==      Total: 7
2025-06-21 19:00:05.491 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59721d12]
2025-06-21 19:00:05.492 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 19:00:05.500 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpi (truncated)...]
2025-06-21 19:00:05.505 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 19:00:05.505 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:00:05.564 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13
2025-06-21 19:00:05.564 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 19:00:05.564 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoByTypeWithPagination(PageRequestDTO)
2025-06-21 19:00:05.672 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 19:00:05.673 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 19:00:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.674 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@625788c9]
2025-06-21 19:00:05.674 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@984234071 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 19:00:05.674 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 19:00:05.674 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 19:00:05.685 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 19:00:05.685 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@625788c9]
2025-06-21 19:00:05.685 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 19:00:05.686 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@625788c9]
2025-06-21 19:00:05.686 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@625788c9]
2025-06-21 19:00:05.686 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@625788c9]
2025-06-21 19:00:05.719 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 19:00:05.719 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13] with attributes [permitAll]
2025-06-21 19:00:05.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13
2025-06-21 19:00:05.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13", parameters={masked}
2025-06-21 19:00:05.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoByTypeWithPagination(PageRequestDTO)
2025-06-21 19:00:05.722 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.722 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7235210e] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:05.722 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1896601553 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 19:00:05.722 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : ==>  Preparing: SELECT vi.id,vi.title,vi.cover_image_url,vi.description FROM video_info vi INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id INNER JOIN video_type vt ON vitl.type_id =vt.id WHERE vt.name = ? LIMIT ?, ?
2025-06-21 19:00:05.722 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : ==> Parameters: 冒险(String), 0(Integer), 13(Integer)
2025-06-21 19:00:05.735 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : <==      Total: 12
2025-06-21 19:00:05.735 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7235210e]
2025-06-21 19:00:05.735 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:05.735 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15594cf6] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:05.736 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1317230179 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 19:00:05.736 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.countVideoInfoByType : ==>  Preparing: SELECT COUNT(*) FROM video_info vi INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id INNER JOIN video_type vt ON vitl.type_id =vt.id WHERE vt.name = ?
2025-06-21 19:00:05.736 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.countVideoInfoByType : ==> Parameters: 冒险(String)
2025-06-21 19:00:05.748 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.countVideoInfoByType : <==      Total: 1
2025-06-21 19:00:05.748 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15594cf6]
2025-06-21 19:00:05.749 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 19:00:05.749 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 19:00:05.750 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 19:00:05.750 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:00:08.601 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-21 19:00:08.601 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 19:00:08.601 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 19:00:08.708 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 19:00:08.709 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 19:00:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e5c2a]
2025-06-21 19:00:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@801789219 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 19:00:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 19:00:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 19:00:08.721 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 19:00:08.721 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e5c2a]
2025-06-21 19:00:08.721 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 19:00:08.721 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e5c2a]
2025-06-21 19:00:08.721 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e5c2a]
2025-06-21 19:00:08.721 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@12e5c2a]
2025-06-21 19:00:08.753 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 19:00:08.753 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-21 19:00:08.753 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-21 19:00:08.753 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-21 19:00:08.754 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-21 19:00:08.754 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:08.754 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2af50e03] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:08.754 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1691652239 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 19:00:08.754 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-21 19:00:08.755 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-21 19:00:08.766 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-21 19:00:08.767 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2af50e03]
2025-06-21 19:00:08.767 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 19:00:08.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-21 19:00:08.769 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 19:00:08.769 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:00:08.905 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 19:00:08.905 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 19:00:08.906 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 19:00:09.023 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 19:00:09.025 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 19:00:09.026 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:09.026 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3b7dc8]
2025-06-21 19:00:09.026 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2128565141 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will be managed by Spring
2025-06-21 19:00:09.026 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 19:00:09.026 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 19:00:09.038 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 19:00:09.038 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3b7dc8]
2025-06-21 19:00:09.039 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 19:00:09.039 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3b7dc8]
2025-06-21 19:00:09.039 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3b7dc8]
2025-06-21 19:00:09.039 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3b7dc8]
2025-06-21 19:00:09.080 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 19:00:09.080 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-21 19:00:09.080 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-21 19:00:09.081 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-21 19:00:09.081 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-21 19:00:09.082 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:09.083 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@680046b2] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:09.083 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1292115460 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will not be managed by Spring
2025-06-21 19:00:09.083 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-21 19:00:09.083 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-21 19:00:09.096 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-21 19:00:09.097 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@680046b2]
2025-06-21 19:00:09.097 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:00:09.097 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23662014] was not registered for synchronization because synchronization is not active
2025-06-21 19:00:09.097 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1228523292 wrapping com.mysql.cj.jdbc.ConnectionImpl@1b1f7d95] will not be managed by Spring
2025-06-21 19:00:09.097 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-21 19:00:09.098 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-21 19:00:09.140 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-21 19:00:09.140 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23662014]
2025-06-21 19:00:09.141 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 19:00:09.141 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-21 19:00:09.144 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 19:00:09.144 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:06:12.758 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 19:06:12.758 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 19:06:12.758 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 19:06:12.758 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 19:06:12.758 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 19:06:12.758 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 19:06:12.878 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 19:06:12.878 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 19:06:12.881 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 19:06:12.881 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:06:12.881 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64126435]
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1625767314 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will be managed by Spring
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 19:06:12.882 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e92695f]
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@178175366 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will be managed by Spring
2025-06-21 19:06:12.882 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 19:06:12.883 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e92695f]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64126435]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e92695f]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64126435]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e92695f]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e92695f]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64126435]
2025-06-21 19:06:12.894 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64126435]
2025-06-21 19:06:12.925 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 19:06:12.926 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 19:06:12.926 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 19:06:12.926 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 19:06:12.927 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 19:06:12.928 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:06:12.928 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a26ef7c] was not registered for synchronization because synchronization is not active
2025-06-21 19:06:12.929 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@13039282 wrapping com.mysql.cj.jdbc.ConnectionImpl@a2f47b1] will not be managed by Spring
2025-06-21 19:06:12.929 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 19:06:12.929 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 19:06:12.929 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-21 19:06:12.929 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 19:06:12.929 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 19:06:12.930 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 19:06:12.930 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 19:06:12.930 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:06:12.930 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@108d0ec4] was not registered for synchronization because synchronization is not active
2025-06-21 19:06:12.930 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@316616870 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 19:06:12.931 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 19:06:12.931 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-21 19:06:12.941 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 19:06:12.941 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a26ef7c]
2025-06-21 19:06:12.942 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 19:06:12.942 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T11: (truncated)...]
2025-06-21 19:06:12.943 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 19:06:12.943 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@108d0ec4]
2025-06-21 19:06:12.944 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:06:12.944 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6382f7ba] was not registered for synchronization because synchronization is not active
2025-06-21 19:06:12.944 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 19:06:12.945 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:06:12.946 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@461450784 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 19:06:12.946 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 19:06:12.947 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-21 19:06:12.959 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 19:06:12.959 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6382f7ba]
2025-06-21 19:06:12.959 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 19:06:12.959 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57bc46b6] was not registered for synchronization because synchronization is not active
2025-06-21 19:06:12.960 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@603411676 wrapping com.mysql.cj.jdbc.ConnectionImpl@443bf619] will not be managed by Spring
2025-06-21 19:06:12.960 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 19:06:12.960 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-21 19:06:12.972 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 19:06:12.972 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57bc46b6]
2025-06-21 19:06:12.973 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 19:06:12.973 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 19:06:12.974 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 19:06:12.974 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 19:28:43.158 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-21 19:58:43.164 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-21 20:28:43.175 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-06-21 20:44:27.883 [31mWARN [0;39m [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m15s286ms149µs200ns).
2025-06-21 21:02:28.334 [34mINFO [0;39m [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-06-21 21:32:28.335 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-06-21 21:57:36.283 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-21 21:57:36.283 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-21 21:57:36.283 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 21:57:36.283 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-21 21:57:36.286 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 21:57:36.286 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 21:57:36.430 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 21:57:36.436 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 21:57:36.436 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b]
2025-06-21 21:57:36.438 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-21 21:57:36.438 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2051895724 wrapping com.mysql.cj.jdbc.ConnectionImpl@1bd16028] will be managed by Spring
2025-06-21 21:57:36.438 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-21 21:57:36.440 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-21 21:57:36.440 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 21:57:36.440 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1]
2025-06-21 21:57:36.441 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1595078502 wrapping com.mysql.cj.jdbc.ConnectionImpl@4ca7c548] will be managed by Spring
2025-06-21 21:57:36.441 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-21 21:57:36.441 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-21 21:57:36.454 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-21 21:57:36.455 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b]
2025-06-21 21:57:36.456 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-21 21:57:36.456 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1]
2025-06-21 21:57:36.456 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 21:57:36.456 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-21 21:57:36.457 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1] from current transaction
2025-06-21 21:57:36.457 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b] from current transaction
2025-06-21 21:57:36.457 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 21:57:36.457 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-21 21:57:36.458 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 21:57:36.458 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-21 21:57:36.470 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 21:57:36.470 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b]
2025-06-21 21:57:36.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 21:57:36.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b]
2025-06-21 21:57:36.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b]
2025-06-21 21:57:36.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d577a0b]
2025-06-21 21:57:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-21 21:57:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1]
2025-06-21 21:57:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-21 21:57:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1]
2025-06-21 21:57:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1]
2025-06-21 21:57:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300244a1]
2025-06-21 21:57:36.519 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 21:57:36.521 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-21 21:57:36.521 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-21 21:57:36.521 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-21 21:57:36.522 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-21 21:57:36.522 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-21 21:57:36.522 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-21 21:57:36.522 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-21 21:57:36.523 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-21 21:57:36.523 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-21 21:57:36.523 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 21:57:36.523 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56bdd6bb] was not registered for synchronization because synchronization is not active
2025-06-21 21:57:36.523 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1211187090 wrapping com.mysql.cj.jdbc.ConnectionImpl@4ca7c548] will not be managed by Spring
2025-06-21 21:57:36.523 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-21 21:57:36.524 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-21 21:57:36.524 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 21:57:36.524 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145992fe] was not registered for synchronization because synchronization is not active
2025-06-21 21:57:36.525 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2131566132 wrapping com.mysql.cj.jdbc.ConnectionImpl@1bd16028] will not be managed by Spring
2025-06-21 21:57:36.525 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-21 21:57:36.525 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-21 21:57:36.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-21 21:57:36.537 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-21 21:57:36.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145992fe]
2025-06-21 21:57:36.537 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56bdd6bb]
2025-06-21 21:57:36.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 21:57:36.537 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6825f080] was not registered for synchronization because synchronization is not active
2025-06-21 21:57:36.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@817053976 wrapping com.mysql.cj.jdbc.ConnectionImpl@1bd16028] will not be managed by Spring
2025-06-21 21:57:36.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-21 21:57:36.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-21 21:57:36.539 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 21:57:36.539 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-21T13: (truncated)...]
2025-06-21 21:57:36.541 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 21:57:36.541 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6825f080]
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@94dcbe5] was not registered for synchronization because synchronization is not active
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1311480764 wrapping com.mysql.cj.jdbc.ConnectionImpl@1bd16028] will not be managed by Spring
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-21 21:57:36.551 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-21 21:57:36.563 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-21 21:57:36.563 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@94dcbe5]
2025-06-21 21:57:36.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-21 21:57:36.564 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-21 21:57:36.565 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-21 21:57:36.565 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-21 22:02:28.338 [34mINFO [0;39m [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-06-21 22:32:28.347 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
