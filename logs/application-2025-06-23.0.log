2025-06-23 15:06:31.645 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-23 15:06:31.645 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 2152 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-23 15:06:31.648 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-23 15:06:31.649 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-23 15:06:32.665 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 15:06:32.672 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23 15:06:32.751 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 50 ms. Found 0 Redis repository interfaces.
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-23 15:06:32.910 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-23 15:06:32.911 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-23 15:06:32.913 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-23 15:06:32.914 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-23 15:06:32.915 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-23 15:06:32.915 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-23 15:06:32.915 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-23 15:06:32.915 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-23 15:06:32.915 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-23 15:06:32.915 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-23 15:06:32.916 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-23 15:06:32.916 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-23 15:06:32.916 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-23 15:06:32.916 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-23 15:06:32.917 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-23 15:06:32.917 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-23 15:06:32.917 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-23 15:06:33.404 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-23 15:06:33.410 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 15:06:33.411 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-23 15:06:33.411 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-23 15:06:33.499 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-23 15:06:33.500 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1809 ms
2025-06-23 15:06:33.751 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-23 15:06:33.763 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-23 15:06:33.770 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-23 15:06:33.777 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-23 15:06:33.785 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-23 15:06:33.790 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-23 15:06:33.797 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-23 15:06:33.803 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-23 15:06:33.811 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-23 15:06:33.819 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-23 15:06:33.834 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-23 15:06:33.841 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-23 15:06:33.847 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-23 15:06:33.854 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-23 15:06:33.865 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-23 15:06:34.259 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-23 15:06:34.854 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-23 15:06:34.854 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-23 15:06:35.119 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-23 15:06:35.122 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.182 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-23 15:06:35.183 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.184 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-23 15:06:35.184 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-23 15:06:35.185 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.188 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-23 15:06:35.189 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.189 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-23 15:06:35.189 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-23 15:06:35.269 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-23 15:06:35.271 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-23 15:06:35.275 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2ba9ed19, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3293030b, org.springframework.security.web.context.SecurityContextPersistenceFilter@a20898c, org.springframework.security.web.header.HeaderWriterFilter@289cf7db, org.springframework.security.web.authentication.logout.LogoutFilter@29bf90fc, com.example.pure.filter.JwtFilter@31b91435, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@22d47f09, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@28100232, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b53e6fc, org.springframework.security.web.session.SessionManagementFilter@268e30d4, org.springframework.security.web.access.ExceptionTranslationFilter@40416321, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7f9d40b3]
2025-06-23 15:06:35.276 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-23 15:06:35.278 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-23 15:06:35.423 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-23 15:06:35.447 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-23 15:06:35.516 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-23 15:06:35.524 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-23 15:06:35.881 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-23 15:06:36.002 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-23 15:06:36.025 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-23 15:06:36.026 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-23 15:06:36.027 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-23 15:06:36.027 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@231c521e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@296949c8, org.springframework.security.web.context.SecurityContextPersistenceFilter@64921450, org.springframework.security.web.header.HeaderWriterFilter@50b2ba2c, org.springframework.web.filter.CorsFilter@1be3a294, org.springframework.security.web.authentication.logout.LogoutFilter@7c07023, com.example.pure.filter.JwtFilter@31b91435, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18918d70, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5d373794, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33502cfe, org.springframework.security.web.session.SessionManagementFilter@12478b4e, org.springframework.security.web.access.ExceptionTranslationFilter@152d2a58, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@74ab8610]
2025-06-23 15:06:36.066 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3a7704c, started on Mon Jun 23 15:06:31 CST 2025
2025-06-23 15:06:36.077 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-23 15:06:36.078 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-23 15:06:36.078 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-23 15:06:36.078 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-23 15:06:36.078 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-23 15:06:36.079 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-23 15:06:36.082 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-06-23 15:06:36.083 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-23 15:06:36.084 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-23 15:06:36.084 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-23 15:06:36.084 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-23 15:06:36.155 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-23 15:06:36.183 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-23 15:06:36.413 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 15:06:36.423 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 15:06:36.424 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-23 15:06:36.425 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-23 15:06:36.425 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-23 15:06:36.425 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@29f86630]
2025-06-23 15:06:36.425 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@29f86630]
2025-06-23 15:06:36.425 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@29f86630]]
2025-06-23 15:06:36.425 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-23 15:06:36.425 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-23 15:06:36.425 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-23 15:06:36.438 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.235 seconds (JVM running for 6.362)
2025-06-23 15:07:36.062 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-23 15:07:46.829 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 15:07:46.829 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-23 15:07:46.829 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-23 15:07:46.830 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-23 15:07:46.830 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-23 15:07:46.833 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@52a50906
2025-06-23 15:07:46.835 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@623cb5e4
2025-06-23 15:07:46.836 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-23 15:07:46.836 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 7 ms
2025-06-23 15:07:46.853 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:07:46.853 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:07:46.856 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:07:46.857 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:07:46.866 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:07:46.866 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:07:47.391 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:07:47.391 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:07:47.410 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:07:47.410 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:07:47.414 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed]
2025-06-23 15:07:47.414 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea]
2025-06-23 15:07:47.419 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@53834787 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:07:47.419 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@439384686 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:07:47.422 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-23 15:07:47.422 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-23 15:07:47.439 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-23 15:07:47.439 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-23 15:07:47.469 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-23 15:07:47.469 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-23 15:07:47.470 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed]
2025-06-23 15:07:47.470 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea]
2025-06-23 15:07:47.472 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:07:47.472 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:07:47.472 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea] from current transaction
2025-06-23 15:07:47.472 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed] from current transaction
2025-06-23 15:07:47.472 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:07:47.472 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:07:47.473 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:07:47.473 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:07:47.484 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:07:47.484 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:07:47.485 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed]
2025-06-23 15:07:47.485 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea]
2025-06-23 15:07:47.486 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:07:47.486 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:07:47.486 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea]
2025-06-23 15:07:47.486 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed]
2025-06-23 15:07:47.487 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed]
2025-06-23 15:07:47.487 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea]
2025-06-23 15:07:47.487 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba650ed]
2025-06-23 15:07:47.487 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a585ea]
2025-06-23 15:07:47.567 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:07:47.567 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:07:47.573 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:07:47.573 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:07:47.573 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:07:47.573 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:07:47.576 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:07:47.576 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:07:47.578 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:07:47.578 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:07:47.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:07:47.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@727b934c] was not registered for synchronization because synchronization is not active
2025-06-23 15:07:47.595 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@467408746 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:07:47.595 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:07:47.595 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:07:47.598 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:07:47.598 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5dbc3b31] was not registered for synchronization because synchronization is not active
2025-06-23 15:07:47.608 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:07:47.609 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@727b934c]
2025-06-23 15:07:47.616 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1583597625 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:07:47.616 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:07:47.616 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:07:47.618 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:07:47.642 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:07:47.643 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5dbc3b31]
2025-06-23 15:07:47.643 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:07:47.643 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22552c93] was not registered for synchronization because synchronization is not active
2025-06-23 15:07:47.643 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1386600732 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:07:47.643 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:07:47.644 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:07:47.647 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:07:47.655 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:07:47.658 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22552c93]
2025-06-23 15:07:47.659 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:07:47.659 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc7dde] was not registered for synchronization because synchronization is not active
2025-06-23 15:07:47.659 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@180811974 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:07:47.659 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:07:47.660 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:07:47.670 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:07:47.671 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:07:47.671 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc7dde]
2025-06-23 15:07:47.674 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:07:47.674 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:07:47.681 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:07:47.687 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:07:47.687 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:08:45.331 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:08:45.331 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:08:45.331 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:08:45.331 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:08:45.331 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:08:45.331 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:08:45.454 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:08:45.454 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:08:45.485 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:08:45.485 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cfde21a]
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56080b27]
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@341277029 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@699325401 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:08:45.485 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:08:45.486 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:08:45.486 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:08:45.497 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cfde21a]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56080b27]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cfde21a]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56080b27]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cfde21a]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56080b27]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cfde21a]
2025-06-23 15:08:45.498 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56080b27]
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:08:45.530 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:08:45.531 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:08:45.531 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:08:45.532 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:08:45.532 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6336817a] was not registered for synchronization because synchronization is not active
2025-06-23 15:08:45.532 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1633869550 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:08:45.533 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:08:45.533 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:08:45.533 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@783de4c9] was not registered for synchronization because synchronization is not active
2025-06-23 15:08:45.533 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:08:45.533 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@473012585 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:08:45.533 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:08:45.534 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:08:45.545 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:08:45.545 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:08:45.545 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@783de4c9]
2025-06-23 15:08:45.545 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6336817a]
2025-06-23 15:08:45.545 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:08:45.545 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bc02e8c] was not registered for synchronization because synchronization is not active
2025-06-23 15:08:45.546 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2085389457 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:08:45.546 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:08:45.546 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:08:45.546 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:08:45.546 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:08:45.548 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:08:45.548 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:08:45.558 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:08:45.558 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bc02e8c]
2025-06-23 15:08:45.558 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:08:45.558 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@268f44d3] was not registered for synchronization because synchronization is not active
2025-06-23 15:08:45.559 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1288813248 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:08:45.559 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:08:45.559 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:08:45.570 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:08:45.571 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@268f44d3]
2025-06-23 15:08:45.571 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:08:45.572 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:08:45.573 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:08:45.573 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:38.418 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:17:38.418 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:17:38.418 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:38.418 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:38.419 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:38.419 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:38.529 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:38.532 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:38.532 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:38.532 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@323bdaf8]
2025-06-23 15:17:38.532 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@5083619 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:17:38.532 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:38.532 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:38.533 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:38.537 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:38.538 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:38.538 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145bd72f]
2025-06-23 15:17:38.538 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@967014334 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:17:38.538 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:38.538 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:38.543 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:38.543 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@323bdaf8]
2025-06-23 15:17:38.543 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:38.543 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@323bdaf8]
2025-06-23 15:17:38.543 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@323bdaf8]
2025-06-23 15:17:38.543 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@323bdaf8]
2025-06-23 15:17:38.550 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:38.550 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145bd72f]
2025-06-23 15:17:38.550 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:38.550 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145bd72f]
2025-06-23 15:17:38.550 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145bd72f]
2025-06-23 15:17:38.550 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@145bd72f]
2025-06-23 15:17:38.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:38.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:17:38.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:17:38.575 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:17:38.576 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:38.576 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:38.576 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d03e64] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:38.576 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1055136104 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:38.576 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:17:38.577 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:17:38.583 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:38.583 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:17:38.583 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:17:38.583 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:17:38.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:38.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:38.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@363841a7] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:38.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@64470907 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:17:38.584 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:17:38.585 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:38.588 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:17:38.589 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d03e64]
2025-06-23 15:17:38.589 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:38.589 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a62aa26] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:38.589 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1100242574 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:38.589 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:17:38.589 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:17:38.597 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:17:38.597 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@363841a7]
2025-06-23 15:17:38.597 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:38.597 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:17:38.598 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:38.598 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:38.599 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:17:38.600 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a62aa26]
2025-06-23 15:17:38.600 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:38.600 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@611a58e4] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:38.600 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1317465467 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:38.600 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:17:38.600 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:38.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:17:38.611 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@611a58e4]
2025-06-23 15:17:38.612 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:38.612 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:17:38.612 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:38.613 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:43.601 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:17:43.601 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:17:43.601 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:43.601 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:43.602 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:43.602 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:43.719 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:43.719 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:43.722 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:43.723 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:43.723 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28d87fbf]
2025-06-23 15:17:43.723 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@138382649 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:17:43.723 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:43.723 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:43.724 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:43.724 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:43.724 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32d01213]
2025-06-23 15:17:43.724 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@994073069 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:17:43.724 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:43.724 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28d87fbf]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32d01213]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28d87fbf]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32d01213]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28d87fbf]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32d01213]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28d87fbf]
2025-06-23 15:17:43.735 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32d01213]
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:17:43.767 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23e928d2] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d654f2c] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1176267527 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1156021935 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:43.768 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:17:43.779 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:17:43.779 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d654f2c]
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23e928d2]
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7959d1e1] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@781385161 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:17:43.780 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:17:43.781 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:43.781 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:43.793 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:17:43.794 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7959d1e1]
2025-06-23 15:17:43.794 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:43.794 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a8b14df] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:43.794 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2104099510 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:43.794 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:17:43.794 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:43.806 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:17:43.806 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a8b14df]
2025-06-23 15:17:43.806 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:43.806 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:17:43.807 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:43.807 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:50.112 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:17:50.112 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:17:50.112 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:50.112 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:50.112 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:50.112 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:50.227 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:50.229 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:50.231 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:50.231 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:50.231 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cecfa2e]
2025-06-23 15:17:50.231 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1659617155 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:17:50.231 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:50.232 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:50.232 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:50.232 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:50.233 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f8df26]
2025-06-23 15:17:50.233 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@917109881 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:17:50.233 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:50.233 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cecfa2e]
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f8df26]
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cecfa2e]
2025-06-23 15:17:50.244 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f8df26]
2025-06-23 15:17:50.245 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f8df26]
2025-06-23 15:17:50.245 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cecfa2e]
2025-06-23 15:17:50.245 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f8df26]
2025-06-23 15:17:50.245 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cecfa2e]
2025-06-23 15:17:50.276 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:50.276 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:50.277 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cbb469b] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1818529157 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40af21c1] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1324991844 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:17:50.278 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:17:50.289 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:17:50.289 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40af21c1]
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cbb469b]
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a719d4d] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@594089707 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:17:50.290 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:17:50.291 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:50.292 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:50.302 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:17:50.302 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a719d4d]
2025-06-23 15:17:50.302 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:50.302 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4184ee94] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:50.303 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1188371413 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:50.303 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:17:50.303 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:50.315 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:17:50.316 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4184ee94]
2025-06-23 15:17:50.316 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:50.316 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:17:50.317 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:50.317 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:57.371 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:17:57.371 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:17:57.371 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:57.371 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:17:57.372 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:57.372 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:57.490 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:57.493 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:17:57.493 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:57.494 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:57.494 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e1251e5]
2025-06-23 15:17:57.494 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@912517298 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:17:57.494 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:57.494 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:57.495 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:17:57.496 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:57.496 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@265c3564]
2025-06-23 15:17:57.496 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2024128133 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:17:57.496 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:17:57.496 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:57.506 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:57.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e1251e5]
2025-06-23 15:17:57.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:57.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e1251e5]
2025-06-23 15:17:57.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e1251e5]
2025-06-23 15:17:57.507 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e1251e5]
2025-06-23 15:17:57.509 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:17:57.509 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@265c3564]
2025-06-23 15:17:57.509 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:17:57.509 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@265c3564]
2025-06-23 15:17:57.509 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@265c3564]
2025-06-23 15:17:57.509 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@265c3564]
2025-06-23 15:17:57.539 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:57.539 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:17:57.539 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:17:57.539 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:17:57.539 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:17:57.539 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b83b5b1] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@32535242 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:57.540 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@94cc084] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:57.541 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:57.541 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1482412399 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:57.541 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:17:57.541 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:17:57.552 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:17:57.552 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:17:57.552 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b83b5b1]
2025-06-23 15:17:57.552 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@94cc084]
2025-06-23 15:17:57.552 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:57.552 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2255421d] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:57.553 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1299381811 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:57.553 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:17:57.553 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:57.553 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:17:57.553 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:17:57.554 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:57.554 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:17:57.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:17:57.564 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2255421d]
2025-06-23 15:17:57.565 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:17:57.565 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e6506ed] was not registered for synchronization because synchronization is not active
2025-06-23 15:17:57.565 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@908652182 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:17:57.565 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:17:57.565 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:17:57.576 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:17:57.576 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e6506ed]
2025-06-23 15:17:57.577 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:17:57.577 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:17:57.578 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:17:57.578 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:05.770 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:05.770 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:05.770 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:05.770 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:05.770 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:05.770 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:05.885 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:05.885 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:05.888 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11096670]
2025-06-23 15:18:05.888 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@510550539 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ab9029]
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@185919694 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:05.888 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11096670]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ab9029]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11096670]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ab9029]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11096670]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ab9029]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11096670]
2025-06-23 15:18:05.900 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ab9029]
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:05.932 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@13ff700e] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c1d7393] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1986480088 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1360190723 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:05.933 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@13ff700e]
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c1d7393]
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9222f] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1611614378 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:05.944 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:05.945 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:05.945 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:05.945 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:05.945 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:05.946 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:05.956 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:05.957 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9222f]
2025-06-23 15:18:05.957 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:05.957 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@179b6588] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:05.957 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1496171790 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:05.957 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:05.957 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:05.968 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:05.969 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@179b6588]
2025-06-23 15:18:05.969 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:05.969 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:05.970 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:05.970 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:08.546 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:08.546 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:08.546 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:08.546 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:08.546 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:08.546 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:08.656 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:08.658 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:08.658 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:08.658 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a5ccace]
2025-06-23 15:18:08.658 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@90570146 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:08.659 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:08.659 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:08.660 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:08.663 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:08.663 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:08.663 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6196e4fa]
2025-06-23 15:18:08.663 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@417018348 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:08.663 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:08.663 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:08.669 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:08.669 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a5ccace]
2025-06-23 15:18:08.669 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:08.670 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a5ccace]
2025-06-23 15:18:08.670 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a5ccace]
2025-06-23 15:18:08.670 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a5ccace]
2025-06-23 15:18:08.675 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:08.675 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6196e4fa]
2025-06-23 15:18:08.675 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:08.675 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6196e4fa]
2025-06-23 15:18:08.675 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6196e4fa]
2025-06-23 15:18:08.675 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6196e4fa]
2025-06-23 15:18:08.699 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:08.700 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:08.700 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:08.700 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:08.700 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:08.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:08.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31b1ceb] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:08.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@407882788 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:08.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:08.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:08.709 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:08.709 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:08.709 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:08.709 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:08.709 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61a2977b] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@434519567 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:08.710 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:08.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:08.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31b1ceb]
2025-06-23 15:18:08.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:08.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3019ebee] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:08.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1807635778 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:08.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:08.713 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:08.724 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:08.724 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61a2977b]
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3019ebee]
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34238c1e] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@230410517 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:08.725 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:08.726 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:08.726 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:08.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:08.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34238c1e]
2025-06-23 15:18:08.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:08.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:08.738 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:08.738 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:14.389 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:14.389 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:14.389 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:14.389 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:14.390 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:14.390 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:14.500 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:14.500 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:14.503 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:14.503 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1599a958]
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14fe4c8d]
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1902083004 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1444749763 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:14.503 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:14.514 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:14.514 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1599a958]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14fe4c8d]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1599a958]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14fe4c8d]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1599a958]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14fe4c8d]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14fe4c8d]
2025-06-23 15:18:14.515 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1599a958]
2025-06-23 15:18:14.545 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:14.545 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:14.546 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34c15099] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2019851556 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@219e03fe] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@557540950 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:14.547 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:14.558 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:14.558 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@219e03fe]
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34c15099]
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@320b679e] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2072016778 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:14.559 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:14.560 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:14.560 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:14.570 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:14.570 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@320b679e]
2025-06-23 15:18:14.570 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:14.570 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@298b146d] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:14.570 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1087746389 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:14.571 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:14.571 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:14.582 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:14.582 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@298b146d]
2025-06-23 15:18:14.582 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:14.582 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:14.583 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:14.583 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:18.984 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:18.984 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:18.984 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:18.984 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:18.984 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:18.984 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:19.095 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:19.095 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:19.097 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:19.097 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:19.097 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:19.097 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:19.097 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@43fdd967]
2025-06-23 15:18:19.097 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49378f8f]
2025-06-23 15:18:19.098 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@745740327 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:19.098 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1578726749 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:19.098 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:19.098 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:19.098 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:19.098 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:19.107 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:19.107 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:19.107 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49378f8f]
2025-06-23 15:18:19.107 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@43fdd967]
2025-06-23 15:18:19.107 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:19.107 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:19.108 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@43fdd967]
2025-06-23 15:18:19.108 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49378f8f]
2025-06-23 15:18:19.108 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@43fdd967]
2025-06-23 15:18:19.108 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@43fdd967]
2025-06-23 15:18:19.108 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49378f8f]
2025-06-23 15:18:19.108 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@49378f8f]
2025-06-23 15:18:19.137 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:19.138 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:19.138 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:19.138 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:19.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:19.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:19.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f5c9f17] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:19.140 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@461326636 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:19.140 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:19.140 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:19.141 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:19.141 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:19.141 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:19.141 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:19.142 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:19.142 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:19.142 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313755d2] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:19.142 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@210306999 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:19.142 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:19.142 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:19.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:19.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f5c9f17]
2025-06-23 15:18:19.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:19.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7531d0cd] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:19.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@242113883 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:19.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:19.152 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:19.154 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:19.154 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@313755d2]
2025-06-23 15:18:19.155 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:19.155 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:19.155 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:19.155 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:19.163 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:19.164 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7531d0cd]
2025-06-23 15:18:19.164 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:19.164 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a015f9b] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:19.164 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1364018606 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:19.164 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:19.164 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:19.174 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:19.174 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a015f9b]
2025-06-23 15:18:19.174 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:19.175 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:19.175 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:19.175 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:27.304 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:27.304 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:27.304 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:27.304 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:27.305 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:27.305 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:27.416 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:27.418 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:27.418 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:27.418 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f07486a]
2025-06-23 15:18:27.418 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@638129283 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:27.418 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:27.418 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:27.419 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:27.421 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:27.421 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:27.421 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3279a89a]
2025-06-23 15:18:27.421 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1406704767 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:27.421 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:27.421 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:27.430 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:27.430 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f07486a]
2025-06-23 15:18:27.430 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:27.430 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f07486a]
2025-06-23 15:18:27.431 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f07486a]
2025-06-23 15:18:27.431 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f07486a]
2025-06-23 15:18:27.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:27.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3279a89a]
2025-06-23 15:18:27.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:27.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3279a89a]
2025-06-23 15:18:27.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3279a89a]
2025-06-23 15:18:27.433 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3279a89a]
2025-06-23 15:18:27.461 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:27.461 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:27.461 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:27.461 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:27.462 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:27.462 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:27.462 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59daa221] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:27.462 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1834743431 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:27.462 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:27.463 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:27.466 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:27.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:27.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:27.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:27.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:27.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:27.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1054cfc] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:27.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1269348346 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:27.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:27.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:27.473 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:27.473 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59daa221]
2025-06-23 15:18:27.473 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:27.473 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103e98e5] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:27.474 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@477752169 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:27.474 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:27.474 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:27.479 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:27.480 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1054cfc]
2025-06-23 15:18:27.480 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:27.480 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:27.481 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:27.481 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:27.485 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:27.485 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103e98e5]
2025-06-23 15:18:27.485 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:27.485 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fa8538a] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:27.485 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@188024618 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:27.486 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:27.486 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:27.497 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:27.497 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fa8538a]
2025-06-23 15:18:27.498 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:27.498 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:27.499 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:27.499 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:40.300 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:40.300 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:40.300 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:40.300 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:40.300 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:40.300 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:40.413 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:40.413 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:40.415 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:40.415 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6c2a2087]
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9cda4c]
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@672538702 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1852395830 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:40.415 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9cda4c]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6c2a2087]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6c2a2087]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9cda4c]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6c2a2087]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9cda4c]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6c2a2087]
2025-06-23 15:18:40.426 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e9cda4c]
2025-06-23 15:18:40.457 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:40.457 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:40.458 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@734b47c2] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@158068842 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@435829da] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1146755182 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:40.459 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:40.470 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:40.470 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:40.470 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@435829da]
2025-06-23 15:18:40.470 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@734b47c2]
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4d0f8fc2] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1327200576 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:40.471 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:40.472 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:40.472 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:40.482 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:40.483 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4d0f8fc2]
2025-06-23 15:18:40.483 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:40.483 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b910c74] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:40.483 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2052193115 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:40.483 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:40.483 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:40.494 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:40.495 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b910c74]
2025-06-23 15:18:40.495 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:40.495 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:40.496 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:40.496 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:43.811 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:43.811 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:43.811 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:43.811 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:43.811 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:43.811 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:43.929 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:43.929 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:43.931 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:43.931 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da5f97]
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a29b6db]
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1267588971 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1805997746 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:43.931 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:43.942 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:43.942 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a29b6db]
2025-06-23 15:18:43.942 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:43.942 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a29b6db]
2025-06-23 15:18:43.942 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a29b6db]
2025-06-23 15:18:43.942 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a29b6db]
2025-06-23 15:18:43.944 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:43.944 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da5f97]
2025-06-23 15:18:43.945 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:43.945 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da5f97]
2025-06-23 15:18:43.945 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da5f97]
2025-06-23 15:18:43.945 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da5f97]
2025-06-23 15:18:43.973 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:43.973 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:43.973 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:43.973 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:43.974 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:43.974 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:43.974 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@26ae65e9] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:43.974 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1821798011 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:43.974 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:43.975 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:43.979 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:43.979 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:43.979 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:43.979 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:43.980 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:43.980 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:43.980 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d21c8e4] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:43.980 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1896457430 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:43.980 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:43.980 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:43.985 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:43.985 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@26ae65e9]
2025-06-23 15:18:43.985 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:43.985 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78279ddf] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:43.986 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@770915871 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:43.986 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:43.986 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:43.992 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:43.993 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d21c8e4]
2025-06-23 15:18:43.993 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:43.994 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:43.994 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78279ddf]
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62b240f5] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@641724441 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:43.995 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:43.996 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:44.006 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:44.007 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62b240f5]
2025-06-23 15:18:44.007 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:44.007 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:44.008 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:44.008 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:49.403 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:49.403 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:49.403 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:49.403 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:49.403 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:49.403 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:49.516 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:49.519 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:49.519 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:49.519 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c8788c9]
2025-06-23 15:18:49.519 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@513664641 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:49.519 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:49.519 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:49.519 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:49.522 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:49.522 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:49.522 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bd42b1]
2025-06-23 15:18:49.522 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@859445779 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:49.522 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:49.522 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:49.530 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:49.530 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c8788c9]
2025-06-23 15:18:49.530 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:49.530 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c8788c9]
2025-06-23 15:18:49.530 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c8788c9]
2025-06-23 15:18:49.530 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c8788c9]
2025-06-23 15:18:49.534 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:49.534 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bd42b1]
2025-06-23 15:18:49.534 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:49.534 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bd42b1]
2025-06-23 15:18:49.534 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bd42b1]
2025-06-23 15:18:49.534 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bd42b1]
2025-06-23 15:18:49.561 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:49.562 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:49.562 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:49.562 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:49.562 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:49.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:49.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4196a2d8] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:49.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@107893256 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:49.563 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:49.564 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:49.566 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1401adf4] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1040056080 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:49.567 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:49.568 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:49.574 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:49.574 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4196a2d8]
2025-06-23 15:18:49.574 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:49.574 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b320eb4] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:49.574 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@256308072 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:49.574 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:49.575 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:49.579 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:49.579 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1401adf4]
2025-06-23 15:18:49.580 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:49.580 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:49.580 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:49.581 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:49.585 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:49.585 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b320eb4]
2025-06-23 15:18:49.585 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:49.585 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c10cf0c] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:49.586 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1463558548 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:49.586 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:49.586 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:49.596 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:49.596 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c10cf0c]
2025-06-23 15:18:49.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:49.597 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:49.598 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:49.598 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:52.627 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:52.627 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:52.627 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:52.627 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:52.627 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:52.627 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:52.740 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:52.742 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:52.742 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:52.742 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@269448cd]
2025-06-23 15:18:52.742 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@428383658 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:52.743 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:52.743 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:52.744 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:52.746 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:52.746 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:52.746 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5be75ed1]
2025-06-23 15:18:52.746 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@522149177 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:52.746 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:52.747 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:52.754 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:52.754 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@269448cd]
2025-06-23 15:18:52.755 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:52.755 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@269448cd]
2025-06-23 15:18:52.755 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@269448cd]
2025-06-23 15:18:52.755 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@269448cd]
2025-06-23 15:18:52.759 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:52.759 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5be75ed1]
2025-06-23 15:18:52.759 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:52.759 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5be75ed1]
2025-06-23 15:18:52.759 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5be75ed1]
2025-06-23 15:18:52.759 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5be75ed1]
2025-06-23 15:18:52.785 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:52.785 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:52.785 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:52.786 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:52.786 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:52.786 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:52.786 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@290bec37] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:52.787 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@634618602 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:52.787 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:52.787 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:52.791 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:52.792 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:52.792 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:52.792 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:52.792 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:52.792 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:52.792 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97bff8f] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:52.793 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1220942935 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:52.793 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:52.793 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:52.798 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:52.798 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@290bec37]
2025-06-23 15:18:52.798 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:52.798 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d28b4c8] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:52.798 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1261735182 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:52.798 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:52.799 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:52.805 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:52.805 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97bff8f]
2025-06-23 15:18:52.806 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:52.806 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:52.807 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:52.807 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:52.809 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:52.809 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d28b4c8]
2025-06-23 15:18:52.809 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:52.809 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@537280b8] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:52.810 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@730132714 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:52.810 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:52.810 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:52.821 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:52.821 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@537280b8]
2025-06-23 15:18:52.821 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:52.822 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:52.822 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:52.822 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:59.379 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:18:59.379 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:18:59.379 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:59.379 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:18:59.380 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:59.380 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:59.491 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:59.491 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:18:59.493 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:59.493 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45422263]
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aa174ee]
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1666325745 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1306657885 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:59.493 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aa174ee]
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45422263]
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aa174ee]
2025-06-23 15:18:59.504 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:18:59.505 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aa174ee]
2025-06-23 15:18:59.505 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45422263]
2025-06-23 15:18:59.505 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45422263]
2025-06-23 15:18:59.505 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2aa174ee]
2025-06-23 15:18:59.505 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45422263]
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:18:59.535 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fb382f3] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16789923] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1357296901 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1020250772 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:18:59.536 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:59.537 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fb382f3]
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16789923]
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f95f63d] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:59.548 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1308022125 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:59.549 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:18:59.549 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:18:59.549 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:18:59.550 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:59.550 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f95f63d]
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66cafa81] was not registered for synchronization because synchronization is not active
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1457441915 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:18:59.560 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:18:59.573 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:18:59.573 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66cafa81]
2025-06-23 15:18:59.574 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:18:59.574 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:18:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:18:59.575 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:19:05.511 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-06-23 15:19:05.511 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-06-23 15:19:05.511 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:19:05.511 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:19:05.511 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:19:05.511 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:19:05.623 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:19:05.623 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:19:05.626 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:19:05.626 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9b6cff7]
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57ae810]
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@416216768 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@41500586 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:19:05.626 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57ae810]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9b6cff7]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57ae810]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9b6cff7]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57ae810]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9b6cff7]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57ae810]
2025-06-23 15:19:05.638 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9b6cff7]
2025-06-23 15:19:05.669 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:19:05.670 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-06-23 15:19:05.670 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-06-23 15:19:05.670 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-06-23 15:19:05.670 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUserMessages(Authentication, int, int, String)
2025-06-23 15:19:05.671 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:19:05.671 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@409401d5] was not registered for synchronization because synchronization is not active
2025-06-23 15:19:05.671 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2097184070 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:19:05.671 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-06-23 15:19:05.671 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-06-23 15:19:05.672 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:19:05.672 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-06-23 15:19:05.672 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-06-23 15:19:05.672 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-06-23 15:19:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-06-23 15:19:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:19:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47561cba] was not registered for synchronization because synchronization is not active
2025-06-23 15:19:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1283890899 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:19:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-06-23 15:19:05.673 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-06-23 15:19:05.682 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-06-23 15:19:05.682 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@409401d5]
2025-06-23 15:19:05.682 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:19:05.682 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc200ab] was not registered for synchronization because synchronization is not active
2025-06-23 15:19:05.683 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1010313295 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:19:05.683 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-06-23 15:19:05.683 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-06-23 15:19:05.685 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-06-23 15:19:05.685 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47561cba]
2025-06-23 15:19:05.686 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:19:05.686 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-06-23T07: (truncated)...]
2025-06-23 15:19:05.686 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:19:05.687 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:19:05.695 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-06-23 15:19:05.695 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc200ab]
2025-06-23 15:19:05.695 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:19:05.695 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8608734] was not registered for synchronization because synchronization is not active
2025-06-23 15:19:05.696 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1147865547 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:19:05.696 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-06-23 15:19:05.696 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-06-23 15:19:05.707 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-06-23 15:19:05.707 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8608734]
2025-06-23 15:19:05.707 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:19:05.707 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-06-23 15:19:05.708 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:19:05.708 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:17.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-23 15:21:17.610 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-23 15:21:17.610 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:17.610 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:17.611 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-23 15:21:17.611 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-23 15:21:17.719 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:17.719 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:17.721 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:17.721 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:17.721 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@80fe6]
2025-06-23 15:21:17.721 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1251882780 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:21:17.721 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:17.721 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:17.721 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:17.722 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:17.722 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@735f61e2]
2025-06-23 15:21:17.722 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2010879956 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:21:17.722 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:17.722 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@735f61e2]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@80fe6]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@80fe6]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@735f61e2]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@80fe6]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@80fe6]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@735f61e2]
2025-06-23 15:21:17.733 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@735f61e2]
2025-06-23 15:21:17.765 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:17.765 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:17.766 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-23 15:21:17.766 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-23 15:21:17.766 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-23 15:21:17.766 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-23 15:21:17.766 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-23 15:21:17.767 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-23 15:21:17.767 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-23 15:21:17.767 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-23 15:21:17.769 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:17.769 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2694f034] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:17.769 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1460514185 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:21:17.770 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-23 15:21:17.770 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-23 15:21:17.784 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-23 15:21:17.784 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2694f034]
2025-06-23 15:21:17.784 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:17.785 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-23 15:21:17.787 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:17.787 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:17.841 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:17.841 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa736ca] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:17.842 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@813204745 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:17.842 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-23 15:21:17.842 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-23 15:21:17.859 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-23 15:21:17.859 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fa736ca]
2025-06-23 15:21:17.859 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:17.860 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f307dfe] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:17.860 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1905277508 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:17.860 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-23 15:21:17.860 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-23 15:21:17.871 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-23 15:21:17.871 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f307dfe]
2025-06-23 15:21:17.872 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:17.883 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-23 15:21:17.887 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:17.887 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:22.422 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3
2025-06-23 15:21:22.422 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:22.422 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithEpisodes(String, CustomUserDetails)
2025-06-23 15:21:22.530 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:22.532 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2100280e]
2025-06-23 15:21:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@597610472 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:21:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:22.532 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:22.544 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:22.544 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2100280e]
2025-06-23 15:21:22.544 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:22.544 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2100280e]
2025-06-23 15:21:22.544 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2100280e]
2025-06-23 15:21:22.544 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2100280e]
2025-06-23 15:21:22.579 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:22.579 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3] with attributes [permitAll]
2025-06-23 15:21:22.579 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3
2025-06-23 15:21:22.580 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3", parameters={masked}
2025-06-23 15:21:22.580 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithEpisodes(String, CustomUserDetails)
2025-06-23 15:21:22.581 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.581 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@652c8594] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:22.581 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1503108725 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:21:22.581 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-23 15:21:22.581 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 你与我最后的战场，亦或是世界起始的圣战 第二季(String)
2025-06-23 15:21:22.593 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-23 15:21:22.594 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@652c8594]
2025-06-23 15:21:22.594 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.594 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d655002] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:22.594 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@753164319 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:21:22.594 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : ==>  Preparing: SELECT ve.id, ve.video_info_id, ve.number, ve.play_url, ve.duration, ve.created_time, ve.updated_time, COUNT(DISTINCT CASE WHEN vel.status = true THEN vel.id END) AS likes_count, CAST(IFNULL(MAX(CASE WHEN vel.user_id = ? AND vel.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status FROM video_episodes ve LEFT JOIN video_episodes_likes vel ON ve.id = vel.video_episode_id WHERE ve.video_info_id = ? GROUP BY ve.id ORDER BY ve.number ASC
2025-06-23 15:21:22.594 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : ==> Parameters: 23(Long), 9(Long)
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : <==      Total: 8
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d655002]
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9f0644e] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@540947301 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : ==>  Preparing: SELECT vt.name,vt.id FROM video_type vt JOIN video_info_type_link vitl ON vt.id =vitl.type_id WHERE vitl.video_info_id =?
2025-06-23 15:21:22.609 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : ==> Parameters: 9(Long)
2025-06-23 15:21:22.621 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : <==      Total: 7
2025-06-23 15:21:22.622 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9f0644e]
2025-06-23 15:21:22.622 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:22.638 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpi (truncated)...]
2025-06-23 15:21:22.643 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:22.643 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:22.657 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13
2025-06-23 15:21:22.657 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:22.658 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoByTypeWithPagination(PageRequestDTO)
2025-06-23 15:21:22.765 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:22.767 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:22.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2325a563]
2025-06-23 15:21:22.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@895538387 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:21:22.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:22.767 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:22.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:22.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2325a563]
2025-06-23 15:21:22.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:22.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2325a563]
2025-06-23 15:21:22.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2325a563]
2025-06-23 15:21:22.780 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2325a563]
2025-06-23 15:21:22.812 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:22.812 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13] with attributes [permitAll]
2025-06-23 15:21:22.812 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13
2025-06-23 15:21:22.813 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/by-type/pagination?keyword=%E5%86%92%E9%99%A9&pageNum=1&pageSize=13", parameters={masked}
2025-06-23 15:21:22.813 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoByTypeWithPagination(PageRequestDTO)
2025-06-23 15:21:22.815 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.815 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d774a52] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:22.815 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1338925598 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:22.815 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : ==>  Preparing: SELECT vi.id,vi.title,vi.cover_image_url,vi.description FROM video_info vi INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id INNER JOIN video_type vt ON vitl.type_id =vt.id WHERE vt.name = ? LIMIT ?, ?
2025-06-23 15:21:22.815 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : ==> Parameters: 冒险(String), 0(Integer), 13(Integer)
2025-06-23 15:21:22.828 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : <==      Total: 12
2025-06-23 15:21:22.829 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d774a52]
2025-06-23 15:21:22.829 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:22.829 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67b060fd] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:22.829 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1387603401 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:22.829 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.countVideoInfoByType : ==>  Preparing: SELECT COUNT(*) FROM video_info vi INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id INNER JOIN video_type vt ON vitl.type_id =vt.id WHERE vt.name = ?
2025-06-23 15:21:22.829 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.countVideoInfoByType : ==> Parameters: 冒险(String)
2025-06-23 15:21:22.840 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.countVideoInfoByType : <==      Total: 1
2025-06-23 15:21:22.841 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67b060fd]
2025-06-23 15:21:22.841 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:22.842 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-23 15:21:22.844 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:22.844 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:27.493 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-23 15:21:27.493 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3
2025-06-23 15:21:27.493 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:27.493 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:27.494 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-23 15:21:27.494 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithEpisodes(String, CustomUserDetails)
2025-06-23 15:21:27.604 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:27.604 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:27.606 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:27.606 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47d85221]
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@46833753]
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@511893302 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1896390094 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:27.606 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47d85221]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@46833753]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47d85221]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@46833753]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47d85221]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@46833753]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47d85221]
2025-06-23 15:21:27.617 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@46833753]
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100] with attributes [authenticated]
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3] with attributes [permitAll]
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments?videoEpisodesId=562&pageNum=1&pageSize=100", parameters={masked}
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo-with-episodes?title=%E4%BD%A0%E4%B8%8E%E6%88%91%E6%9C%80%E5%90%8E%E7%9A%84%E6%88%98%E5%9C%BA%EF%BC%8C%E4%BA%A6%E6%88%96%E6%98%AF%E4%B8%96%E7%95%8C%E8%B5%B7%E5%A7%8B%E7%9A%84%E5%9C%A3%E6%88%98%20%E7%AC%AC%E4%BA%8C%E5%AD%A3", parameters={masked}
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentsByVideoEpisodesId(VideoComentsPageRequest, CustomUserDetails)
2025-06-23 15:21:27.648 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithEpisodes(String, CustomUserDetails)
2025-06-23 15:21:27.649 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.649 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4563cdbe] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:27.649 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1478099235 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:27.649 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-23 15:21:27.649 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 你与我最后的战场，亦或是世界起始的圣战 第二季(String)
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4563cdbe]
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e4e4b72] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@890991928 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : ==>  Preparing: SELECT ve.id, ve.video_info_id, ve.number, ve.play_url, ve.duration, ve.created_time, ve.updated_time, COUNT(DISTINCT CASE WHEN vel.status = true THEN vel.id END) AS likes_count, CAST(IFNULL(MAX(CASE WHEN vel.user_id = ? AND vel.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status FROM video_episodes ve LEFT JOIN video_episodes_likes vel ON ve.id = vel.video_episode_id WHERE ve.video_info_id = ? GROUP BY ve.id ORDER BY ve.number ASC
2025-06-23 15:21:27.660 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : ==> Parameters: 23(Long), 9(Long)
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoEpisodesByVideoInfoId : <==      Total: 8
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e4e4b72]
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@70fe7c96] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1718065351 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : ==>  Preparing: SELECT vt.name,vt.id FROM video_type vt JOIN video_info_type_link vitl ON vt.id =vitl.type_id WHERE vitl.video_info_id =?
2025-06-23 15:21:27.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : ==> Parameters: 9(Long)
2025-06-23 15:21:27.676 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.676 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202]
2025-06-23 15:21:27.677 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1514780093 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:21:27.677 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.V.getBasicComments : ==>  Preparing: SELECT vc.id, vc.video_episodes_id, vc.user_id, vc.parent_comment_id, vc.content, vc.created_time, vc.updated_time, up.username, up.avatar FROM video_comments vc INNER JOIN user_profile up ON vc.user_id = up.id WHERE vc.video_episodes_id = ? ORDER BY vc.created_time DESC LIMIT ?, ?
2025-06-23 15:21:27.677 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.V.getBasicComments : ==> Parameters: 562(Long), 0(Integer), 100(Integer)
2025-06-23 15:21:27.685 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByLinkWithVideoId : <==      Total: 7
2025-06-23 15:21:27.685 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@70fe7c96]
2025-06-23 15:21:27.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:27.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpi (truncated)...]
2025-06-23 15:21:27.688 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:27.688 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:27.691 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.V.getBasicComments : <==      Total: 11
2025-06-23 15:21:27.691 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202]
2025-06-23 15:21:27.691 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202] from current transaction
2025-06-23 15:21:27.693 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.V.getCommentsLikeData : ==>  Preparing: SELECT vcl.comment_id, COUNT(CASE WHEN vcl.type = 'like' AND vcl.status = true THEN 1 END) as likes_count, COUNT(CASE WHEN vcl.type = 'dislike' AND vcl.status = true THEN 1 END) as dislikes_count, MAX(CASE WHEN vcl.user_id = ? AND vcl.status = true THEN vcl.type END) as user_like_type FROM video_comment_likes vcl WHERE vcl.comment_id IN ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) GROUP BY vcl.comment_id
2025-06-23 15:21:27.694 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.V.getCommentsLikeData : ==> Parameters: 23(Long), 29(Long), 28(Long), 15(Long), 14(Long), 13(Long), 12(Long), 11(Long), 10(Long), 9(Long), 7(Long), 1(Long)
2025-06-23 15:21:27.707 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.V.getCommentsLikeData : <==      Total: 6
2025-06-23 15:21:27.707 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202]
2025-06-23 15:21:27.708 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202]
2025-06-23 15:21:27.708 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202]
2025-06-23 15:21:27.708 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78b21202]
2025-06-23 15:21:27.727 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E6%97%A5%E6%9C%AC%E5%8A%A8%E6%BC%AB&pageNum=1&pageSize=20
2025-06-23 15:21:27.727 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:27.727 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoByTypeWithPagination(PageRequestDTO)
2025-06-23 15:21:27.741 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:27.755 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取视频评论成功, success=true, data=PageFinalResult(list=[VideoCommentsDTO(id=29,  (truncated)...]
2025-06-23 15:21:27.756 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:27.757 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:27.772 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-23 15:21:27.773 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:21:27.773 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-23 15:21:27.830 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:27.833 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:27.833 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.833 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27409af3]
2025-06-23 15:21:27.833 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@523484912 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:21:27.833 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:27.833 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:27.845 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:27.845 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27409af3]
2025-06-23 15:21:27.845 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:27.845 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27409af3]
2025-06-23 15:21:27.845 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27409af3]
2025-06-23 15:21:27.845 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27409af3]
2025-06-23 15:21:27.880 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:27.880 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E6%97%A5%E6%9C%AC%E5%8A%A8%E6%BC%AB&pageNum=1&pageSize=20] with attributes [permitAll]
2025-06-23 15:21:27.880 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/by-type/pagination?keyword=%E6%97%A5%E6%9C%AC%E5%8A%A8%E6%BC%AB&pageNum=1&pageSize=20
2025-06-23 15:21:27.881 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/by-type/pagination?keyword=%E6%97%A5%E6%9C%AC%E5%8A%A8%E6%BC%AB&pageNum=1&pageSize=20", parameters={masked}
2025-06-23 15:21:27.881 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoByTypeWithPagination(PageRequestDTO)
2025-06-23 15:21:27.882 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.882 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78a314fd] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:27.882 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1576882515 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:21:27.882 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : ==>  Preparing: SELECT vi.id,vi.title,vi.cover_image_url,vi.description FROM video_info vi INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id INNER JOIN video_type vt ON vitl.type_id =vt.id WHERE vt.name = ? LIMIT ?, ?
2025-06-23 15:21:27.882 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : ==> Parameters: 日本动漫(String), 0(Integer), 20(Integer)
2025-06-23 15:21:27.884 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:21:27.887 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:21:27.887 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.887 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cdf19e7]
2025-06-23 15:21:27.887 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1803871605 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:21:27.887 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:21:27.887 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:21:27.899 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:21:27.899 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cdf19e7]
2025-06-23 15:21:27.899 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:21:27.899 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cdf19e7]
2025-06-23 15:21:27.899 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cdf19e7]
2025-06-23 15:21:27.899 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cdf19e7]
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTypeWithPagination : <==      Total: 20
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@78a314fd]
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@381815c1] was not registered for synchronization because synchronization is not active
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1492974275 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.countVideoInfoByType : ==>  Preparing: SELECT COUNT(*) FROM video_info vi INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id INNER JOIN video_type vt ON vitl.type_id =vt.id WHERE vt.name = ?
2025-06-23 15:21:27.907 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.countVideoInfoByType : ==> Parameters: 日本动漫(String)
2025-06-23 15:21:27.919 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.countVideoInfoByType : <==      Total: 1
2025-06-23 15:21:27.919 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@381815c1]
2025-06-23 15:21:27.919 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:21:27.919 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-23 15:21:27.921 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:21:27.921 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:21:27.929 [39mDEBUG[0;39m [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:21:27.929 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] with attributes [authenticated]
2025-06-23 15:21:27.929 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-23 15:21:27.929 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/view/images/image-general/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png", parameters={}
2025-06-23 15:21:27.930 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureImageFileController#viewImage(String, HttpHeaders, Authentication)
2025-06-23 15:21:27.934 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.c.PureImageFileController : 请求图片查看（传统方式）: 60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png
2025-06-23 15:21:27.938 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.s.impl.FilePureServiceImpl : 探测到文件 '60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png' 的类型为 'image/png'
2025-06-23 15:21:27.945 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:image/png' in response
2025-06-23 15:21:27.950 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [URL [file:D:/upload/png/60d7f80e-d334-42b9-b509-6eaefb8c3c6a.png] (range: 0 to 220760)]
2025-06-23 15:21:27.953 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 206 PARTIAL_CONTENT
2025-06-23 15:21:27.953 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:22:26.536 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing POST /api/video/comments/like
2025-06-23 15:22:26.536 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:22:26.536 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#likeComment(CommentLikeRequest, CustomUserDetails)
2025-06-23 15:22:26.644 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:22:26.647 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:22:26.647 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:26.647 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@459a0f5d]
2025-06-23 15:22:26.647 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@711858983 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:22:26.647 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:22:26.647 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:22:26.658 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:22:26.658 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@459a0f5d]
2025-06-23 15:22:26.659 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:22:26.659 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@459a0f5d]
2025-06-23 15:22:26.659 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@459a0f5d]
2025-06-23 15:22:26.659 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@459a0f5d]
2025-06-23 15:22:26.690 [39mDEBUG[0;39m [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:22:26.690 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/video/comments/like] with attributes [authenticated]
2025-06-23 15:22:26.690 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured POST /api/video/comments/like
2025-06-23 15:22:26.690 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : POST "/api/video/comments/like", parameters={}
2025-06-23 15:22:26.690 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#likeComment(CommentLikeRequest, CustomUserDetails)
2025-06-23 15:22:26.694 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [CommentLikeRequest(commentId=29, type=dislike)]
2025-06-23 15:22:26.709 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:26.709 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.709 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2121672349 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:22:26.709 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.getUserIdByCommentId : ==>  Preparing: SELECT user_id FROM video_comments WHERE id = ?
2025-06-23 15:22:26.709 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.getUserIdByCommentId : ==> Parameters: 29(Long)
2025-06-23 15:22:26.719 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.getUserIdByCommentId : <==      Total: 1
2025-06-23 15:22:26.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced] from current transaction
2025-06-23 15:22:26.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.findByCommentIdAndUserId : ==>  Preparing: SELECT * FROM video_comment_likes WHERE comment_id = ? AND user_id = ?
2025-06-23 15:22:26.720 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.findByCommentIdAndUserId : ==> Parameters: 29(Long), 23(Long)
2025-06-23 15:22:26.743 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.findByCommentIdAndUserId : <==      Total: 0
2025-06-23 15:22:26.744 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.744 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced] from current transaction
2025-06-23 15:22:26.744 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.insert : ==>  Preparing: INSERT INTO video_comment_likes (comment_id, user_id, status, type, created_time, updated_time) VALUES (?, ?, ?, ?, ?, ?)
2025-06-23 15:22:26.745 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.insert : ==> Parameters: 29(Long), 23(Long), true(Boolean), dislike(String), 2025-06-23 15:22:26.7446437(Timestamp), 2025-06-23 15:22:26.7446437(Timestamp)
2025-06-23 15:22:26.770 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.V.insert : <==    Updates: 1
2025-06-23 15:22:26.772 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.772 [34mINFO [0;39m [http-nio-8080-exec-4] c.e.p.s.i.VideoCommentInteractionServiceImpl : 用户 23 首次对评论 29 进行操作: dislike
2025-06-23 15:22:26.772 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.772 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.772 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2be67ced]
2025-06-23 15:22:26.795 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:22:26.795 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=null, time=2025-06-23T07:22:26.794644200Z)]
2025-06-23 15:22:26.796 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:22:26.796 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:22:27.113 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/29/likes
2025-06-23 15:22:27.113 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:22:27.114 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentLikeInfo(Long, CustomUserDetails)
2025-06-23 15:22:27.215 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:22:27.217 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:22:27.218 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:27.218 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55a39324]
2025-06-23 15:22:27.218 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@254717510 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:22:27.218 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:22:27.218 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:22:27.229 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:22:27.230 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55a39324]
2025-06-23 15:22:27.230 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:22:27.230 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55a39324]
2025-06-23 15:22:27.230 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55a39324]
2025-06-23 15:22:27.230 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55a39324]
2025-06-23 15:22:27.263 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:22:27.264 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/29/likes] with attributes [authenticated]
2025-06-23 15:22:27.264 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/29/likes
2025-06-23 15:22:27.264 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/29/likes", parameters={}
2025-06-23 15:22:27.264 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentLikeInfo(Long, CustomUserDetails)
2025-06-23 15:22:27.265 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:27.265 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62464d85] was not registered for synchronization because synchronization is not active
2025-06-23 15:22:27.265 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@888219333 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:22:27.265 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==>  Preparing: SELECT count(*) FROM video_comment_likes WHERE comment_id = ? AND type = ? AND status = ?
2025-06-23 15:22:27.266 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==> Parameters: 29(Long), like(String), true(Boolean)
2025-06-23 15:22:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : <==      Total: 1
2025-06-23 15:22:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62464d85]
2025-06-23 15:22:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:27.277 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b6cb44c] was not registered for synchronization because synchronization is not active
2025-06-23 15:22:27.278 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@454484673 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:22:27.278 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==>  Preparing: SELECT count(*) FROM video_comment_likes WHERE comment_id = ? AND type = ? AND status = ?
2025-06-23 15:22:27.278 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==> Parameters: 29(Long), dislike(String), true(Boolean)
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : <==      Total: 1
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b6cb44c]
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@575e05dc] was not registered for synchronization because synchronization is not active
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2034107823 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.findByCommentIdAndUserId : ==>  Preparing: SELECT * FROM video_comment_likes WHERE comment_id = ? AND user_id = ?
2025-06-23 15:22:27.290 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.findByCommentIdAndUserId : ==> Parameters: 29(Long), 23(Long)
2025-06-23 15:22:27.305 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.V.findByCommentIdAndUserId : <==      Total: 1
2025-06-23 15:22:27.306 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@575e05dc]
2025-06-23 15:22:27.306 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:22:27.310 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取点赞信息成功, success=true, data=CommentLikeInfoDTO(likeCount=1, dislikeCount=1 (truncated)...]
2025-06-23 15:22:27.312 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:22:27.312 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:22:32.684 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing POST /api/video/comments/like
2025-06-23 15:22:32.684 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:22:32.684 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#likeComment(CommentLikeRequest, CustomUserDetails)
2025-06-23 15:22:32.835 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:22:32.838 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:22:32.838 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:32.838 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362d61b7]
2025-06-23 15:22:32.838 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1335351360 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:22:32.839 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:22:32.839 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:22:32.850 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:22:32.850 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362d61b7]
2025-06-23 15:22:32.850 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:22:32.850 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362d61b7]
2025-06-23 15:22:32.850 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362d61b7]
2025-06-23 15:22:32.850 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@362d61b7]
2025-06-23 15:22:32.882 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:22:32.882 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/video/comments/like] with attributes [authenticated]
2025-06-23 15:22:32.883 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured POST /api/video/comments/like
2025-06-23 15:22:32.883 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : POST "/api/video/comments/like", parameters={}
2025-06-23 15:22:32.883 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#likeComment(CommentLikeRequest, CustomUserDetails)
2025-06-23 15:22:32.884 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [CommentLikeRequest(commentId=29, type=dislike)]
2025-06-23 15:22:32.895 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:32.895 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.895 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@491536982 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:22:32.895 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.getUserIdByCommentId : ==>  Preparing: SELECT user_id FROM video_comments WHERE id = ?
2025-06-23 15:22:32.895 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.getUserIdByCommentId : ==> Parameters: 29(Long)
2025-06-23 15:22:32.906 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.getUserIdByCommentId : <==      Total: 1
2025-06-23 15:22:32.907 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.907 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb] from current transaction
2025-06-23 15:22:32.907 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.findByCommentIdAndUserId : ==>  Preparing: SELECT * FROM video_comment_likes WHERE comment_id = ? AND user_id = ?
2025-06-23 15:22:32.907 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.findByCommentIdAndUserId : ==> Parameters: 29(Long), 23(Long)
2025-06-23 15:22:32.919 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.findByCommentIdAndUserId : <==      Total: 1
2025-06-23 15:22:32.919 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.919 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb] from current transaction
2025-06-23 15:22:32.919 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.update : ==>  Preparing: UPDATE video_comment_likes SET status = ?, type = ?, updated_time = ? WHERE id = ?
2025-06-23 15:22:32.919 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.update : ==> Parameters: false(Boolean), dislike(String), 2025-06-23 15:22:32.9195187(Timestamp), 7(Long)
2025-06-23 15:22:32.941 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.V.update : <==    Updates: 1
2025-06-23 15:22:32.941 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.941 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.i.VideoCommentInteractionServiceImpl : 用户 23 更新了对评论 29 的操作状态: type=dislike, status=false
2025-06-23 15:22:32.941 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.941 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.941 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64c02eb]
2025-06-23 15:22:32.965 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:22:32.965 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=null, time=2025-06-23T07:22:32.965520900Z)]
2025-06-23 15:22:32.966 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:22:32.966 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:22:33.313 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/video/comments/29/likes
2025-06-23 15:22:33.313 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:22:33.313 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentLikeInfo(Long, CustomUserDetails)
2025-06-23 15:22:33.419 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:22:33.421 [34mINFO [0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:22:33.421 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:33.421 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10e3fd24]
2025-06-23 15:22:33.421 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@690884305 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will be managed by Spring
2025-06-23 15:22:33.421 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:22:33.421 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:22:33.430 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:22:33.431 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10e3fd24]
2025-06-23 15:22:33.431 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:22:33.431 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10e3fd24]
2025-06-23 15:22:33.431 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10e3fd24]
2025-06-23 15:22:33.431 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@10e3fd24]
2025-06-23 15:22:33.465 [39mDEBUG[0;39m [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:22:33.465 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/video/comments/29/likes] with attributes [authenticated]
2025-06-23 15:22:33.465 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/video/comments/29/likes
2025-06-23 15:22:33.465 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/video/comments/29/likes", parameters={}
2025-06-23 15:22:33.465 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VideoCommentInteractionController#getCommentLikeInfo(Long, CustomUserDetails)
2025-06-23 15:22:33.466 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:33.466 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e48bfb6] was not registered for synchronization because synchronization is not active
2025-06-23 15:22:33.466 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@83828704 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:22:33.466 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==>  Preparing: SELECT count(*) FROM video_comment_likes WHERE comment_id = ? AND type = ? AND status = ?
2025-06-23 15:22:33.466 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==> Parameters: 29(Long), like(String), true(Boolean)
2025-06-23 15:22:33.478 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : <==      Total: 1
2025-06-23 15:22:33.478 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e48bfb6]
2025-06-23 15:22:33.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:33.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779a44b1] was not registered for synchronization because synchronization is not active
2025-06-23 15:22:33.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2091184379 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:22:33.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==>  Preparing: SELECT count(*) FROM video_comment_likes WHERE comment_id = ? AND type = ? AND status = ?
2025-06-23 15:22:33.479 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : ==> Parameters: 29(Long), dislike(String), true(Boolean)
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.countByCommentIdAndTypeAndStatus : <==      Total: 1
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@779a44b1]
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b840632] was not registered for synchronization because synchronization is not active
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@245260351 wrapping com.mysql.cj.jdbc.ConnectionImpl@f9c3aa2] will not be managed by Spring
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.findByCommentIdAndUserId : ==>  Preparing: SELECT * FROM video_comment_likes WHERE comment_id = ? AND user_id = ?
2025-06-23 15:22:33.490 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.findByCommentIdAndUserId : ==> Parameters: 29(Long), 23(Long)
2025-06-23 15:22:33.501 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.V.findByCommentIdAndUserId : <==      Total: 1
2025-06-23 15:22:33.501 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b840632]
2025-06-23 15:22:33.501 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:22:33.501 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=获取点赞信息成功, success=true, data=CommentLikeInfoDTO(likeCount=1, dislikeCount=0 (truncated)...]
2025-06-23 15:22:33.502 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:22:33.502 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:23:20.668 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-23 15:23:20.668 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:23:20.668 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-23 15:23:20.776 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:23:20.778 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:23:20.778 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:23:20.778 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7255b6f9]
2025-06-23 15:23:20.778 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@870891317 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:23:20.778 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:23:20.779 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:23:20.789 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:23:20.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7255b6f9]
2025-06-23 15:23:20.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:23:20.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7255b6f9]
2025-06-23 15:23:20.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7255b6f9]
2025-06-23 15:23:20.790 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7255b6f9]
2025-06-23 15:23:20.819 [39mDEBUG[0;39m [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:23:20.820 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-23 15:23:20.820 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-23 15:23:20.820 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-23 15:23:20.821 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-23 15:23:20.821 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:23:20.821 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ee4b08] was not registered for synchronization because synchronization is not active
2025-06-23 15:23:20.821 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@381510411 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:23:20.821 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-23 15:23:20.821 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-23 15:23:20.833 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-23 15:23:20.833 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45ee4b08]
2025-06-23 15:23:20.833 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:23:20.834 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-23 15:23:20.834 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:23:20.835 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:23:20.981 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-23 15:23:20.981 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 15:23:20.981 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-23 15:23:21.079 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 15:23:21.081 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 15:23:21.081 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:23:21.081 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bc553d2]
2025-06-23 15:23:21.082 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@278711080 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will be managed by Spring
2025-06-23 15:23:21.082 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 15:23:21.082 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 15:23:21.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 15:23:21.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bc553d2]
2025-06-23 15:23:21.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 15:23:21.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bc553d2]
2025-06-23 15:23:21.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bc553d2]
2025-06-23 15:23:21.094 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bc553d2]
2025-06-23 15:23:21.124 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 15:23:21.125 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-23 15:23:21.125 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-23 15:23:21.125 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-23 15:23:21.125 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-23 15:23:21.126 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:23:21.126 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e551b90] was not registered for synchronization because synchronization is not active
2025-06-23 15:23:21.126 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1914804599 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:23:21.126 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-23 15:23:21.126 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-23 15:23:21.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-23 15:23:21.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e551b90]
2025-06-23 15:23:21.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 15:23:21.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11e0984f] was not registered for synchronization because synchronization is not active
2025-06-23 15:23:21.139 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1394364186 wrapping com.mysql.cj.jdbc.ConnectionImpl@2f6860d2] will not be managed by Spring
2025-06-23 15:23:21.140 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-23 15:23:21.140 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-23 15:23:21.150 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-23 15:23:21.150 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11e0984f]
2025-06-23 15:23:21.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 15:23:21.151 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-23 15:23:21.153 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 15:23:21.153 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 15:37:36.068 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-23 16:07:36.074 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-23 16:37:36.085 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-06-23 17:07:36.091 [34mINFO [0;39m [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-06-23 17:37:36.097 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-06-23 18:07:36.099 [34mINFO [0;39m [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-06-23 18:17:17.514 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-06-23 18:17:17.514 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-23 18:17:17.514 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 18:17:17.514 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-23 18:17:17.515 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-23 18:17:17.515 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-23 18:17:17.643 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 18:17:17.643 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-06-23 18:17:17.647 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 18:17:17.647 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6]
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1211428489 wrapping com.mysql.cj.jdbc.ConnectionImpl@24505149] will be managed by Spring
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b]
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1285161697 wrapping com.mysql.cj.jdbc.ConnectionImpl@426e3843] will be managed by Spring
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-23 18:17:17.648 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-06-23 18:17:17.662 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-23 18:17:17.662 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-23 18:17:17.662 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6]
2025-06-23 18:17:17.662 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b]
2025-06-23 18:17:17.662 [34mINFO [0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 18:17:17.662 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-06-23 18:17:17.662 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b] from current transaction
2025-06-23 18:17:17.662 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6] from current transaction
2025-06-23 18:17:17.663 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 18:17:17.663 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-23 18:17:17.663 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 18:17:17.663 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-06-23 18:17:17.674 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 18:17:17.674 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@200dc4c6]
2025-06-23 18:17:17.675 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cddd28b]
2025-06-23 18:17:17.712 [39mDEBUG[0;39m [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 18:17:17.712 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-06-23 18:17:17.713 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/type] with attributes [permitAll]
2025-06-23 18:17:17.713 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12] with attributes [permitAll]
2025-06-23 18:17:17.713 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/type
2025-06-23 18:17:17.713 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-06-23 18:17:17.713 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/type", parameters={}
2025-06-23 18:17:17.713 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12", parameters={masked}
2025-06-23 18:17:17.714 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoAllType()
2025-06-23 18:17:17.714 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-06-23 18:17:17.714 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 18:17:17.714 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1171497e] was not registered for synchronization because synchronization is not active
2025-06-23 18:17:17.714 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1473625011 wrapping com.mysql.cj.jdbc.ConnectionImpl@24505149] will not be managed by Spring
2025-06-23 18:17:17.714 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findAllVideoType : ==>  Preparing: SELECT * FROM video_type
2025-06-23 18:17:17.715 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findAllVideoType : ==> Parameters: 
2025-06-23 18:17:17.716 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 18:17:17.716 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d9baa4] was not registered for synchronization because synchronization is not active
2025-06-23 18:17:17.716 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1208724683 wrapping com.mysql.cj.jdbc.ConnectionImpl@426e3843] will not be managed by Spring
2025-06-23 18:17:17.716 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoWithPagination : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info LIMIT ?, ?
2025-06-23 18:17:17.716 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoWithPagination : ==> Parameters: 0(Integer), 12(Integer)
2025-06-23 18:17:17.728 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findAllVideoType : <==      Total: 51
2025-06-23 18:17:17.728 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1171497e]
2025-06-23 18:17:17.728 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 18:17:17.729 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频类型查询成功, success=true, data=PageFinalResult(list=[VideoType(id=29, name=20 (truncated)...]
2025-06-23 18:17:17.730 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 18:17:17.730 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 18:17:17.731 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoWithPagination : <==      Total: 12
2025-06-23 18:17:17.731 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d9baa4]
2025-06-23 18:17:17.732 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-23 18:17:17.732 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@581db20a] was not registered for synchronization because synchronization is not active
2025-06-23 18:17:17.732 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@307965751 wrapping com.mysql.cj.jdbc.ConnectionImpl@426e3843] will not be managed by Spring
2025-06-23 18:17:17.732 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.countVideoInfo : ==>  Preparing: SELECT COUNT(*) FROM video_info
2025-06-23 18:17:17.732 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.countVideoInfo : ==> Parameters: 
2025-06-23 18:17:17.745 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.countVideoInfo : <==      Total: 1
2025-06-23 18:17:17.745 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@581db20a]
2025-06-23 18:17:17.746 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-23 18:17:17.746 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频查询成功, success=true, data=PageFinalResult(list=[VideoInfo(id=9, title=你与我最 (truncated)...]
2025-06-23 18:17:17.748 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-23 18:17:17.748 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-23 18:37:36.099 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-06-23 19:07:36.105 [34mINFO [0;39m [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-06-23 19:37:36.105 [34mINFO [0;39m [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
2025-06-23 20:07:36.121 [34mINFO [0;39m [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
2025-06-23 20:37:36.132 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
2025-06-23 21:07:36.138 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 12]
2025-06-23 21:37:36.145 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 13]
2025-06-23 22:07:36.154 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 14]
2025-06-23 22:37:36.157 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 15]
2025-06-23 23:07:36.168 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 16]
2025-06-23 23:37:36.183 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 17]
