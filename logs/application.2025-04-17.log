2025-04-17 16:36:55.442 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-17 16:36:55.444 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 22672 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-17 16:36:55.446 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-17 16:36:55.447 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-17 16:36:56.300 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-17 16:36:56.302 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-17 16:36:56.327 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-04-17 16:36:56.411 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-17 16:36:56.411 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-17 16:36:56.411 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-17 16:36:56.412 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-17 16:36:56.413 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-17 16:36:56.413 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-17 16:36:56.413 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-17 16:36:56.413 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-17 16:36:56.414 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-17 16:36:56.848 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-17 16:36:56.852 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-17 16:36:56.853 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-17 16:36:56.854 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-17 16:36:56.931 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-17 16:36:56.931 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1441 ms
2025-04-17 16:36:57.118 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-17 16:36:57.129 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-17 16:36:57.141 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-17 16:36:57.153 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-17 16:36:57.275 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-17 16:36:57.761 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-17 16:36:57.929 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-17 16:36:57.931 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:57.939 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-04-17 16:36:57.939 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:57.940 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-17 16:36:57.941 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 16:36:58.027 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-17 16:36:58.028 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-17 16:36:58.032 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@3915e7c3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@167a21b, org.springframework.security.web.context.SecurityContextPersistenceFilter@427308f8, org.springframework.security.web.header.HeaderWriterFilter@28f154cc, org.springframework.security.web.authentication.logout.LogoutFilter@282c4da0, com.example.pure.filter.JwtFilter@29d25e65, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4975dda1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5463f035, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c0df4ab, org.springframework.security.web.session.SessionManagementFilter@1d60059f, org.springframework.security.web.access.ExceptionTranslationFilter@1ec88aa1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@716f94c1]
2025-04-17 16:36:58.033 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-17 16:36:58.034 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-17 16:36:58.219 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-17 16:36:58.249 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-17 16:36:58.398 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-17 16:36:58.414 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-17 16:36:59.318 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-17 16:36:59.429 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-17 16:36:59.452 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-17 16:36:59.453 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-17 16:36:59.454 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-17 16:36:59.454 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-17 16:36:59.454 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-17 16:36:59.454 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-17 16:36:59.454 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-17 16:36:59.454 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-17 16:36:59.454 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@32b46831, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5353dd09, org.springframework.security.web.context.SecurityContextPersistenceFilter@6c07add6, org.springframework.security.web.header.HeaderWriterFilter@267ff4df, org.springframework.web.filter.CorsFilter@320ff86f, org.springframework.security.web.authentication.logout.LogoutFilter@2094bf3d, com.example.pure.filter.JwtFilter@29d25e65, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@e26a3df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@57a667c8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@192b472d, org.springframework.security.web.session.SessionManagementFilter@7c4b5ceb, org.springframework.security.web.access.ExceptionTranslationFilter@4e8598d9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7bfa1eb5]
2025-04-17 16:36:59.484 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@15d49048, started on Thu Apr 17 16:36:55 CST 2025
2025-04-17 16:36:59.495 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-17 16:36:59.495 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-17 16:36:59.495 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-17 16:36:59.495 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-17 16:36:59.495 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-17 16:36:59.495 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-17 16:36:59.497 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-17 16:36:59.498 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-17 16:36:59.498 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-17 16:36:59.498 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
2025-04-17 16:36:59.498 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-17 16:36:59.499 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-17 16:36:59.499 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-17 16:36:59.499 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-17 16:36:59.534 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-17 16:36:59.559 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-17 16:36:59.750 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-17 16:36:59.917 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-17 16:36:59.919 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 16:36:59.919 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 16:36:59.919 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-17 16:36:59.919 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de30c31]
2025-04-17 16:36:59.919 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de30c31]
2025-04-17 16:36:59.919 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de30c31]]
2025-04-17 16:36:59.919 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-17 16:36:59.920 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 16:36:59.920 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 16:36:59.931 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 4.923 seconds (JVM running for 5.567)
2025-04-17 16:37:59.346 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-17 16:37:59.347 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-17 16:37:59.347 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-17 16:37:59.347 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-17 16:37:59.347 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-17 16:37:59.348 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6151c1f0
2025-04-17 16:37:59.348 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@2e2e0496
2025-04-17 16:37:59.348 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-17 16:37:59.348 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-17 16:37:59.357 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-17 16:37:59.359 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 16:37:59.366 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-17 16:37:59.368 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-17 16:37:59.372 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-17 16:37:59.372 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-17 16:37:59.374 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-17 16:37:59.375 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-17 16:37:59.423 [http-nio-8080-exec-2] DEBUG o.s.web.method.HandlerMethod - Could not resolve parameter [0] in public com.example.pure.common.Result<com.example.pure.model.dto.TokenResponse> com.example.pure.controller.AuthController.login(com.example.pure.model.dto.LoginRequest,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse): JSON parse error: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value; nested exception is com.fasterxml.jackson.core.JsonParseException: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 2, column: 16]
2025-04-17 16:37:59.424 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-04-17 16:37:59.426 [http-nio-8080-exec-2] ERROR c.e.d.e.GlobalExceptionHandler - 系统异常
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value; nested exception is com.fasterxml.jackson.core.JsonParseException: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 2, column: 16]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:391)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:343)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:160)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.fasterxml.jackson.core.JsonParseException: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 2, column: 16]
	at com.fasterxml.jackson.core.JsonParser._constructError(JsonParser.java:2391)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportError(ParserMinimalBase.java:735)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportUnexpectedChar(ParserMinimalBase.java:659)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._skipColon2(UTF8StreamJsonParser.java:3188)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._skipColon(UTF8StreamJsonParser.java:3166)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextToken(UTF8StreamJsonParser.java:802)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3682)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:380)
	... 101 common frames omitted
2025-04-17 16:37:59.439 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 16:37:59.441 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [Result(code=500, message=系统异常，请联系管理员, data=null)]
2025-04-17 16:37:59.452 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value; nested exception is com.fasterxml.jackson.core.JsonParseException: Unexpected character ('ï' (code 239)): was expecting a colon to separate field name and value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 2, column: 16]]
2025-04-17 16:37:59.452 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 500 INTERNAL_SERVER_ERROR
2025-04-17 16:37:59.453 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 16:37:59.480 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-17 16:39:48.811 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-17 16:39:48.812 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=23adfa126662, password=12345678, email=null)]
2025-04-17 16:39:49.323 [http-nio-8080-exec-4] INFO  c.e.demo13.controller.AuthController - 用户登录: 23adfa126662
2025-04-17 16:39:49.325 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.AuthServiceImpl - 处理用户登录请求: 23adfa126662
2025-04-17 16:39:49.398 [http-nio-8080-exec-4] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-17 16:39:49.409 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 16:39:49.412 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171]
2025-04-17 16:39:49.418 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@2322c1b1] will be managed by Spring
2025-04-17 16:39:49.420 [http-nio-8080-exec-4] DEBUG c.e.d.m.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, /* Check if password is loaded */ email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-17 16:39:49.436 [http-nio-8080-exec-4] DEBUG c.e.d.m.UserMapper.findByUsername - ==> Parameters: 23adfa126662(String)
2025-04-17 16:39:49.454 [http-nio-8080-exec-4] DEBUG c.e.d.m.UserMapper.findByUsername - <==      Total: 1
2025-04-17 16:39:49.455 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171]
2025-04-17 16:39:49.456 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171] from current transaction
2025-04-17 16:39:49.457 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-17 16:39:49.457 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-17 16:39:49.459 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-17 16:39:49.459 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171]
2025-04-17 16:39:49.459 [http-nio-8080-exec-4] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-17 16:39:49.460 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171]
2025-04-17 16:39:49.460 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171]
2025-04-17 16:39:49.460 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@245fa171]
2025-04-17 16:39:49.721 [http-nio-8080-exec-4] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-04-17 16:39:49.787 [http-nio-8080-exec-4] INFO  c.e.d.service.impl.DeviceServiceImpl - 用户 23adfa126662 添加设备 eee7e57c-f1c2-4aa6-9d5d-dc1566e4b9e8 成功，使用token作为标识
2025-04-17 16:39:49.795 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-17 16:39:49.800 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 更新用户基本信息: 23adfa126662
2025-04-17 16:39:49.800 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 1
2025-04-17 16:39:49.801 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 16:39:49.801 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d]
2025-04-17 16:39:49.801 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@2322c1b1] will be managed by Spring
2025-04-17 16:39:49.801 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.UserMapper.findById - ==>  Preparing: SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE id = ?
2025-04-17 16:39:49.802 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.UserMapper.findById - ==> Parameters: 1(Long)
2025-04-17 16:39:49.802 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.UserMapper.findById - <==      Total: 1
2025-04-17 16:39:49.803 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d]
2025-04-17 16:39:49.803 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d] from current transaction
2025-04-17 16:39:49.816 [http-nio-8080-exec-4] DEBUG c.e.demo13.mapper.UserMapper.update - ==>  Preparing: UPDATE user SET username = ?, password = ?, email = ?, nickname = ?, avatar = ?, gender = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, update_time = NOW() WHERE id = ?
2025-04-17 16:39:49.816 [http-nio-8080-exec-4] DEBUG c.e.demo13.mapper.UserMapper.update - ==> Parameters: 23adfa126662(String), $2a$12$zZJ.NzKo/56aVFdL5CCu7up/dX2v.BH0sr3KHGCIake0V/UYbH2oS(String), <EMAIL>(String), nihaohao(String), a(String), 1(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-04-17T16:39:49.800(LocalDateTime), eyJhbGciOiJIUzI1NiJ9.***************************************************************************.BnEX8giRTWAUIjNzXLIDpAwBkTklFWlzRV4z2BZ93n4(String), 1(Long)
2025-04-17 16:39:49.820 [http-nio-8080-exec-4] DEBUG c.e.demo13.mapper.UserMapper.update - <==    Updates: 1
2025-04-17 16:39:49.820 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d]
2025-04-17 16:39:49.821 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 用户基本信息更新成功: 23adfa126662
2025-04-17 16:39:49.821 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d]
2025-04-17 16:39:49.821 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d]
2025-04-17 16:39:49.821 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28ae775d]
2025-04-17 16:39:49.828 [http-nio-8080-exec-4] INFO  c.e.d.service.impl.AuthServiceImpl - 用户登录成功: 23adfa126662
2025-04-17 16:39:49.828 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-17 16:39:49.833 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 16:39:49.834 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.834 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1101139708 wrapping com.mysql.cj.jdbc.ConnectionImpl@2322c1b1] will be managed by Spring
2025-04-17 16:39:49.834 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-17 16:39:49.834 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), LOGIN(String), 2025-04-17(String)
2025-04-17 16:39:49.835 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-17 16:39:49.835 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.836 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3] from current transaction
2025-04-17 16:39:49.836 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-17 16:39:49.836 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), LOGIN(String), 2025-04-16(String)
2025-04-17 16:39:49.837 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 1
2025-04-17 16:39:49.837 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.837 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3] from current transaction
2025-04-17 16:39:49.837 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, create_time, update_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-04-17 16:39:49.838 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==> Parameters: 1(Long), LOGIN(String), 3(Integer), 2025-04-17(LocalDate), 2025-04-17T16:39:49.837(LocalDateTime), 2025-04-17T16:39:49.837(LocalDateTime), 127.0.0.1(String)
2025-04-17 16:39:49.839 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.AccessLogMapper.insert - <==    Updates: 1
2025-04-17 16:39:49.839 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.839 [http-nio-8080-exec-4] INFO  c.e.d.s.impl.AccessLogServiceImpl - Created new access log for user: 1, type: LOGIN, count: 3
2025-04-17 16:39:49.839 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.839 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.839 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35f503a3]
2025-04-17 16:39:49.850 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 16:39:49.850 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=登录成功, data=TokenResponse(accessToken=Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdXRob3 (truncated)...]
2025-04-17 16:39:49.851 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-17 16:39:49.851 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 17:07:59.497 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-04-17 17:22:12.921 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de30c31]]
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de30c31]
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de30c31]
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:22:12.922 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:22:13.215 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-17 17:22:13.217 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-17 17:57:47.235 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-17 17:57:47.235 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 7748 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-17 17:57:47.238 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-17 17:57:47.238 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-17 17:57:48.004 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-17 17:57:48.005 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-17 17:57:48.029 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-04-17 17:57:48.100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-17 17:57:48.100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-17 17:57:48.101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-17 17:57:48.101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-17 17:57:48.102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-17 17:57:48.102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-17 17:57:48.102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-17 17:57:48.102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-17 17:57:48.103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-17 17:57:48.466 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-17 17:57:48.470 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-17 17:57:48.472 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-17 17:57:48.472 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-17 17:57:48.540 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-17 17:57:48.540 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1250 ms
2025-04-17 17:57:48.718 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-17 17:57:48.730 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-17 17:57:48.742 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-17 17:57:48.750 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-17 17:57:48.844 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-17 17:57:49.183 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-17 17:57:49.315 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)
2025-04-17 17:57:49.318 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.323 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-17 17:57:49.323 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.323 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-04-17 17:57:49.324 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 17:57:49.389 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-17 17:57:49.391 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-17 17:57:49.394 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@46612bfc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4f213a2, org.springframework.security.web.context.SecurityContextPersistenceFilter@120df990, org.springframework.security.web.header.HeaderWriterFilter@722531ab, org.springframework.security.web.authentication.logout.LogoutFilter@24197b13, com.example.pure.filter.JwtFilter@2db86a7c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@282c4da0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@18cf5c52, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@25699aa7, org.springframework.security.web.session.SessionManagementFilter@25f0c5e7, org.springframework.security.web.access.ExceptionTranslationFilter@74fb5b59, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@267891bf]
2025-04-17 17:57:49.396 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-17 17:57:49.396 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-17 17:57:49.522 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-17 17:57:49.537 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-17 17:57:49.584 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-17 17:57:49.591 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-17 17:57:49.847 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-17 17:57:49.930 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-17 17:57:49.950 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-17 17:57:49.951 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-17 17:57:49.952 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3ad847f8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19faedcc, org.springframework.security.web.context.SecurityContextPersistenceFilter@733f1395, org.springframework.security.web.header.HeaderWriterFilter@57e03347, org.springframework.web.filter.CorsFilter@2c6927f0, org.springframework.security.web.authentication.logout.LogoutFilter@b8a144e, com.example.pure.filter.JwtFilter@2db86a7c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21dea711, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7772d266, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ef2cbe6, org.springframework.security.web.session.SessionManagementFilter@2edb2f8b, org.springframework.security.web.access.ExceptionTranslationFilter@307c59ea, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c2dd89b]
2025-04-17 17:57:49.981 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7098b907, started on Thu Apr 17 17:57:47 CST 2025
2025-04-17 17:57:49.991 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-17 17:57:49.991 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-17 17:57:49.991 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-17 17:57:49.991 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-17 17:57:49.992 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-17 17:57:49.992 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-17 17:57:49.994 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-17 17:57:49.995 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-17 17:57:49.995 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-17 17:57:49.995 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-04-17 17:57:49.996 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-17 17:57:49.996 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-17 17:57:49.996 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-17 17:57:49.996 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-17 17:57:50.030 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-17 17:57:50.054 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-17 17:57:50.249 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-17 17:57:50.369 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-17 17:57:50.371 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 17:57:50.371 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 17:57:50.371 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-17 17:57:50.371 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e159116]
2025-04-17 17:57:50.371 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e159116]
2025-04-17 17:57:50.371 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e159116]]
2025-04-17 17:57:50.372 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-17 17:57:50.372 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:57:50.372 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:57:50.382 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.598 seconds (JVM running for 4.195)
2025-04-17 17:58:49.968 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-17 17:59:26.013 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e159116]]
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e159116]
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2e159116]
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:59:26.014 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:59:26.187 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-17 17:59:26.191 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-17 17:59:30.210 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-17 17:59:30.212 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 24756 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-17 17:59:30.212 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-17 17:59:30.212 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-17 17:59:31.020 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-17 17:59:31.022 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-17 17:59:31.046 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-04-17 17:59:31.123 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-17 17:59:31.123 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-17 17:59:31.123 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-17 17:59:31.123 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-17 17:59:31.124 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-17 17:59:31.124 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-17 17:59:31.125 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-17 17:59:31.125 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-17 17:59:31.125 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-17 17:59:31.585 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-17 17:59:31.590 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-17 17:59:31.591 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-17 17:59:31.591 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-17 17:59:31.679 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-17 17:59:31.679 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1422 ms
2025-04-17 17:59:31.890 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-17 17:59:31.902 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-17 17:59:31.916 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-17 17:59:31.926 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-17 17:59:32.057 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-17 17:59:32.436 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-17 17:59:32.558 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-17 17:59:32.560 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.566 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)
2025-04-17 17:59:32.567 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.567 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-17 17:59:32.567 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.567 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-04-17 17:59:32.567 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 17:59:32.633 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-17 17:59:32.635 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-17 17:59:32.638 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@4f213a2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@25699aa7, org.springframework.security.web.context.SecurityContextPersistenceFilter@b506ed0, org.springframework.security.web.header.HeaderWriterFilter@8f57e4c, org.springframework.security.web.authentication.logout.LogoutFilter@71b97eeb, com.example.pure.filter.JwtFilter@261f359f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65f3e805, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10618775, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5a47730c, org.springframework.security.web.session.SessionManagementFilter@5882b202, org.springframework.security.web.access.ExceptionTranslationFilter@130a6eb9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3a109ff7]
2025-04-17 17:59:32.639 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-17 17:59:32.640 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-17 17:59:32.787 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-17 17:59:32.802 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-17 17:59:32.852 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-17 17:59:32.860 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-17 17:59:33.136 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-17 17:59:33.235 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-17 17:59:33.253 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-17 17:59:33.254 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-17 17:59:33.255 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-17 17:59:33.256 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2b395581, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@726a8729, org.springframework.security.web.context.SecurityContextPersistenceFilter@21dea711, org.springframework.security.web.header.HeaderWriterFilter@2edb2f8b, org.springframework.web.filter.CorsFilter@1a2724d3, org.springframework.security.web.authentication.logout.LogoutFilter@5fb8dc01, com.example.pure.filter.JwtFilter@261f359f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7772d266, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@a21c74, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7dd91ffc, org.springframework.security.web.session.SessionManagementFilter@733f1395, org.springframework.security.web.access.ExceptionTranslationFilter@57e03347, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5817f1ca]
2025-04-17 17:59:33.283 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@15d49048, started on Thu Apr 17 17:59:30 CST 2025
2025-04-17 17:59:33.292 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-17 17:59:33.293 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-17 17:59:33.293 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-17 17:59:33.293 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-17 17:59:33.293 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-17 17:59:33.293 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-17 17:59:33.295 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-17 17:59:33.296 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-17 17:59:33.296 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-17 17:59:33.296 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-04-17 17:59:33.296 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-17 17:59:33.297 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-17 17:59:33.297 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-17 17:59:33.297 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-17 17:59:33.329 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-17 17:59:33.352 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-17 17:59:33.520 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-17 17:59:33.635 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-17 17:59:33.637 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 17:59:33.637 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 17:59:33.637 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-17 17:59:33.637 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-17 17:59:33.637 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-17 17:59:33.637 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]]
2025-04-17 17:59:33.637 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-17 17:59:33.637 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:59:33.637 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 17:59:33.647 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.893 seconds (JVM running for 4.443)
2025-04-17 17:59:49.685 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-17 17:59:49.685 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-17 17:59:49.685 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-17 17:59:49.685 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-17 17:59:49.686 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-17 17:59:49.686 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4a6d4b8b
2025-04-17 17:59:49.687 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@765d1cdd
2025-04-17 17:59:49.687 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-17 17:59:49.687 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-17 17:59:49.695 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /v3/api-docs
2025-04-17 17:59:49.697 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 17:59:49.704 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-04-17 17:59:49.705 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-17 17:59:49.709 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /v3/api-docs] with attributes [permitAll]
2025-04-17 17:59:49.709 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /v3/api-docs
2025-04-17 17:59:49.710 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/v3/api-docs", parameters={}
2025-04-17 17:59:49.711 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-04-17 17:59:50.139 [http-nio-8080-exec-1] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 415 ms
2025-04-17 17:59:50.157 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json]
2025-04-17 17:59:50.158 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing ["{"openapi":"3.0.1","info":{"title":"Spring Boot REST API","description":"Spring Boot REST API with J (truncated)..."]
2025-04-17 17:59:50.166 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-17 17:59:50.166 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 18:00:33.275 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-17 18:02:42.210 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/users/createUser
2025-04-17 18:02:42.210 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 18:02:42.210 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:02:42.662 [http-nio-8080-exec-6] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-17 18:02:42.675 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:02:42.677 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0]
2025-04-17 18:02:42.682 [http-nio-8080-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@d6d2fda] will be managed by Spring
2025-04-17 18:02:42.684 [http-nio-8080-exec-6] DEBUG c.e.d.m.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, /* Check if password is loaded */ email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-17 18:02:42.699 [http-nio-8080-exec-6] DEBUG c.e.d.m.UserMapper.findByUsername - ==> Parameters: 23adfa126662(String)
2025-04-17 18:02:42.715 [http-nio-8080-exec-6] DEBUG c.e.d.m.UserMapper.findByUsername - <==      Total: 1
2025-04-17 18:02:42.716 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0]
2025-04-17 18:02:42.717 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0] from current transaction
2025-04-17 18:02:42.718 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-17 18:02:42.718 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-17 18:02:42.719 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-17 18:02:42.719 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0]
2025-04-17 18:02:42.719 [http-nio-8080-exec-6] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-17 18:02:42.720 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0]
2025-04-17 18:02:42.720 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0]
2025-04-17 18:02:42.720 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c15cba0]
2025-04-17 18:02:42.734 [http-nio-8080-exec-6] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-17 18:02:42.734 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/users/createUser] with attributes [authenticated]
2025-04-17 18:02:42.734 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/users/createUser
2025-04-17 18:02:42.734 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/users/createUser", parameters={}
2025-04-17 18:02:42.735 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:02:42.738 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [RegisterRequest(username=ab1, password=Ab123456, confirmPassword=Ab123456, email=<EMAIL>, nickna (truncated)...]
2025-04-17 18:02:42.797 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleValidationExceptions(MethodArgumentNotValidException)
2025-04-17 18:02:42.797 [http-nio-8080-exec-6] WARN  c.e.d.e.GlobalExceptionHandler - 参数校验失败: {username=用户名长度必须在4-20个字符之间}
2025-04-17 18:02:42.798 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 18:02:42.798 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=601, message=参数校验失败, data={username=用户名长度必须在4-20个字符之间})]
2025-04-17 18:02:42.801 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.example.pure.common.Result<com.example.pure.model.entity.User> com.example.pure.controller.UserController.createUser(com.example.pure.model.dto.RegisterRequest,javax.servlet.http.HttpServletRequest): [Field error in object 'registerRequest' on field 'username': rejected value [ab1]; codes [Size.registerRequest.username,Size.username,Size.java.lang.String,Size]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [registerRequest.username,username]; arguments []; default message [username],20,4]; default message [用户名长度必须在4-20个字符之间]] ]
2025-04-17 18:02:42.801 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 400 BAD_REQUEST
2025-04-17 18:02:42.801 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 18:03:10.106 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/users/createUser
2025-04-17 18:03:10.107 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 18:03:10.107 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:03:10.112 [http-nio-8080-exec-4] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-17 18:03:10.120 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:03:10.120 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377a74a1]
2025-04-17 18:03:10.120 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1749131492 wrapping com.mysql.cj.jdbc.ConnectionImpl@d6d2fda] will be managed by Spring
2025-04-17 18:03:10.120 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-17 18:03:10.121 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-17 18:03:10.121 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-17 18:03:10.121 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377a74a1]
2025-04-17 18:03:10.121 [http-nio-8080-exec-4] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-17 18:03:10.122 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377a74a1]
2025-04-17 18:03:10.122 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377a74a1]
2025-04-17 18:03:10.122 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377a74a1]
2025-04-17 18:03:10.122 [http-nio-8080-exec-4] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-17 18:03:10.122 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/users/createUser] with attributes [authenticated]
2025-04-17 18:03:10.122 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/users/createUser
2025-04-17 18:03:10.123 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/users/createUser", parameters={}
2025-04-17 18:03:10.123 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:03:10.123 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [RegisterRequest(username=abc123, password=Abc123456, confirmPassword=Abc123456, email=<EMAIL>, n (truncated)...]
2025-04-17 18:03:10.150 [http-nio-8080-exec-4] INFO  c.e.demo13.controller.UserController - 用户注册: abc123
2025-04-17 18:03:10.158 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 处理用户注册请求: abc123
2025-04-17 18:03:10.158 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:03:10.158 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f]
2025-04-17 18:03:10.158 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1624576793 wrapping com.mysql.cj.jdbc.ConnectionImpl@d6d2fda] will be managed by Spring
2025-04-17 18:03:10.158 [http-nio-8080-exec-4] DEBUG c.e.d.m.UserMapper.countByUsername - ==>  Preparing: SELECT COUNT(*) FROM user WHERE username = ?
2025-04-17 18:03:10.159 [http-nio-8080-exec-4] DEBUG c.e.d.m.UserMapper.countByUsername - ==> Parameters: abc123(String)
2025-04-17 18:03:10.159 [http-nio-8080-exec-4] DEBUG c.e.d.m.UserMapper.countByUsername - <==      Total: 1
2025-04-17 18:03:10.160 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f]
2025-04-17 18:03:10.160 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f] from current transaction
2025-04-17 18:03:10.160 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.UserMapper.countByEmail - ==>  Preparing: SELECT COUNT(*) FROM user WHERE email = ?
2025-04-17 18:03:10.160 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.UserMapper.countByEmail - ==> Parameters: <EMAIL>(String)
2025-04-17 18:03:10.160 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.UserMapper.countByEmail - <==      Total: 1
2025-04-17 18:03:10.160 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f]
2025-04-17 18:03:10.225 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f] from current transaction
2025-04-17 18:03:10.237 [http-nio-8080-exec-4] DEBUG c.e.demo13.mapper.UserMapper.insert - ==>  Preparing: INSERT INTO user ( username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW() )
2025-04-17 18:03:10.238 [http-nio-8080-exec-4] DEBUG c.e.demo13.mapper.UserMapper.insert - ==> Parameters: abc123(String), $2a$10$V7QwicNCykDdREkhKwi6mu0fTDpkbgfJMgXcCGj.YIcReyahgcTaO(String), <EMAIL>(String), abc123(String), null, 0(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean)
2025-04-17 18:03:10.239 [http-nio-8080-exec-4] DEBUG c.e.demo13.mapper.UserMapper.insert - <==    Updates: 1
2025-04-17 18:03:10.241 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f]
2025-04-17 18:03:10.242 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f]
2025-04-17 18:03:10.242 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5686b0f]
2025-04-17 18:03:10.247 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-04-17 18:03:10.249 [http-nio-8080-exec-4] ERROR c.e.d.e.GlobalExceptionHandler - 系统异常
org.apache.ibatis.binding.BindingException: Mapper method 'com.example.pure.mapper.primary.UserMapper.insert' has an unsupported return type: class com.example.pure.model.entity.User
	at org.apache.ibatis.binding.MapperMethod.rowCountResult(MapperMethod.java:118)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy109.insert(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at com.sun.proxy.$Proxy111.insert(Unknown Source)
	at com.example.pure.service.impl.UserServiceImpl.createUser(UserServiceImpl.java:77)
	at com.example.pure.service.impl.UserServiceImpl$$FastClassBySpringCGLIB$$f62dcded.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.UserServiceImpl$$EnhancerBySpringCGLIB$$285860fd.createUser(<generated>)
	at com.example.pure.controller.UserController.createUser(UserController.java:94)
	at com.example.pure.controller.UserController$$FastClassBySpringCGLIB$$f9c372e1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.UserController$$EnhancerBySpringCGLIB$$1eb3980c.createUser(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:157)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-04-17 18:03:10.251 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 18:03:10.251 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [Result(code=500, message=系统异常，请联系管理员, data=null)]
2025-04-17 18:03:10.252 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.apache.ibatis.binding.BindingException: Mapper method 'com.example.pure.mapper.primary.UserMapper.insert' has an unsupported return type: class com.example.pure.model.entity.User]
2025-04-17 18:03:10.252 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 500 INTERNAL_SERVER_ERROR
2025-04-17 18:03:10.252 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]]
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:16:09.587 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:16:09.874 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-17 18:16:09.881 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-17 18:16:13.981 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-17 18:16:13.983 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 16416 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-17 18:16:13.983 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-17 18:16:13.984 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-17 18:16:14.742 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-17 18:16:14.744 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-17 18:16:14.767 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-04-17 18:16:14.839 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-17 18:16:14.839 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-17 18:16:14.839 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-17 18:16:14.840 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-17 18:16:14.841 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-17 18:16:14.841 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-17 18:16:14.841 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-17 18:16:14.841 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-17 18:16:14.841 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-17 18:16:15.211 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-17 18:16:15.215 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-17 18:16:15.216 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-17 18:16:15.216 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-17 18:16:15.283 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-17 18:16:15.284 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1260 ms
2025-04-17 18:16:15.454 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-17 18:16:15.465 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-17 18:16:15.477 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-17 18:16:15.486 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-17 18:16:15.580 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-17 18:16:15.902 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-17 18:16:16.034 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)
2025-04-17 18:16:16.037 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.043 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-17 18:16:16.044 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.044 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)
2025-04-17 18:16:16.044 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 18:16:16.112 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-17 18:16:16.114 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-17 18:16:16.117 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2b441e56, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@267891bf, org.springframework.security.web.context.SecurityContextPersistenceFilter@207dd1b7, org.springframework.security.web.header.HeaderWriterFilter@44fd7ba4, org.springframework.security.web.authentication.logout.LogoutFilter@4466cf5d, com.example.pure.filter.JwtFilter@478fb7dc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23ad2d17, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25f0c5e7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a109ff7, org.springframework.security.web.session.SessionManagementFilter@1e92c3b6, org.springframework.security.web.access.ExceptionTranslationFilter@5463f035, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@48cdb156]
2025-04-17 18:16:16.119 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-17 18:16:16.119 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-17 18:16:16.245 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-17 18:16:16.259 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-17 18:16:16.302 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-17 18:16:16.308 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-17 18:16:16.583 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-17 18:16:16.669 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-17 18:16:16.689 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-17 18:16:16.690 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-17 18:16:16.691 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@53ba1175, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@cd5ff55, org.springframework.security.web.context.SecurityContextPersistenceFilter@7282af25, org.springframework.security.web.header.HeaderWriterFilter@2fdf22f7, org.springframework.web.filter.CorsFilter@6003220a, org.springframework.security.web.authentication.logout.LogoutFilter@57e03347, com.example.pure.filter.JwtFilter@478fb7dc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7661e474, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@20ffb8d5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16b64a03, org.springframework.security.web.session.SessionManagementFilter@3a2546d6, org.springframework.security.web.access.ExceptionTranslationFilter@1132baa3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7ed7ae]
2025-04-17 18:16:16.718 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7098b907, started on Thu Apr 17 18:16:14 CST 2025
2025-04-17 18:16:16.727 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-17 18:16:16.727 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-17 18:16:16.728 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-17 18:16:16.728 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-17 18:16:16.728 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-17 18:16:16.728 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-17 18:16:16.730 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-17 18:16:16.730 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-17 18:16:16.730 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-17 18:16:16.731 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-17 18:16:16.731 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-17 18:16:16.731 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-17 18:16:16.731 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-17 18:16:16.732 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-17 18:16:16.770 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-17 18:16:16.798 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-17 18:16:16.980 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-17 18:16:17.112 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-17 18:16:17.113 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 18:16:17.114 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 18:16:17.114 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-17 18:16:17.114 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@333a44f2]
2025-04-17 18:16:17.114 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@333a44f2]
2025-04-17 18:16:17.114 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@333a44f2]]
2025-04-17 18:16:17.114 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-17 18:16:17.114 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:16:17.114 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:16:17.125 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.566 seconds (JVM running for 4.124)
2025-04-17 18:16:20.083 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-17 18:16:20.083 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-17 18:16:20.083 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-17 18:16:20.083 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-17 18:16:20.083 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-17 18:16:20.084 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@78e12af
2025-04-17 18:16:20.085 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@5942b4fe
2025-04-17 18:16:20.085 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-17 18:16:20.085 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-17 18:16:20.093 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/users/createUser
2025-04-17 18:16:20.095 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 18:16:20.101 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:16:20.571 [http-nio-8080-exec-1] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-17 18:16:20.617 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:16:20.619 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f657e58]
2025-04-17 18:16:20.624 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2123223676 wrapping com.mysql.cj.jdbc.ConnectionImpl@387ec7b5] will be managed by Spring
2025-04-17 18:16:20.625 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-17 18:16:20.639 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-17 18:16:20.655 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-17 18:16:20.655 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f657e58]
2025-04-17 18:16:20.656 [http-nio-8080-exec-1] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-17 18:16:20.656 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f657e58]
2025-04-17 18:16:20.656 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f657e58]
2025-04-17 18:16:20.656 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f657e58]
2025-04-17 18:16:20.657 [http-nio-8080-exec-1] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-17 18:16:20.661 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/users/createUser] with attributes [authenticated]
2025-04-17 18:16:20.662 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/users/createUser
2025-04-17 18:16:20.663 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/users/createUser", parameters={}
2025-04-17 18:16:20.664 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:16:20.677 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [RegisterRequest(username=abc123, password=Abc123456, confirmPassword=Abc123456, email=<EMAIL>, n (truncated)...]
2025-04-17 18:16:20.756 [http-nio-8080-exec-1] INFO  c.e.demo13.controller.UserController - 用户注册: abc123
2025-04-17 18:16:20.763 [http-nio-8080-exec-1] DEBUG c.e.d.service.impl.UserServiceImpl - 处理用户注册请求: abc123
2025-04-17 18:16:20.763 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:16:20.763 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766]
2025-04-17 18:16:20.763 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1560813462 wrapping com.mysql.cj.jdbc.ConnectionImpl@387ec7b5] will be managed by Spring
2025-04-17 18:16:20.763 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.countByUsername - ==>  Preparing: SELECT COUNT(*) FROM user WHERE username = ?
2025-04-17 18:16:20.764 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.countByUsername - ==> Parameters: abc123(String)
2025-04-17 18:16:20.765 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.countByUsername - <==      Total: 1
2025-04-17 18:16:20.766 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766]
2025-04-17 18:16:20.766 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766] from current transaction
2025-04-17 18:16:20.766 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.countByEmail - ==>  Preparing: SELECT COUNT(*) FROM user WHERE email = ?
2025-04-17 18:16:20.766 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.countByEmail - ==> Parameters: <EMAIL>(String)
2025-04-17 18:16:20.766 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.countByEmail - <==      Total: 1
2025-04-17 18:16:20.767 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766]
2025-04-17 18:16:20.833 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766] from current transaction
2025-04-17 18:16:20.844 [http-nio-8080-exec-1] DEBUG c.e.demo13.mapper.UserMapper.insert - ==>  Preparing: INSERT INTO user ( username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW() )
2025-04-17 18:16:20.846 [http-nio-8080-exec-1] DEBUG c.e.demo13.mapper.UserMapper.insert - ==> Parameters: abc123(String), $2a$10$nTDRX1n7BUbquyTBOGpAYeHxWduXweiIOzDnwD0CewqAc59lnhogu(String), <EMAIL>(String), abc123(String), null, 0(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean)
2025-04-17 18:16:20.847 [http-nio-8080-exec-1] DEBUG c.e.demo13.mapper.UserMapper.insert - <==    Updates: 1
2025-04-17 18:16:20.849 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766]
2025-04-17 18:16:20.851 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766]
2025-04-17 18:16:20.851 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@630b8766]
2025-04-17 18:16:20.857 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-04-17 18:16:20.859 [http-nio-8080-exec-1] ERROR c.e.d.e.GlobalExceptionHandler - 系统异常
org.apache.ibatis.binding.BindingException: Mapper method 'com.example.pure.mapper.primary.UserMapper.insert' has an unsupported return type: class java.lang.Void
	at org.apache.ibatis.binding.MapperMethod.rowCountResult(MapperMethod.java:118)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy109.insert(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at com.sun.proxy.$Proxy111.insert(Unknown Source)
	at com.example.pure.service.impl.UserServiceImpl.createUser(UserServiceImpl.java:77)
	at com.example.pure.service.impl.UserServiceImpl$$FastClassBySpringCGLIB$$f62dcded.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.UserServiceImpl$$EnhancerBySpringCGLIB$$478787b7.createUser(<generated>)
	at com.example.pure.controller.UserController.createUser(UserController.java:94)
	at com.example.pure.controller.UserController$$FastClassBySpringCGLIB$$f9c372e1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.UserController$$EnhancerBySpringCGLIB$$4a8f6ab3.createUser(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:157)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-04-17 18:16:20.873 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 18:16:20.874 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [Result(code=500, message=系统异常，请联系管理员, data=null)]
2025-04-17 18:16:20.885 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.apache.ibatis.binding.BindingException: Mapper method 'com.example.pure.mapper.primary.UserMapper.insert' has an unsupported return type: class java.lang.Void]
2025-04-17 18:16:20.886 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 500 INTERNAL_SERVER_ERROR
2025-04-17 18:16:20.886 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 18:17:16.706 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@333a44f2]]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@333a44f2]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@333a44f2]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:20:07.894 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:20:08.172 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-17 18:20:08.179 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-17 18:20:12.211 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 19968 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-17 18:20:12.210 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-17 18:20:12.212 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-17 18:20:12.212 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-17 18:20:12.953 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-17 18:20:12.954 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-17 18:20:12.978 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-04-17 18:20:13.051 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-17 18:20:13.051 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-17 18:20:13.051 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-17 18:20:13.052 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-17 18:20:13.052 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-17 18:20:13.053 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-17 18:20:13.053 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-17 18:20:13.053 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-17 18:20:13.053 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-17 18:20:13.485 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-17 18:20:13.489 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-17 18:20:13.490 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-17 18:20:13.491 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-17 18:20:13.569 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-17 18:20:13.569 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1315 ms
2025-04-17 18:20:13.768 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-17 18:20:13.778 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-17 18:20:13.789 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-17 18:20:13.798 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-17 18:20:13.894 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-17 18:20:14.250 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-17 18:20:14.387 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-04-17 18:20:14.389 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.396 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)
2025-04-17 18:20:14.397 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.397 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)
2025-04-17 18:20:14.398 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-17 18:20:14.473 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-17 18:20:14.474 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-17 18:20:14.478 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@78b0ec3a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46612bfc, org.springframework.security.web.context.SecurityContextPersistenceFilter@5882b202, org.springframework.security.web.header.HeaderWriterFilter@130a6eb9, org.springframework.security.web.authentication.logout.LogoutFilter@1c74d19, com.example.pure.filter.JwtFilter@1a1f79ce, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b506ed0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65f3e805, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f213a2, org.springframework.security.web.session.SessionManagementFilter@6bce4140, org.springframework.security.web.access.ExceptionTranslationFilter@5b742bc8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b441e56]
2025-04-17 18:20:14.479 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-17 18:20:14.480 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-17 18:20:14.635 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-17 18:20:14.650 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-17 18:20:14.698 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-17 18:20:14.705 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-17 18:20:14.988 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-17 18:20:15.076 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-17 18:20:15.097 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-17 18:20:15.097 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-17 18:20:15.097 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-17 18:20:15.097 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-17 18:20:15.097 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-17 18:20:15.097 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-17 18:20:15.098 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-17 18:20:15.099 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-17 18:20:15.099 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@19faedcc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c6927f0, org.springframework.security.web.context.SecurityContextPersistenceFilter@2edb2f8b, org.springframework.security.web.header.HeaderWriterFilter@307c59ea, org.springframework.web.filter.CorsFilter@5ef2cbe6, org.springframework.security.web.authentication.logout.LogoutFilter@a21c74, com.example.pure.filter.JwtFilter@1a1f79ce, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@733f1395, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@21dea711, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@266da047, org.springframework.security.web.session.SessionManagementFilter@57e03347, org.springframework.security.web.access.ExceptionTranslationFilter@20ffb8d5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3ad847f8]
2025-04-17 18:20:15.129 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@503f91c3, started on Thu Apr 17 18:20:12 CST 2025
2025-04-17 18:20:15.139 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-17 18:20:15.139 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-17 18:20:15.139 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-17 18:20:15.139 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-17 18:20:15.140 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-17 18:20:15.140 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-17 18:20:15.142 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-17 18:20:15.142 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-17 18:20:15.142 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-17 18:20:15.143 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
2025-04-17 18:20:15.143 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-17 18:20:15.144 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-17 18:20:15.144 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-17 18:20:15.144 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-17 18:20:15.179 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-17 18:20:15.207 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-17 18:20:15.404 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-17 18:20:15.532 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-17 18:20:15.533 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 18:20:15.533 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 18:20:15.534 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-17 18:20:15.534 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e38e254]
2025-04-17 18:20:15.534 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e38e254]
2025-04-17 18:20:15.534 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e38e254]]
2025-04-17 18:20:15.534 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-17 18:20:15.534 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:20:15.534 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:20:15.544 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.741 seconds (JVM running for 4.306)
2025-04-17 18:20:31.559 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-17 18:20:31.559 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-17 18:20:31.559 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-17 18:20:31.559 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-17 18:20:31.559 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-17 18:20:31.560 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7055d8a9
2025-04-17 18:20:31.561 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@41b91b2a
2025-04-17 18:20:31.561 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-17 18:20:31.561 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-17 18:20:31.570 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/users/createUser
2025-04-17 18:20:31.572 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 18:20:31.579 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:20:32.036 [http-nio-8080-exec-1] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-17 18:20:32.084 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:20:32.086 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@600934f8]
2025-04-17 18:20:32.091 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@254914993 wrapping com.mysql.cj.jdbc.ConnectionImpl@214f8c48] will be managed by Spring
2025-04-17 18:20:32.093 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-17 18:20:32.108 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-17 18:20:32.125 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-17 18:20:32.125 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@600934f8]
2025-04-17 18:20:32.125 [http-nio-8080-exec-1] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-17 18:20:32.126 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@600934f8]
2025-04-17 18:20:32.126 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@600934f8]
2025-04-17 18:20:32.126 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@600934f8]
2025-04-17 18:20:32.127 [http-nio-8080-exec-1] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-17 18:20:32.132 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/users/createUser] with attributes [authenticated]
2025-04-17 18:20:32.132 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/users/createUser
2025-04-17 18:20:32.133 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/users/createUser", parameters={}
2025-04-17 18:20:32.134 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.UserController#createUser(RegisterRequest, HttpServletRequest)
2025-04-17 18:20:32.146 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [RegisterRequest(username=abc123, password=Abc123456, confirmPassword=Abc123456, email=<EMAIL>, n (truncated)...]
2025-04-17 18:20:32.221 [http-nio-8080-exec-1] INFO  c.e.demo13.controller.UserController - 用户注册: abc123
2025-04-17 18:20:32.228 [http-nio-8080-exec-1] DEBUG c.e.d.service.impl.UserServiceImpl - 处理用户注册请求: abc123
2025-04-17 18:20:32.228 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:20:32.229 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.229 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1971084692 wrapping com.mysql.cj.jdbc.ConnectionImpl@214f8c48] will be managed by Spring
2025-04-17 18:20:32.229 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.countByUsername - ==>  Preparing: SELECT COUNT(*) FROM user WHERE username = ?
2025-04-17 18:20:32.229 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.countByUsername - ==> Parameters: abc123(String)
2025-04-17 18:20:32.231 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.countByUsername - <==      Total: 1
2025-04-17 18:20:32.231 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.231 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9] from current transaction
2025-04-17 18:20:32.231 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.countByEmail - ==>  Preparing: SELECT COUNT(*) FROM user WHERE email = ?
2025-04-17 18:20:32.232 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.countByEmail - ==> Parameters: <EMAIL>(String)
2025-04-17 18:20:32.232 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.countByEmail - <==      Total: 1
2025-04-17 18:20:32.232 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.299 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9] from current transaction
2025-04-17 18:20:32.312 [http-nio-8080-exec-1] DEBUG c.e.demo13.mapper.UserMapper.insert - ==>  Preparing: INSERT INTO user ( username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW() )
2025-04-17 18:20:32.314 [http-nio-8080-exec-1] DEBUG c.e.demo13.mapper.UserMapper.insert - ==> Parameters: abc123(String), $2a$10$8QkjCnhl0urxTPdK83ZYtOm.hEXS.3aN/4D41tajJCJZ3DTOrktdW(String), <EMAIL>(String), abc123(String), null, 0(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean)
2025-04-17 18:20:32.315 [http-nio-8080-exec-1] DEBUG c.e.demo13.mapper.UserMapper.insert - <==    Updates: 1
2025-04-17 18:20:32.318 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.318 [http-nio-8080-exec-1] DEBUG c.e.d.service.impl.UserServiceImpl - 获取用户DTO, username: abc123
2025-04-17 18:20:32.318 [http-nio-8080-exec-1] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: abc123
2025-04-17 18:20:32.320 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9] from current transaction
2025-04-17 18:20:32.320 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, /* Check if password is loaded */ email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-17 18:20:32.320 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.findByUsername - ==> Parameters: abc123(String)
2025-04-17 18:20:32.321 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.findByUsername - <==      Total: 1
2025-04-17 18:20:32.322 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.323 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9] from current transaction
2025-04-17 18:20:32.323 [http-nio-8080-exec-1] DEBUG c.e.d.m.RoleMapper.assignRoleToUser - ==>  Preparing: INSERT INTO user_role (user_id, role_id) VALUES (?, ?)
2025-04-17 18:20:32.323 [http-nio-8080-exec-1] DEBUG c.e.d.m.RoleMapper.assignRoleToUser - ==> Parameters: 13(Long), 1(Long)
2025-04-17 18:20:32.324 [http-nio-8080-exec-1] DEBUG c.e.d.m.RoleMapper.assignRoleToUser - <==    Updates: 1
2025-04-17 18:20:32.324 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.324 [http-nio-8080-exec-1] INFO  c.e.d.service.impl.UserServiceImpl - 已为用户 abc123 分配默认角色 ROLE_USER
2025-04-17 18:20:32.325 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.325 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.325 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a12eae9]
2025-04-17 18:20:32.356 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 18:20:32.357 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=注册成功, data=UserDTO(id=13, username=abc123, email=<EMAIL>, nickname=abc1 (truncated)...]
2025-04-17 18:20:32.366 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-17 18:20:32.367 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 18:21:15.131 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-17 18:23:07.705 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-17 18:23:07.706 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-17 18:23:07.706 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-17 18:23:07.706 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-17 18:23:07.707 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-17 18:23:07.707 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-17 18:23:07.707 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-17 18:23:07.707 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-17 18:23:07.709 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=abc123, password=Abc123456, email=null)]
2025-04-17 18:23:07.730 [http-nio-8080-exec-5] INFO  c.e.demo13.controller.AuthController - 用户登录: abc123
2025-04-17 18:23:07.730 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.AuthServiceImpl - 处理用户登录请求: abc123
2025-04-17 18:23:07.796 [http-nio-8080-exec-5] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: abc123
2025-04-17 18:23:07.798 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:23:07.798 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@311c7311]
2025-04-17 18:23:07.798 [http-nio-8080-exec-5] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2011273527 wrapping com.mysql.cj.jdbc.ConnectionImpl@214f8c48] will be managed by Spring
2025-04-17 18:23:07.799 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-17 18:23:07.799 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 13(Long)
2025-04-17 18:23:07.800 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-17 18:23:07.800 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@311c7311]
2025-04-17 18:23:07.800 [http-nio-8080-exec-5] DEBUG c.e.d.s.CustomUserDetailsService - 用户 abc123 的角色信息已加载, 角色数量: 1
2025-04-17 18:23:07.800 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@311c7311]
2025-04-17 18:23:07.800 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@311c7311]
2025-04-17 18:23:07.800 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@311c7311]
2025-04-17 18:23:07.861 [http-nio-8080-exec-5] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-04-17 18:23:07.885 [http-nio-8080-exec-5] INFO  c.e.d.service.impl.DeviceServiceImpl - 用户 abc123 添加设备 2af945c3-f52c-49e2-ae43-bf0af4ca7df2 成功，使用token作为标识
2025-04-17 18:23:07.886 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: abc123
2025-04-17 18:23:07.888 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 更新用户基本信息: abc123
2025-04-17 18:23:07.888 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 13
2025-04-17 18:23:07.889 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:23:07.889 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1]
2025-04-17 18:23:07.889 [http-nio-8080-exec-5] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@214f8c48] will be managed by Spring
2025-04-17 18:23:07.889 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.UserMapper.findById - ==>  Preparing: SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE id = ?
2025-04-17 18:23:07.890 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.UserMapper.findById - ==> Parameters: 13(Long)
2025-04-17 18:23:07.890 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.UserMapper.findById - <==      Total: 1
2025-04-17 18:23:07.890 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1]
2025-04-17 18:23:07.891 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1] from current transaction
2025-04-17 18:23:07.892 [http-nio-8080-exec-5] DEBUG c.e.demo13.mapper.UserMapper.update - ==>  Preparing: UPDATE user SET username = ?, password = ?, email = ?, nickname = ?, gender = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, update_time = NOW() WHERE id = ?
2025-04-17 18:23:07.893 [http-nio-8080-exec-5] DEBUG c.e.demo13.mapper.UserMapper.update - ==> Parameters: abc123(String), $2a$10$8QkjCnhl0urxTPdK83ZYtOm.hEXS.3aN/4D41tajJCJZ3DTOrktdW(String), <EMAIL>(String), abc123(String), 0(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-04-17T18:23:07.888(LocalDateTime), eyJhbGciOiJIUzI1NiJ9.*******************************************************************.iYTuFi9aHsiLPolG2ZXqjCsW6N6dV6qnP8EANrgaQsE(String), 13(Long)
2025-04-17 18:23:07.895 [http-nio-8080-exec-5] DEBUG c.e.demo13.mapper.UserMapper.update - <==    Updates: 1
2025-04-17 18:23:07.895 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1]
2025-04-17 18:23:07.896 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 用户基本信息更新成功: abc123
2025-04-17 18:23:07.896 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1]
2025-04-17 18:23:07.896 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1]
2025-04-17 18:23:07.896 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5847ecd1]
2025-04-17 18:23:07.906 [http-nio-8080-exec-5] INFO  c.e.d.service.impl.AuthServiceImpl - 用户登录成功: abc123
2025-04-17 18:23:07.906 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: abc123
2025-04-17 18:23:07.911 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-17 18:23:07.911 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.911 [http-nio-8080-exec-5] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@751475109 wrapping com.mysql.cj.jdbc.ConnectionImpl@214f8c48] will be managed by Spring
2025-04-17 18:23:07.911 [http-nio-8080-exec-5] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-17 18:23:07.911 [http-nio-8080-exec-5] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 13(Long), LOGIN(String), 2025-04-17(String)
2025-04-17 18:23:07.912 [http-nio-8080-exec-5] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-17 18:23:07.913 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.914 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8] from current transaction
2025-04-17 18:23:07.914 [http-nio-8080-exec-5] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-17 18:23:07.914 [http-nio-8080-exec-5] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 13(Long), LOGIN(String), 2025-04-16(String)
2025-04-17 18:23:07.915 [http-nio-8080-exec-5] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-17 18:23:07.915 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.915 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8] from current transaction
2025-04-17 18:23:07.915 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, create_time, update_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-04-17 18:23:07.916 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==> Parameters: 13(Long), LOGIN(String), 1(Integer), 2025-04-17(LocalDate), 2025-04-17T18:23:07.915(LocalDateTime), 2025-04-17T18:23:07.915(LocalDateTime), 127.0.0.1(String)
2025-04-17 18:23:07.917 [http-nio-8080-exec-5] DEBUG c.e.d.mapper.AccessLogMapper.insert - <==    Updates: 1
2025-04-17 18:23:07.917 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.917 [http-nio-8080-exec-5] INFO  c.e.d.s.impl.AccessLogServiceImpl - Created new access log for user: 13, type: LOGIN, count: 1
2025-04-17 18:23:07.917 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.917 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.917 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2618c6a8]
2025-04-17 18:23:07.924 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-17 18:23:07.924 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=登录成功, data=TokenResponse(accessToken=Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdXRob3 (truncated)...]
2025-04-17 18:23:07.926 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-17 18:23:07.926 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e38e254]]
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e38e254]
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e38e254]
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:23:35.379 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-17 18:23:35.653 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-17 18:23:35.658 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
