2025-06-15 18:43:45.903 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-15 18:43:45.909 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 25316 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-15 18:43:45.910 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-15 18:43:45.910 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-15 18:43:47.194 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-15 18:43:47.196 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-15 18:43:47.235 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-15 18:43:47.348 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-15 18:43:47.349 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-15 18:43:47.350 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-15 18:43:47.351 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-15 18:43:47.352 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-15 18:43:47.352 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-15 18:43:47.352 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-15 18:43:47.352 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-15 18:43:47.352 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-15 18:43:47.353 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-15 18:43:47.353 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-15 18:43:47.353 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-15 18:43:47.353 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-15 18:43:47.353 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-15 18:43:47.353 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-15 18:43:47.354 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-15 18:43:47.354 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-15 18:43:47.354 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-15 18:43:47.354 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-15 18:43:47.354 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-15 18:43:47.354 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-15 18:43:47.355 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-15 18:43:47.355 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-15 18:43:47.355 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-15 18:43:47.355 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-15 18:43:47.355 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-15 18:43:47.355 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-15 18:43:47.356 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-15 18:43:47.356 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-15 18:43:47.356 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-15 18:43:47.956 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-15 18:43:47.960 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-15 18:43:47.962 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-15 18:43:47.962 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-15 18:43:48.063 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-15 18:43:48.063 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2109 ms
2025-06-15 18:43:48.316 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-15 18:43:48.327 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-15 18:43:48.334 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-15 18:43:48.342 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-15 18:43:48.349 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-15 18:43:48.354 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-15 18:43:48.360 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-15 18:43:48.366 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-15 18:43:48.373 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-15 18:43:48.382 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-15 18:43:48.394 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-15 18:43:48.399 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-15 18:43:48.404 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-15 18:43:48.411 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-15 18:43:48.422 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-15 18:43:49.009 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-15 18:43:49.639 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-15 18:43:49.640 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-15 18:43:49.955 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-15 18:43:49.957 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.029 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 18:43:50.029 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.030 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-15 18:43:50.030 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.031 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-15 18:43:50.032 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.032 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-15 18:43:50.032 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.033 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-15 18:43:50.033 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.033 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-15 18:43:50.033 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.038 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-15 18:43:50.038 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.039 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 18:43:50.039 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:43:50.148 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-15 18:43:50.150 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-15 18:43:50.154 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@16c9f7f0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@187df588, org.springframework.security.web.context.SecurityContextPersistenceFilter@4511146f, org.springframework.security.web.header.HeaderWriterFilter@354e2bff, org.springframework.security.web.authentication.logout.LogoutFilter@8dedec8, com.example.pure.filter.JwtFilter@38caad07, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5819ee0f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@f310675, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d75940, org.springframework.security.web.session.SessionManagementFilter@4b7a4c83, org.springframework.security.web.access.ExceptionTranslationFilter@41c88e00, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@77e5c765]
2025-06-15 18:43:50.156 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-15 18:43:50.157 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-15 18:43:50.362 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-15 18:43:50.382 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-15 18:43:50.437 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-15 18:43:50.445 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-15 18:43:50.744 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-15 18:43:50.903 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-15 18:43:50.924 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-15 18:43:50.925 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-15 18:43:50.926 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-15 18:43:50.926 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-15 18:43:50.926 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-15 18:43:50.926 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2b18b308, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@37dc7661, org.springframework.security.web.context.SecurityContextPersistenceFilter@6e612122, org.springframework.security.web.header.HeaderWriterFilter@2f4fc18, org.springframework.web.filter.CorsFilter@2e015ac1, org.springframework.security.web.authentication.logout.LogoutFilter@16745abd, com.example.pure.filter.JwtFilter@38caad07, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1d38cdde, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3809f65d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3e755d5f, org.springframework.security.web.session.SessionManagementFilter@7187078a, org.springframework.security.web.access.ExceptionTranslationFilter@7f31937b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d51b6a8]
2025-06-15 18:43:50.963 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@34645867, started on Sun Jun 15 18:43:45 CST 2025
2025-06-15 18:43:50.979 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-15 18:43:50.979 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-15 18:43:50.979 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-15 18:43:50.980 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-15 18:43:50.982 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-15 18:43:50.983 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-15 18:43:50.983 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-15 18:43:50.983 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-15 18:43:50.983 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-15 18:43:50.984 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-15 18:43:50.984 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-06-15 18:43:50.984 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-15 18:43:50.985 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-15 18:43:50.985 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-15 18:43:50.985 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-15 18:43:51.067 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 18:43:51.101 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 18:43:51.346 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-15 18:43:51.355 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 18:43:51.357 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 18:43:51.357 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 18:43:51.357 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-15 18:43:51.357 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5bfeb9ba]
2025-06-15 18:43:51.357 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5bfeb9ba]
2025-06-15 18:43:51.357 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5bfeb9ba]]
2025-06-15 18:43:51.358 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-15 18:43:51.358 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 18:43:51.358 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 18:43:51.372 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.937 seconds (JVM running for 7.019)
2025-06-15 18:44:02.788 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 18:44:02.788 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-15 18:44:02.788 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-15 18:44:02.788 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-15 18:44:02.788 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-15 18:44:02.790 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1823a991
2025-06-15 18:44:02.790 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6d24c5ab
2025-06-15 18:44:02.790 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-15 18:44:02.791 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-06-15 18:44:02.803 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 18:44:02.805 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 18:44:02.813 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 18:44:03.276 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 18:44:03.293 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:44:03.296 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.301 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@209834047 wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4e1f94] will be managed by Spring
2025-06-15 18:44:03.303 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-06-15 18:44:03.317 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: hao111(String)
2025-06-15 18:44:03.414 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-06-15 18:44:03.415 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.419 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e] from current transaction
2025-06-15 18:44:03.419 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : ==>  Preparing: SELECT * FROM user WHERE id = ?
2025-06-15 18:44:03.420 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : ==> Parameters: 8(Long)
2025-06-15 18:44:03.434 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findUserWithPasswordByUserId : <==      Total: 1
2025-06-15 18:44:03.435 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.435 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e] from current transaction
2025-06-15 18:44:03.435 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 18:44:03.436 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 18:44:03.450 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 18:44:03.450 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.451 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 18:44:03.452 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.452 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.452 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@657b1a1e]
2025-06-15 18:44:03.553 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 18:44:03.557 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 18:44:03.558 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 18:44:03.560 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 18:44:03.562 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 18:44:03.593 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Abc123456, email=null, deviceId=null)]
2025-06-15 18:44:03.683 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 18:44:03.684 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 18:44:03.989 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 18:44:04.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:44:04.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fe3c66a]
2025-06-15 18:44:04.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1436859374 wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4e1f94] will be managed by Spring
2025-06-15 18:44:04.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 18:44:04.005 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 18:44:04.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 18:44:04.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fe3c66a]
2025-06-15 18:44:04.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 18:44:04.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fe3c66a]
2025-06-15 18:44:04.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fe3c66a]
2025-06-15 18:44:04.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fe3c66a]
2025-06-15 18:44:04.132 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-06-15 18:44:04.158 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户 hao111 设备数量已达上限，移除最早登录的设备: 67
2025-06-15 18:44:04.163 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 通过token移除用户 hao111 设备成功
2025-06-15 18:44:04.170 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户 hao111 添加设备 b74f7d3f-357e-4dfd-9f18-80da6da43391 成功，使用token作为标识
2025-06-15 18:44:04.170 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户 hao111 登录设备超过限制，已移除最早登录的设备: eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************.0qh3FdpsvRfy493C5EV6pBrkI5G2hSQ9ICRDuVZLqaQ
2025-06-15 18:44:04.182 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:44:04.182 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@26804b45] was not registered for synchronization because synchronization is not active
2025-06-15 18:44:04.182 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1170505936 wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4e1f94] will not be managed by Spring
2025-06-15 18:44:04.182 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-06-15 18:44:04.183 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: hao111(String)
2025-06-15 18:44:04.198 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-06-15 18:44:04.198 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@26804b45]
2025-06-15 18:44:04.214 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 8
2025-06-15 18:44:04.216 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:44:04.216 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@444a1f1c]
2025-06-15 18:44:04.230 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@******** wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4e1f94] will be managed by Spring
2025-06-15 18:44:04.230 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, updated_time = NOW() WHERE id = ?
2025-06-15 18:44:04.231 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==> Parameters: hao111(String), $2a$10$7B6SHc03VdBxyUwqS61BiuFn7OLfJdVtVsRsyMuc.6sAZwbgiKzne(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-15 18:44:04.201803(Timestamp), eyJhbGciOiJIUzI1NiJ9.*******************************************************************.imPvQ055iwHg5esiEsgrdK6j3Scv1GvqF0JIWRPDWD4(String), 8(Long)
2025-06-15 18:44:04.279 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-15 18:44:04.279 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@444a1f1c]
2025-06-15 18:44:04.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 8
2025-06-15 18:44:04.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@444a1f1c]
2025-06-15 18:44:04.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@444a1f1c]
2025-06-15 18:44:04.281 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@444a1f1c]
2025-06-15 18:44:04.316 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户登录成功: hao111
2025-06-15 18:44:04.343 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 根据用户名查找用户: hao111
2025-06-15 18:44:04.406 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:44:04.407 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.407 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@417732474 wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4e1f94] will be managed by Spring
2025-06-15 18:44:04.407 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-15 18:44:04.407 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 8(Long), LOGIN(String), 2025-06-15(String)
2025-06-15 18:44:04.444 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-06-15 18:44:04.445 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.447 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7] from current transaction
2025-06-15 18:44:04.447 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-15 18:44:04.447 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 8(Long), LOGIN(String), 2025-06-14(String)
2025-06-15 18:44:04.462 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-06-15 18:44:04.462 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.462 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7] from current transaction
2025-06-15 18:44:04.463 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.AccessLogMapper.insert : ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, created_time, updated_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-15 18:44:04.464 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.AccessLogMapper.insert : ==> Parameters: 8(Long), LOGIN(String), 1(Integer), 2025-06-15(LocalDate), 2025-06-15T18:44:04.462667500(LocalDateTime), 2025-06-15T18:44:04.462667500(LocalDateTime), 127.0.0.1(String)
2025-06-15 18:44:04.493 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.AccessLogMapper.insert : <==    Updates: 1
2025-06-15 18:44:04.495 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.495 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.AccessLogServiceImpl : Created new access log for user: 8, type: LOGIN, count: 1
2025-06-15 18:44:04.495 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.495 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.495 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6516e9a7]
2025-06-15 18:44:04.567 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:44:04.567 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ee87b09]
2025-06-15 18:44:04.567 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@626018377 wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4e1f94] will be managed by Spring
2025-06-15 18:44:04.567 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-15 18:44:04.568 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 8(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户hao111执行了登陆(String), 2025-06-15T18:44:04.552291500(LocalDateTime)
2025-06-15 18:44:04.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-15 18:44:04.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ee87b09]
2025-06-15 18:44:04.594 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=8, summary=用户hao111执行了登陆
2025-06-15 18:44:04.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ee87b09]
2025-06-15 18:44:04.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ee87b09]
2025-06-15 18:44:04.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ee87b09]
2025-06-15 18:44:04.630 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 18:44:04.642 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=hao111, accessToken=eyJhbGc (truncated)...]
2025-06-15 18:44:04.653 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-15 18:44:04.654 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 18:44:50.951 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-15 18:45:25.943 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 18:45:25.943 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 18:45:25.943 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-15 18:45:25.943 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5bfeb9ba]]
2025-06-15 18:45:25.943 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5bfeb9ba]
2025-06-15 18:45:25.943 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5bfeb9ba]
2025-06-15 18:45:25.943 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-15 18:45:25.943 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 18:45:25.943 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 18:45:26.401 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-15 18:45:26.410 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-15 18:45:29.688 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-15 18:45:29.692 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 4916 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-15 18:45:29.693 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-15 18:45:29.693 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-15 18:45:30.751 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-15 18:45:30.753 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-15 18:45:30.786 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-15 18:45:30.890 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-15 18:45:30.891 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-15 18:45:30.892 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-15 18:45:30.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-15 18:45:30.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-15 18:45:30.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-15 18:45:30.893 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-15 18:45:30.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-15 18:45:30.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-15 18:45:30.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-15 18:45:30.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-15 18:45:30.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-15 18:45:30.894 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-15 18:45:30.895 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-15 18:45:30.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-15 18:45:30.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-15 18:45:30.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-15 18:45:30.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-15 18:45:30.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-15 18:45:30.896 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-15 18:45:30.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-15 18:45:30.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-15 18:45:30.897 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-15 18:45:31.491 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-15 18:45:31.497 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-15 18:45:31.498 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-15 18:45:31.498 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-15 18:45:31.600 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-15 18:45:31.600 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1863 ms
2025-06-15 18:45:31.869 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-15 18:45:31.882 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-15 18:45:31.889 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-15 18:45:31.896 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-15 18:45:31.905 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-15 18:45:31.910 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-15 18:45:31.917 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-15 18:45:31.923 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-15 18:45:31.931 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-15 18:45:31.939 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-15 18:45:31.953 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-15 18:45:31.960 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-15 18:45:31.965 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-15 18:45:31.971 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-15 18:45:31.986 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-15 18:45:32.438 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-15 18:45:33.679 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-15 18:45:33.679 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-15 18:45:34.208 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-15 18:45:34.210 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.291 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-15 18:45:34.291 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.292 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-15 18:45:34.293 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.294 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-15 18:45:34.294 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.294 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 18:45:34.294 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.294 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-15 18:45:34.294 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.295 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-15 18:45:34.295 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.299 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 18:45:34.299 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.300 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-15 18:45:34.300 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-15 18:45:34.404 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-15 18:45:34.406 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-15 18:45:34.411 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@f472245, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@110318a7, org.springframework.security.web.context.SecurityContextPersistenceFilter@5d9d8ecf, org.springframework.security.web.header.HeaderWriterFilter@7ffc4195, org.springframework.security.web.authentication.logout.LogoutFilter@43e2b8da, com.example.pure.filter.JwtFilter@50e24ea4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@758311ed, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40199d5e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@42ac309, org.springframework.security.web.session.SessionManagementFilter@453439e, org.springframework.security.web.access.ExceptionTranslationFilter@69356aca, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7af0693b]
2025-06-15 18:45:34.413 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-15 18:45:34.415 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-15 18:45:34.589 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-15 18:45:34.609 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-15 18:45:34.665 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-15 18:45:34.674 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-15 18:45:35.024 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-15 18:45:35.144 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-15 18:45:35.168 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-15 18:45:35.168 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-15 18:45:35.168 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-15 18:45:35.168 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-15 18:45:35.169 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-15 18:45:35.170 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-15 18:45:35.171 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@54d2887a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@73e63b4d, org.springframework.security.web.context.SecurityContextPersistenceFilter@61f08aa9, org.springframework.security.web.header.HeaderWriterFilter@5a9ef32e, org.springframework.web.filter.CorsFilter@8aafd70, org.springframework.security.web.authentication.logout.LogoutFilter@2bb84aa5, com.example.pure.filter.JwtFilter@50e24ea4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1a1f22f2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@37b5a51c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3c717ef2, org.springframework.security.web.session.SessionManagementFilter@e2ee348, org.springframework.security.web.access.ExceptionTranslationFilter@6f5288c5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1104ad6a]
2025-06-15 18:45:35.210 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6815c5f2, started on Sun Jun 15 18:45:29 CST 2025
2025-06-15 18:45:35.222 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-15 18:45:35.223 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-15 18:45:35.225 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-15 18:45:35.226 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-15 18:45:35.226 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-15 18:45:35.226 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-15 18:45:35.226 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-15 18:45:35.226 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-15 18:45:35.226 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-15 18:45:35.227 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-15 18:45:35.228 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-15 18:45:35.228 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-15 18:45:35.228 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-15 18:45:35.319 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 18:45:35.352 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 18:45:35.613 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-15 18:45:35.623 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 18:45:35.626 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 18:45:35.627 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 18:45:35.627 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-15 18:45:35.627 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33efa7d4]
2025-06-15 18:45:35.627 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33efa7d4]
2025-06-15 18:45:35.627 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33efa7d4]]
2025-06-15 18:45:35.628 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-15 18:45:35.628 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 18:45:35.628 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 18:45:35.642 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 6.508 seconds (JVM running for 7.849)
2025-06-15 18:45:36.445 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 18:45:36.446 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-15 18:45:36.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-15 18:45:36.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-15 18:45:36.446 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-15 18:45:36.449 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4c14f9b0
2025-06-15 18:45:36.450 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6734283a
2025-06-15 18:45:36.450 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-15 18:45:36.450 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-06-15 18:45:36.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 18:45:36.472 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 18:45:36.482 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 18:45:36.945 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 18:45:37.011 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:45:37.014 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@552f9eda]
2025-06-15 18:45:37.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@926464247 wrapping com.mysql.cj.jdbc.ConnectionImpl@208b3c9] will be managed by Spring
2025-06-15 18:45:37.021 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 18:45:37.039 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 18:45:37.066 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 18:45:37.067 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@552f9eda]
2025-06-15 18:45:37.068 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 18:45:37.069 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@552f9eda]
2025-06-15 18:45:37.069 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@552f9eda]
2025-06-15 18:45:37.069 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@552f9eda]
2025-06-15 18:45:37.104 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 18:45:37.109 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 18:45:37.110 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 18:45:37.112 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 18:45:37.113 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 18:45:37.137 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Abc123456, email=null, deviceId=null)]
2025-06-15 18:45:37.226 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 18:45:37.226 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 18:52:21.191 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-15 18:52:21.191 [31mWARN [0;39m [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m17s833ms544µs500ns).
2025-06-15 18:52:22.916 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 18:52:22.924 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:52:22.925 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55749971]
2025-06-15 18:52:22.925 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@628848328 wrapping com.mysql.cj.jdbc.ConnectionImpl@208b3c9] will be managed by Spring
2025-06-15 18:52:22.926 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 18:52:22.927 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 18:52:22.941 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 18:52:22.941 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55749971]
2025-06-15 18:52:22.941 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 18:52:22.942 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55749971]
2025-06-15 18:52:22.942 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55749971]
2025-06-15 18:52:22.942 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55749971]
2025-06-15 18:52:24.305 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-06-15 18:52:24.502 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户 hao111 设备数量已达上限，移除最早登录的设备: 507e0a9b-2402-41b8-962d-df6946161fd1
2025-06-15 18:52:24.513 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 通过token移除用户 hao111 设备成功
2025-06-15 18:52:24.562 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户 hao111 添加设备 1320eaf2-6c12-4d04-852c-1535093401c0 成功，使用token作为标识
2025-06-15 18:52:24.563 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户 hao111 登录设备超过限制，已移除最早登录的设备: eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************.6F3Y_lriSAGNIUdgn_yHwBCGf4pd_P3SLPQDFDWKS18
2025-06-15 18:52:24.661 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 8
2025-06-15 18:52:24.664 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:52:24.665 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@********]
2025-06-15 18:52:24.722 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@208b3c9] will be managed by Spring
2025-06-15 18:52:24.723 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, updated_time = NOW() WHERE id = ?
2025-06-15 18:52:24.727 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==> Parameters: hao111(String), $2a$10$7B6SHc03VdBxyUwqS61BiuFn7OLfJdVtVsRsyMuc.6sAZwbgiKzne(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-15 18:52:24.6364439(Timestamp), eyJhbGciOiJIUzI1NiJ9.*******************************************************************.rxBIR31meF9a0rlpuvRhmZnA4URXTJZ96JA_bOz5hEk(String), 8(Long)
2025-06-15 18:52:24.763 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-15 18:52:24.763 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@********]
2025-06-15 18:52:24.769 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 8
2025-06-15 18:52:24.770 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@********]
2025-06-15 18:52:24.770 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@********]
2025-06-15 18:52:24.770 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@********]
2025-06-15 18:52:24.801 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户登录成功: hao111
2025-06-15 18:52:24.823 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 根据用户名查找用户: hao111
2025-06-15 18:52:24.874 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:52:24.874 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3924bda1]
2025-06-15 18:52:24.874 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@934779125 wrapping com.mysql.cj.jdbc.ConnectionImpl@208b3c9] will be managed by Spring
2025-06-15 18:52:24.874 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-06-15 18:52:24.875 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 8(Long), LOGIN(String), 2025-06-15(String)
2025-06-15 18:52:24.888 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 1
2025-06-15 18:52:24.888 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3924bda1]
2025-06-15 18:52:24.889 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3924bda1]
2025-06-15 18:52:24.889 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3924bda1]
2025-06-15 18:52:24.889 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3924bda1]
2025-06-15 18:52:24.948 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 18:52:24.949 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@669e2454]
2025-06-15 18:52:24.949 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@22822544 wrapping com.mysql.cj.jdbc.ConnectionImpl@208b3c9] will be managed by Spring
2025-06-15 18:52:24.949 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-15 18:52:24.950 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 8(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户hao111执行了登陆(String), 2025-06-15T18:52:24.936083500(LocalDateTime)
2025-06-15 18:52:24.972 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-15 18:52:24.973 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@669e2454]
2025-06-15 18:52:24.973 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=8, summary=用户hao111执行了登陆
2025-06-15 18:52:24.974 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@669e2454]
2025-06-15 18:52:24.974 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@669e2454]
2025-06-15 18:52:24.974 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@669e2454]
2025-06-15 18:52:25.004 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 18:52:25.017 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=hao111, accessToken=eyJhbGc (truncated)...]
2025-06-15 18:52:25.028 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleClientAbortException(ClientAbortException)
2025-06-15 18:52:25.028 [31mWARN [0;39m [http-nio-8080-exec-1] c.e.p.e.GlobalExceptionHandler : 客户端中断连接: java.io.IOException: An established connection was aborted by the software in your host machine
2025-06-15 18:52:25.040 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:application/json' in response
2025-06-15 18:52:25.040 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Nothing to write: null body
2025-06-15 18:52:25.040 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.apache.catalina.connector.ClientAbortException: java.io.IOException: An established connection was aborted by the software in your host machine]
2025-06-15 18:52:25.041 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-15 18:52:25.041 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 19:13:06.840 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 19:13:06.840 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 19:13:06.840 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-15 19:13:06.840 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33efa7d4]]
2025-06-15 19:13:06.840 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33efa7d4]
2025-06-15 19:13:06.840 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@33efa7d4]
2025-06-15 19:13:06.840 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-15 19:13:06.840 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:13:06.840 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:13:07.322 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-15 19:13:07.331 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-15 19:13:13.210 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-15 19:13:13.212 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 29204 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-15 19:13:13.213 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-15 19:13:13.213 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-15 19:13:14.277 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-15 19:13:14.279 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-15 19:13:14.321 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-06-15 19:13:14.438 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-15 19:13:14.439 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-15 19:13:14.440 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-15 19:13:14.442 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-15 19:13:14.442 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-15 19:13:14.442 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-15 19:13:14.442 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-15 19:13:14.443 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-15 19:13:14.443 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-15 19:13:14.443 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-15 19:13:14.443 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-15 19:13:14.443 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-15 19:13:14.444 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-15 19:13:14.444 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-15 19:13:14.444 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-15 19:13:14.444 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-15 19:13:14.444 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-15 19:13:14.445 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-15 19:13:14.445 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-15 19:13:14.445 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-15 19:13:14.445 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-15 19:13:14.445 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-15 19:13:14.445 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-15 19:13:14.446 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-15 19:13:14.446 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-15 19:13:14.446 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-15 19:13:14.446 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-15 19:13:14.446 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-15 19:13:14.446 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-15 19:13:14.447 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-15 19:13:15.027 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-15 19:13:15.033 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-15 19:13:15.034 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-15 19:13:15.034 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-15 19:13:15.127 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-15 19:13:15.127 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1866 ms
2025-06-15 19:13:15.387 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-15 19:13:15.398 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-15 19:13:15.406 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-15 19:13:15.412 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-15 19:13:15.419 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-15 19:13:15.424 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-15 19:13:15.430 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-15 19:13:15.435 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-15 19:13:15.442 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-15 19:13:15.449 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-15 19:13:15.461 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-15 19:13:15.468 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-15 19:13:15.472 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-15 19:13:15.477 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-15 19:13:15.490 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-15 19:13:15.930 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-15 19:13:16.590 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-15 19:13:16.591 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-15 19:13:16.960 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-15 19:13:16.962 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.042 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-15 19:13:17.044 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.045 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-15 19:13:17.045 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.046 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:13:17.047 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.052 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-15 19:13:17.053 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.053 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:13:17.053 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:13:17.157 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-15 19:13:17.159 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-15 19:13:17.163 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@412440c1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36c45149, org.springframework.security.web.context.SecurityContextPersistenceFilter@72c5064f, org.springframework.security.web.header.HeaderWriterFilter@74bcf1ab, org.springframework.security.web.authentication.logout.LogoutFilter@7a13ad55, com.example.pure.filter.JwtFilter@733bd6f3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@453439e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2d33795c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33f4844b, org.springframework.security.web.session.SessionManagementFilter@675bf541, org.springframework.security.web.access.ExceptionTranslationFilter@2d1f3639, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1b82f62a]
2025-06-15 19:13:17.166 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-15 19:13:17.169 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-15 19:13:17.365 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-15 19:13:17.390 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-15 19:13:17.453 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-15 19:13:17.461 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-15 19:13:17.809 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-15 19:13:17.956 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-15 19:13:17.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-15 19:13:17.979 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-15 19:13:17.980 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-15 19:13:17.981 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-15 19:13:17.982 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-15 19:13:17.982 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@16c1345b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@278cbf5a, org.springframework.security.web.context.SecurityContextPersistenceFilter@7c6fc278, org.springframework.security.web.header.HeaderWriterFilter@448fa659, org.springframework.web.filter.CorsFilter@1c41931a, org.springframework.security.web.authentication.logout.LogoutFilter@4965454c, com.example.pure.filter.JwtFilter@733bd6f3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23ee92df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1a6a4595, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@62cb977a, org.springframework.security.web.session.SessionManagementFilter@45c28c49, org.springframework.security.web.access.ExceptionTranslationFilter@4113d9ab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3b28ab9b]
2025-06-15 19:13:18.024 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64f857e7, started on Sun Jun 15 19:13:13 CST 2025
2025-06-15 19:13:18.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-15 19:13:18.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-15 19:13:18.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-15 19:13:18.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-15 19:13:18.046 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-15 19:13:18.047 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-15 19:13:18.047 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-15 19:13:18.047 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-15 19:13:18.047 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-15 19:13:18.047 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-15 19:13:18.047 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-15 19:13:18.048 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-15 19:13:18.049 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-15 19:13:18.049 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-15 19:13:18.049 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-15 19:13:18.143 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 19:13:18.174 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 19:13:18.439 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-15 19:13:18.448 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 19:13:18.450 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 19:13:18.450 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 19:13:18.450 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-15 19:13:18.450 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41ae9f11]
2025-06-15 19:13:18.450 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41ae9f11]
2025-06-15 19:13:18.451 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41ae9f11]]
2025-06-15 19:13:18.451 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-15 19:13:18.451 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:13:18.451 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:13:18.465 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.792 seconds (JVM running for 6.713)
2025-06-15 19:13:27.679 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 19:13:27.679 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-15 19:13:27.679 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-15 19:13:27.679 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-15 19:13:27.680 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-15 19:13:27.681 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@8ae30d3
2025-06-15 19:13:27.682 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@7fd4c26a
2025-06-15 19:13:27.682 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-15 19:13:27.682 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-15 19:13:27.695 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 19:13:27.698 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 19:13:27.707 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 19:13:28.161 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 19:13:28.248 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:13:28.251 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fd4d5b]
2025-06-15 19:13:28.257 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1533855302 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c9a02f1] will be managed by Spring
2025-06-15 19:13:28.259 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 19:13:28.276 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 19:13:28.306 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 19:13:28.307 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fd4d5b]
2025-06-15 19:13:28.308 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 19:13:28.308 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fd4d5b]
2025-06-15 19:13:28.309 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fd4d5b]
2025-06-15 19:13:28.309 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6fd4d5b]
2025-06-15 19:13:28.351 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 19:13:28.356 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 19:13:28.356 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 19:13:28.359 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 19:13:28.360 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 19:13:28.384 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Abc123456, email=null, deviceId=null)]
2025-06-15 19:13:28.479 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 19:13:28.479 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 19:14:34.294 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-15 19:15:33.296 [31mWARN [0;39m [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=59s1ms276µs500ns).
2025-06-15 19:15:33.322 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 19:15:33.322 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 19:15:33.322 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-15 19:15:33.322 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41ae9f11]]
2025-06-15 19:15:33.322 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41ae9f11]
2025-06-15 19:15:33.323 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41ae9f11]
2025-06-15 19:15:33.323 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-15 19:15:33.323 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:15:33.323 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:15:34.955 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 19:15:34.963 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:15:34.964 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97f877d]
2025-06-15 19:15:34.965 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1327727618 wrapping com.mysql.cj.jdbc.ConnectionImpl@6c9a02f1] will be managed by Spring
2025-06-15 19:15:34.965 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 19:15:34.967 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 19:15:34.983 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 19:15:34.984 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97f877d]
2025-06-15 19:15:34.984 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 19:15:34.985 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97f877d]
2025-06-15 19:15:34.985 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97f877d]
2025-06-15 19:15:34.985 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@97f877d]
2025-06-15 19:15:36.418 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-06-15 19:15:36.576 [1;31mERROR[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户登录失败: Redis command interrupted; nested exception is io.lettuce.core.RedisCommandInterruptedException: Command interrupted
org.springframework.data.redis.RedisSystemException: Redis command interrupted; nested exception is io.lettuce.core.RedisCommandInterruptedException: Command interrupted
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:62)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:277)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1085)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:938)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:279)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:438)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:58)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:61)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.example.pure.util.IpUtil.getClientIp(IpUtil.java:42)
	at com.example.pure.service.impl.DeviceServiceImpl.createDeviceInfo(DeviceServiceImpl.java:70)
	at com.example.pure.service.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.AuthController.login(AuthController.java:112)
	at com.example.pure.controller.AuthController$$FastClassBySpringCGLIB$$7ebf42b1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.AuthController$$EnhancerBySpringCGLIB$$61111894.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: io.lettuce.core.RedisCommandInterruptedException: Command interrupted
	at io.lettuce.core.protocol.AsyncCommand.await(AsyncCommand.java:87)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:244)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1083)
	... 136 common frames omitted
Caused by: java.lang.InterruptedException: null
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:385)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2028)
	at io.lettuce.core.protocol.AsyncCommand.await(AsyncCommand.java:83)
	... 139 common frames omitted
2025-06-15 19:15:36.590 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-15 19:15:36.591 [31mWARN [0;39m [http-nio-8080-exec-1] c.e.p.e.GlobalExceptionHandler : 业务异常: 登录失败: 账号或密码输入无效
2025-06-15 19:15:36.596 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 19:15:36.603 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=登录失败: 账号或密码输入无效, success=false, data=null, time=2025-06-15T11:15:36.5917564 (truncated)...]
2025-06-15 19:15:36.611 [31mWARN [0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
org.apache.catalina.connector.ClientAbortException: java.nio.channels.ClosedChannelException
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:348)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:777)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:298)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:271)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:120)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:523)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1187)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1009)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:456)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.nio.channels.ClosedChannelException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1396)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:600)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:544)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:540)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.filters.GzipOutputFilter$FakeOutputStream.write(GzipOutputFilter.java:159)
	at java.base/java.io.OutputStream.write(OutputStream.java:122)
	at java.base/java.util.zip.GZIPOutputStream.writeHeader(GZIPOutputStream.java:189)
	at java.base/java.util.zip.GZIPOutputStream.<init>(GZIPOutputStream.java:95)
	at java.base/java.util.zip.GZIPOutputStream.<init>(GZIPOutputStream.java:132)
	at org.apache.coyote.http11.filters.GzipOutputFilter.doWrite(GzipOutputFilter.java:65)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:193)
	at org.apache.coyote.Response.doWrite(Response.java:606)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:335)
	... 114 common frames omitted
2025-06-15 19:15:36.614 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Failed to complete request: com.example.pure.exception.BusinessException: 登录失败: 账号或密码输入无效
2025-06-15 19:15:36.615 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 19:23:33.478 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-15 19:23:33.484 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 32356 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-15 19:23:33.485 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-15 19:23:33.485 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-15 19:23:34.537 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-15 19:23:34.539 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-15 19:23:34.575 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-15 19:23:34.685 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-15 19:23:34.685 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-15 19:23:34.686 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-15 19:23:34.687 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-15 19:23:34.688 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-15 19:23:34.688 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-15 19:23:34.688 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-15 19:23:34.688 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-15 19:23:34.689 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-15 19:23:34.689 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-15 19:23:34.689 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-15 19:23:34.689 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-15 19:23:34.689 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-15 19:23:34.689 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-15 19:23:34.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-15 19:23:34.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-15 19:23:34.692 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-15 19:23:34.692 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-15 19:23:34.692 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-15 19:23:35.290 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-15 19:23:35.295 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-15 19:23:35.296 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-15 19:23:35.296 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-15 19:23:35.389 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-15 19:23:35.389 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1849 ms
2025-06-15 19:23:35.670 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-15 19:23:35.684 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-15 19:23:35.691 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-15 19:23:35.700 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-15 19:23:35.709 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-15 19:23:35.715 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-15 19:23:35.723 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-15 19:23:35.730 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-15 19:23:35.738 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-15 19:23:35.746 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-15 19:23:35.761 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-15 19:23:35.768 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-15 19:23:35.773 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-15 19:23:35.780 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-15 19:23:35.794 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-15 19:23:36.249 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-15 19:23:37.630 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-15 19:23:37.643 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-15 19:23:38.266 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-15 19:23:38.269 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.381 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-15 19:23:38.381 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.383 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:23:38.383 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.383 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-15 19:23:38.383 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.383 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-15 19:23:38.385 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.386 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-15 19:23:38.386 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.387 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:23:38.387 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.392 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-15 19:23:38.392 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.393 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:23:38.393 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:23:38.533 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-15 19:23:38.535 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-15 19:23:38.541 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@71891d6b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5ce41f1f, org.springframework.security.web.context.SecurityContextPersistenceFilter@6686507b, org.springframework.security.web.header.HeaderWriterFilter@7a14ab66, org.springframework.security.web.authentication.logout.LogoutFilter@7cc3a7f7, com.example.pure.filter.JwtFilter@20518250, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2e5c7cd5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@f252762, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@56820446, org.springframework.security.web.session.SessionManagementFilter@7ce760af, org.springframework.security.web.access.ExceptionTranslationFilter@483ed60e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3b29d36c]
2025-06-15 19:23:38.545 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-15 19:23:38.548 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-15 19:23:38.736 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-15 19:23:38.764 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-15 19:23:38.832 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-15 19:23:38.842 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-15 19:23:39.177 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-15 19:23:39.324 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-15 19:23:39.348 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-15 19:23:39.349 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-15 19:23:39.350 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-15 19:23:39.351 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-15 19:23:39.352 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4844e24b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2571bdf4, org.springframework.security.web.context.SecurityContextPersistenceFilter@237ee2e1, org.springframework.security.web.header.HeaderWriterFilter@68fc636a, org.springframework.web.filter.CorsFilter@241abc2, org.springframework.security.web.authentication.logout.LogoutFilter@29c21acb, com.example.pure.filter.JwtFilter@20518250, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e00d737, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@656c0eae, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7e5e7753, org.springframework.security.web.session.SessionManagementFilter@18301763, org.springframework.security.web.access.ExceptionTranslationFilter@7be6dabb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c981f76]
2025-06-15 19:23:39.393 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64f857e7, started on Sun Jun 15 19:23:33 CST 2025
2025-06-15 19:23:39.410 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-15 19:23:39.411 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-15 19:23:39.412 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-15 19:23:39.412 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-15 19:23:39.412 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-15 19:23:39.416 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-15 19:23:39.417 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-15 19:23:39.417 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-15 19:23:39.417 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-15 19:23:39.417 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-15 19:23:39.418 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-15 19:23:39.418 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-06-15 19:23:39.419 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-15 19:23:39.420 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-15 19:23:39.420 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-15 19:23:39.420 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-15 19:23:39.509 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 19:23:39.538 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 19:23:39.807 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-15 19:23:39.817 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 19:23:39.819 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 19:23:39.820 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 19:23:39.820 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-15 19:23:39.820 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39a1c200]
2025-06-15 19:23:39.820 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39a1c200]
2025-06-15 19:23:39.820 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39a1c200]]
2025-06-15 19:23:39.820 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-15 19:23:39.820 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:23:39.820 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:23:39.835 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 6.884 seconds (JVM running for 7.87)
2025-06-15 19:23:42.960 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 19:23:42.960 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-15 19:23:42.961 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-15 19:23:42.961 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-15 19:23:42.961 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-15 19:23:42.963 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4722c56b
2025-06-15 19:23:42.963 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@5e9f7b6e
2025-06-15 19:23:42.963 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-15 19:23:42.964 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-06-15 19:23:42.980 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 19:23:42.985 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 19:23:42.996 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 19:23:43.518 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 19:23:43.596 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:23:43.600 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b46f908]
2025-06-15 19:23:43.606 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1374354768 wrapping com.mysql.cj.jdbc.ConnectionImpl@23fb419d] will be managed by Spring
2025-06-15 19:23:43.609 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 19:23:43.629 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 19:23:43.661 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 19:23:43.662 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b46f908]
2025-06-15 19:23:43.664 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 19:23:43.664 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b46f908]
2025-06-15 19:23:43.664 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b46f908]
2025-06-15 19:23:43.665 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b46f908]
2025-06-15 19:23:43.705 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 19:23:43.711 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 19:23:43.712 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 19:23:43.715 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 19:23:43.717 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 19:23:43.748 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Abc123456, email=null, deviceId=null)]
2025-06-15 19:23:43.868 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 19:23:43.869 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 19:37:55.240 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-15 19:37:55.240 [31mWARN [0;39m [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=14m18s884ms64µs300ns).
2025-06-15 19:37:55.270 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 19:37:55.270 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 19:37:55.270 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-15 19:37:55.270 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39a1c200]]
2025-06-15 19:37:55.270 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39a1c200]
2025-06-15 19:37:55.270 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39a1c200]
2025-06-15 19:37:55.270 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-15 19:37:55.270 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:37:55.270 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:37:57.304 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 19:37:57.316 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:37:57.316 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5132a9bc]
2025-06-15 19:37:57.316 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1699580471 wrapping com.mysql.cj.jdbc.ConnectionImpl@23fb419d] will be managed by Spring
2025-06-15 19:37:57.317 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 19:37:57.319 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 19:37:57.336 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 19:37:57.337 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5132a9bc]
2025-06-15 19:37:57.337 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 19:37:57.337 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5132a9bc]
2025-06-15 19:37:57.338 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5132a9bc]
2025-06-15 19:37:57.338 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5132a9bc]
2025-06-15 19:37:58.774 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-06-15 19:37:58.973 [1;31mERROR[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户登录失败: Redis command interrupted; nested exception is io.lettuce.core.RedisCommandInterruptedException: Command interrupted
org.springframework.data.redis.RedisSystemException: Redis command interrupted; nested exception is io.lettuce.core.RedisCommandInterruptedException: Command interrupted
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:62)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:277)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1085)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:938)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:279)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:438)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:58)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:61)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.example.pure.util.IpUtil.getClientIp(IpUtil.java:42)
	at com.example.pure.service.impl.DeviceServiceImpl.createDeviceInfo(DeviceServiceImpl.java:70)
	at com.example.pure.service.impl.AuthServiceImpl.login(AuthServiceImpl.java:151)
	at com.example.pure.controller.AuthController.login(AuthController.java:112)
	at com.example.pure.controller.AuthController$$FastClassBySpringCGLIB$$7ebf42b1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.AuthController$$EnhancerBySpringCGLIB$$472f29bc.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: io.lettuce.core.RedisCommandInterruptedException: Command interrupted
	at io.lettuce.core.protocol.AsyncCommand.await(AsyncCommand.java:87)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:244)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1083)
	... 136 common frames omitted
Caused by: java.lang.InterruptedException: null
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:385)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2028)
	at io.lettuce.core.protocol.AsyncCommand.await(AsyncCommand.java:83)
	... 139 common frames omitted
2025-06-15 19:37:58.990 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-15 19:37:58.990 [31mWARN [0;39m [http-nio-8080-exec-1] c.e.p.e.GlobalExceptionHandler : 业务异常: 登录失败: 账号或密码输入无效
2025-06-15 19:37:58.998 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 19:37:59.006 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=登录失败: 账号或密码输入无效, success=false, data=null, time=2025-06-15T11:37:58.9907985 (truncated)...]
2025-06-15 19:37:59.016 [31mWARN [0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
org.apache.catalina.connector.ClientAbortException: java.nio.channels.ClosedChannelException
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:348)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:777)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:298)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:271)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:120)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:523)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1187)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1009)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:456)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.nio.channels.ClosedChannelException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1396)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:600)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:544)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:540)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.filters.GzipOutputFilter$FakeOutputStream.write(GzipOutputFilter.java:159)
	at java.base/java.io.OutputStream.write(OutputStream.java:122)
	at java.base/java.util.zip.GZIPOutputStream.writeHeader(GZIPOutputStream.java:189)
	at java.base/java.util.zip.GZIPOutputStream.<init>(GZIPOutputStream.java:95)
	at java.base/java.util.zip.GZIPOutputStream.<init>(GZIPOutputStream.java:132)
	at org.apache.coyote.http11.filters.GzipOutputFilter.doWrite(GzipOutputFilter.java:65)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:193)
	at org.apache.coyote.Response.doWrite(Response.java:606)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:335)
	... 114 common frames omitted
2025-06-15 19:37:59.019 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Failed to complete request: com.example.pure.exception.BusinessException: 登录失败: 账号或密码输入无效
2025-06-15 19:37:59.020 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 19:37:59.162 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-15 19:37:59.173 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-15 19:38:24.788 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-15 19:38:24.792 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 21384 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-15 19:38:24.793 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-15 19:38:24.793 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-15 19:38:25.723 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-15 19:38:25.724 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-15 19:38:25.760 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-06-15 19:38:25.859 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-15 19:38:25.859 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-15 19:38:25.859 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-15 19:38:25.860 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-15 19:38:25.861 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-15 19:38:25.862 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-15 19:38:25.862 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-15 19:38:25.862 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-15 19:38:25.862 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-15 19:38:25.862 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-15 19:38:25.862 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-15 19:38:25.863 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-15 19:38:25.863 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-15 19:38:25.863 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-15 19:38:25.863 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-15 19:38:25.863 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-15 19:38:25.863 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-15 19:38:25.864 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-15 19:38:25.864 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-15 19:38:25.864 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-15 19:38:25.864 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-15 19:38:25.865 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-15 19:38:25.865 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-15 19:38:25.865 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-15 19:38:25.865 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-15 19:38:25.865 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-15 19:38:25.865 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-15 19:38:25.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-15 19:38:25.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-15 19:38:25.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-15 19:38:25.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-15 19:38:25.866 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-15 19:38:26.397 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-15 19:38:26.403 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-15 19:38:26.404 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-15 19:38:26.404 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-15 19:38:26.494 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-15 19:38:26.494 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1654 ms
2025-06-15 19:38:26.749 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-15 19:38:26.761 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-15 19:38:26.768 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-15 19:38:26.777 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-15 19:38:26.784 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-15 19:38:26.788 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-15 19:38:26.795 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-15 19:38:26.801 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-15 19:38:26.808 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-15 19:38:26.815 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-15 19:38:26.830 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-15 19:38:26.836 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-15 19:38:26.841 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-15 19:38:26.847 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-15 19:38:26.858 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-15 19:38:27.283 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-15 19:38:27.906 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-15 19:38:27.907 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-15 19:38:28.242 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-15 19:38:28.244 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.319 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-15 19:38:28.319 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.321 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-15 19:38:28.322 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.322 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:38:28.322 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.323 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-15 19:38:28.323 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.323 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-15 19:38:28.323 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.323 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:38:28.323 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.328 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-15 19:38:28.328 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.329 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-15 19:38:28.329 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-15 19:38:28.429 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-15 19:38:28.431 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-15 19:38:28.436 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@7aa1fb0e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5e663ab, org.springframework.security.web.context.SecurityContextPersistenceFilter@a11efe6, org.springframework.security.web.header.HeaderWriterFilter@1a7437d8, org.springframework.security.web.authentication.logout.LogoutFilter@608c2042, com.example.pure.filter.JwtFilter@14bf9fd0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69356aca, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a17ffee, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b74a67a, org.springframework.security.web.session.SessionManagementFilter@2d1f3639, org.springframework.security.web.access.ExceptionTranslationFilter@6c6b00f1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7a7f2247]
2025-06-15 19:38:28.437 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-15 19:38:28.440 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-15 19:38:28.641 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-15 19:38:28.668 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-15 19:38:28.738 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-06-15 19:38:28.746 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-15 19:38:29.070 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-15 19:38:29.203 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-15 19:38:29.228 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-15 19:38:29.229 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-15 19:38:29.230 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-15 19:38:29.230 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-15 19:38:29.230 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-15 19:38:29.230 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-15 19:38:29.230 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-15 19:38:29.230 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-15 19:38:29.230 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@68838767, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@315365ef, org.springframework.security.web.context.SecurityContextPersistenceFilter@2202c92f, org.springframework.security.web.header.HeaderWriterFilter@5c20505f, org.springframework.web.filter.CorsFilter@1f381eaf, org.springframework.security.web.authentication.logout.LogoutFilter@56adbb07, com.example.pure.filter.JwtFilter@14bf9fd0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@440d2d64, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1a712f12, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5df7ae0a, org.springframework.security.web.session.SessionManagementFilter@cce92b5, org.springframework.security.web.access.ExceptionTranslationFilter@6aa18912, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@147aceec]
2025-06-15 19:38:29.268 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64f857e7, started on Sun Jun 15 19:38:24 CST 2025
2025-06-15 19:38:29.281 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-15 19:38:29.282 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-15 19:38:29.286 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-15 19:38:29.287 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-15 19:38:29.287 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-15 19:38:29.287 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-15 19:38:29.287 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-15 19:38:29.287 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-15 19:38:29.288 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
2025-06-15 19:38:29.289 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-15 19:38:29.289 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-15 19:38:29.289 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-15 19:38:29.290 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-15 19:38:29.382 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 19:38:29.413 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 19:38:29.668 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-15 19:38:29.677 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 19:38:29.678 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-15 19:38:29.679 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-15 19:38:29.679 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-15 19:38:29.679 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@160d68b8]
2025-06-15 19:38:29.679 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@160d68b8]
2025-06-15 19:38:29.679 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@160d68b8]]
2025-06-15 19:38:29.679 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-15 19:38:29.679 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:38:29.679 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-15 19:38:29.694 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 5.386 seconds (JVM running for 6.205)
2025-06-15 19:38:49.835 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 19:38:49.835 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-15 19:38:49.836 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-15 19:38:49.836 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-15 19:38:49.836 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-15 19:38:49.838 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@5e2ac07d
2025-06-15 19:38:49.838 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@32a04e59
2025-06-15 19:38:49.838 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-15 19:38:49.838 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-06-15 19:38:49.852 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 19:38:49.856 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 19:38:49.865 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 19:38:50.329 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 19:38:50.410 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:38:50.413 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@95be265]
2025-06-15 19:38:50.418 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@118141579 wrapping com.mysql.cj.jdbc.ConnectionImpl@50d05b64] will be managed by Spring
2025-06-15 19:38:50.420 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 19:38:50.439 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 19:38:50.467 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 19:38:50.468 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@95be265]
2025-06-15 19:38:50.470 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 19:38:50.470 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@95be265]
2025-06-15 19:38:50.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@95be265]
2025-06-15 19:38:50.471 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@95be265]
2025-06-15 19:38:50.505 [39mDEBUG[0;39m [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 19:38:50.510 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 19:38:50.510 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 19:38:50.513 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 19:38:50.515 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 19:38:50.538 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Abc123456, email=null, deviceId=null)]
2025-06-15 19:38:50.641 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 19:38:50.641 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 19:41:07.762 [31mWARN [0;39m [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m40s363ms653µs600ns).
2025-06-15 19:41:07.762 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-15 19:41:09.185 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 19:41:09.195 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:41:09.195 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a32214c]
2025-06-15 19:41:09.196 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1918903689 wrapping com.mysql.cj.jdbc.ConnectionImpl@50d05b64] will be managed by Spring
2025-06-15 19:41:09.196 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 19:41:09.198 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 19:41:09.212 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 19:41:09.212 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a32214c]
2025-06-15 19:41:09.213 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 19:41:09.213 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a32214c]
2025-06-15 19:41:09.213 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a32214c]
2025-06-15 19:41:09.214 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a32214c]
2025-06-15 19:41:10.737 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-06-15 19:41:10.983 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户 hao111 设备数量已达上限，移除最早登录的设备: 55115cc5-c44a-4b10-80d5-db5cc832548b
2025-06-15 19:41:10.994 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 通过token移除用户 hao111 设备成功
2025-06-15 19:41:11.059 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.DeviceServiceImpl : 用户 hao111 添加设备 ae4e7aee-7f87-46db-a66b-157117e9b77b 成功，使用token作为标识
2025-06-15 19:41:11.059 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户 hao111 登录设备超过限制，已移除最早登录的设备: eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************.2CP8dzOdGeXkFMUK-9iz3CuVCFf4DBHw-NXmahSSBTI
2025-06-15 19:41:11.195 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 8
2025-06-15 19:41:11.200 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:41:11.201 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52be7051]
2025-06-15 19:41:11.277 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@50d05b64] will be managed by Spring
2025-06-15 19:41:11.279 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, updated_time = NOW() WHERE id = ?
2025-06-15 19:41:11.286 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : ==> Parameters: hao111(String), $2a$10$7B6SHc03VdBxyUwqS61BiuFn7OLfJdVtVsRsyMuc.6sAZwbgiKzne(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-15 19:41:11.1696466(Timestamp), eyJhbGciOiJIUzI1NiJ9.*******************************************************************.OKiCIYtcr1cSE4HM8hLQaDW9R-bc47wYsv1VjH6-Hr4(String), 8(Long)
2025-06-15 19:41:11.327 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-15 19:41:11.328 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52be7051]
2025-06-15 19:41:11.336 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 8
2025-06-15 19:41:11.338 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52be7051]
2025-06-15 19:41:11.338 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52be7051]
2025-06-15 19:41:11.339 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52be7051]
2025-06-15 19:41:11.372 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.service.impl.AuthServiceImpl : 用户登录成功: hao111
2025-06-15 19:41:11.395 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.service.impl.UserServiceImpl : 根据用户名查找用户: hao111
2025-06-15 19:41:11.514 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 19:41:11.514 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2084b64d]
2025-06-15 19:41:11.514 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1179596169 wrapping com.mysql.cj.jdbc.ConnectionImpl@50d05b64] will be managed by Spring
2025-06-15 19:41:11.515 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-15 19:41:11.516 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 8(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户hao111执行了登陆(String), 2025-06-15T19:41:11.499832700(LocalDateTime)
2025-06-15 19:41:11.539 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-15 19:41:11.542 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2084b64d]
2025-06-15 19:41:11.542 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=8, summary=用户hao111执行了登陆
2025-06-15 19:41:11.542 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2084b64d]
2025-06-15 19:41:11.542 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2084b64d]
2025-06-15 19:41:11.543 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2084b64d]
2025-06-15 19:41:11.576 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 19:41:11.594 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=hao111, accessToken=eyJhbGc (truncated)...]
2025-06-15 19:41:11.608 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-15 19:41:11.610 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 20:11:07.772 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-15 20:41:07.779 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-15 21:11:07.788 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-06-15 21:41:07.789 [34mINFO [0;39m [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-06-15 22:11:07.790 [34mINFO [0;39m [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-06-15 22:41:07.791 [34mINFO [0;39m [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-06-15 23:11:07.806 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-06-15 23:41:07.818 [34mINFO [0;39m [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-06-15 23:56:55.380 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing POST /api/verification/send-email-code?email=<EMAIL>
2025-06-15 23:56:55.380 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 23:56:55.384 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VerificationController#sendEmailVerificationCode(String)
2025-06-15 23:56:55.446 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 23:56:55.457 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:56:55.458 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.459 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2067916759 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:56:55.459 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-06-15 23:56:55.461 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: hao111(String)
2025-06-15 23:56:55.481 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-06-15 23:56:55.482 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.494 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2] from current transaction
2025-06-15 23:56:55.494 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserWithPasswordByUserId : ==>  Preparing: SELECT * FROM user WHERE id = ?
2025-06-15 23:56:55.494 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserWithPasswordByUserId : ==> Parameters: 8(Long)
2025-06-15 23:56:55.509 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.U.findUserWithPasswordByUserId : <==      Total: 1
2025-06-15 23:56:55.510 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.510 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2] from current transaction
2025-06-15 23:56:55.510 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 23:56:55.510 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 23:56:55.522 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 23:56:55.523 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.523 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 23:56:55.523 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.523 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.523 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e32d2d2]
2025-06-15 23:56:55.595 [39mDEBUG[0;39m [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 23:56:55.596 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/verification/send-email-code?email=<EMAIL>] with attributes [permitAll]
2025-06-15 23:56:55.597 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured POST /api/verification/send-email-code?email=<EMAIL>
2025-06-15 23:56:55.598 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : POST "/api/verification/send-email-code?email=<EMAIL>", parameters={masked}
2025-06-15 23:56:55.599 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.VerificationController#sendEmailVerificationCode(String)
2025-06-15 23:56:55.631 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.c.VerificationController : 发送验证码到邮箱: <EMAIL>
2025-06-15 23:56:57.116 [34mINFO [0;39m [http-nio-8080-exec-5] c.e.p.s.impl.VerificationServiceImpl : 验证码已发送到邮箱: <EMAIL>, 验证码: CE9OXO
2025-06-15 23:56:57.118 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 23:56:57.119 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=验证码已发送到您的邮箱, success=true, data=null, time=2025-06-15T15:56:57.117455800Z)]
2025-06-15 23:56:57.121 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-15 23:56:57.121 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 23:57:52.154 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing PUT /api/user/password
2025-06-15 23:57:52.154 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 23:57:52.155 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#updatePassword(PasswordUpdateDTO)
2025-06-15 23:57:52.196 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 23:57:52.201 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:57:52.202 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fcc6777]
2025-06-15 23:57:52.202 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@155343496 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:57:52.202 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 23:57:52.202 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 23:57:52.214 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 23:57:52.214 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fcc6777]
2025-06-15 23:57:52.214 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 23:57:52.214 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fcc6777]
2025-06-15 23:57:52.214 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fcc6777]
2025-06-15 23:57:52.214 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5fcc6777]
2025-06-15 23:57:52.248 [39mDEBUG[0;39m [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 23:57:52.249 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [PUT /api/user/password] with attributes [authenticated]
2025-06-15 23:57:52.249 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured PUT /api/user/password
2025-06-15 23:57:52.250 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : PUT "/api/user/password", parameters={}
2025-06-15 23:57:52.250 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.UserController#updatePassword(PasswordUpdateDTO)
2025-06-15 23:57:52.263 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [PasswordUpdateDTO(email=<EMAIL>, newPassword=Ah123456, verifyCode=CE9OXO)]
2025-06-15 23:57:52.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.s.impl.UserProfileServiceImpl : 根据邮箱查找用户ID: <EMAIL>
2025-06-15 23:57:52.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:57:52.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51bb6eb0] was not registered for synchronization because synchronization is not active
2025-06-15 23:57:52.306 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1333791009 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will not be managed by Spring
2025-06-15 23:57:52.307 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserIdByEmail : ==>  Preparing: SELECT id FROM user_profile WHERE email = ? LIMIT 1
2025-06-15 23:57:52.307 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserIdByEmail : ==> Parameters: <EMAIL>(String)
2025-06-15 23:57:52.322 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.U.findUserIdByEmail : <==      Total: 1
2025-06-15 23:57:52.324 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51bb6eb0]
2025-06-15 23:57:52.346 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.service.impl.UserServiceImpl : 根据ID查找用户信息包含密码信息: 8
2025-06-15 23:57:52.394 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.service.impl.UserServiceImpl : 更新用户密码, userId: 8
2025-06-15 23:57:52.472 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:57:52.472 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c903331]
2025-06-15 23:57:52.473 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:57:52.473 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, updated_time = NOW() WHERE id = ?
2025-06-15 23:57:52.475 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : ==> Parameters: hao111(String), $2a$10$YEYgMYeWpKqfUuqzT1BF5ugvFujKP6mkeoEQPpm950ecy94WpaWrm(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-06-15 19:41:11.0(Timestamp), eyJhbGciOiJIUzI1NiJ9.*******************************************************************.OKiCIYtcr1cSE4HM8hLQaDW9R-bc47wYsv1VjH6-Hr4(String), 8(Long)
2025-06-15 23:57:52.497 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-06-15 23:57:52.497 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c903331]
2025-06-15 23:57:52.497 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.service.impl.UserServiceImpl : 用户密码更新成功, userId: 8
2025-06-15 23:57:52.497 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c903331]
2025-06-15 23:57:52.497 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c903331]
2025-06-15 23:57:52.499 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c903331]
2025-06-15 23:57:52.539 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:57:52.539 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34a336aa]
2025-06-15 23:57:52.539 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2144347607 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:57:52.539 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-06-15 23:57:52.541 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 8(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户hao111执行了更新用户密码(String), 2025-06-15T23:57:52.528999100(LocalDateTime)
2025-06-15 23:57:52.564 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-06-15 23:57:52.564 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34a336aa]
2025-06-15 23:57:52.565 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=8, summary=用户hao111执行了更新用户密码
2025-06-15 23:57:52.565 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34a336aa]
2025-06-15 23:57:52.565 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34a336aa]
2025-06-15 23:57:52.565 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34a336aa]
2025-06-15 23:57:52.591 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 23:57:52.591 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=密码更新成功, success=true, data=null, time=2025-06-15T15:57:52.525997500Z)]
2025-06-15 23:57:52.592 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-15 23:57:52.592 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 23:59:23.627 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 23:59:23.627 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 23:59:23.627 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 23:59:23.667 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 23:59:23.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:59:23.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30e23738]
2025-06-15 23:59:23.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1619883660 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:59:23.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 23:59:23.673 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 23:59:23.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 23:59:23.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30e23738]
2025-06-15 23:59:23.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 23:59:23.686 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30e23738]
2025-06-15 23:59:23.687 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30e23738]
2025-06-15 23:59:23.687 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30e23738]
2025-06-15 23:59:23.720 [39mDEBUG[0;39m [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 23:59:23.720 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 23:59:23.720 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 23:59:23.721 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 23:59:23.721 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 23:59:23.722 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Ah123456, email=null, deviceId=null)]
2025-06-15 23:59:23.731 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 23:59:23.731 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 23:59:23.786 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 23:59:23.790 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:59:23.790 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bbfb935]
2025-06-15 23:59:23.790 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1511177517 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:59:23.790 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 23:59:23.790 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 23:59:23.802 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 23:59:23.802 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bbfb935]
2025-06-15 23:59:23.802 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 23:59:23.802 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bbfb935]
2025-06-15 23:59:23.802 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bbfb935]
2025-06-15 23:59:23.802 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bbfb935]
2025-06-15 23:59:23.908 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.a.d.DaoAuthenticationProvider : Failed to authenticate since password does not match stored value
2025-06-15 23:59:23.915 [31mWARN [0;39m [http-nio-8080-exec-10] c.e.p.service.impl.AuthServiceImpl : 用户登录失败 - 凭证错误: hao111
2025-06-15 23:59:23.918 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-15 23:59:23.918 [31mWARN [0;39m [http-nio-8080-exec-10] c.e.p.e.GlobalExceptionHandler : 业务异常: 用户名或密码错误
2025-06-15 23:59:23.919 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 23:59:23.919 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=用户名或密码错误, success=false, data=null, time=2025-06-15T15:59:23.918605Z)]
2025-06-15 23:59:23.920 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 用户名或密码错误]
2025-06-15 23:59:23.921 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-15 23:59:23.921 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-15 23:59:50.143 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-06-15 23:59:50.143 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-15 23:59:50.144 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 23:59:50.185 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 23:59:50.189 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:59:50.189 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e385941]
2025-06-15 23:59:50.189 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@168120807 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:59:50.189 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 23:59:50.190 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 23:59:50.202 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 23:59:50.202 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e385941]
2025-06-15 23:59:50.203 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 23:59:50.203 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e385941]
2025-06-15 23:59:50.203 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e385941]
2025-06-15 23:59:50.203 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e385941]
2025-06-15 23:59:50.237 [39mDEBUG[0;39m [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'hao111' 认证成功
2025-06-15 23:59:50.237 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-06-15 23:59:50.237 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-06-15 23:59:50.238 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-06-15 23:59:50.238 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-06-15 23:59:50.239 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=hao111, password=Ah123456, email=null, deviceId=null)]
2025-06-15 23:59:50.244 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.pure.controller.AuthController : 用户登录: hao111
2025-06-15 23:59:50.245 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.service.impl.AuthServiceImpl : 处理用户登录请求: hao111
2025-06-15 23:59:50.279 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: hao111
2025-06-15 23:59:50.283 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-15 23:59:50.283 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c41ce2]
2025-06-15 23:59:50.283 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2105146484 wrapping com.mysql.cj.jdbc.ConnectionImpl@1964e32c] will be managed by Spring
2025-06-15 23:59:50.284 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-06-15 23:59:50.284 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 8(Long)
2025-06-15 23:59:50.297 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-06-15 23:59:50.297 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c41ce2]
2025-06-15 23:59:50.297 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 hao111 的角色信息已加载, 角色数量: 1
2025-06-15 23:59:50.297 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c41ce2]
2025-06-15 23:59:50.297 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c41ce2]
2025-06-15 23:59:50.297 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57c41ce2]
2025-06-15 23:59:50.403 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.a.d.DaoAuthenticationProvider : Failed to authenticate since password does not match stored value
2025-06-15 23:59:50.403 [31mWARN [0;39m [http-nio-8080-exec-3] c.e.p.service.impl.AuthServiceImpl : 用户登录失败 - 凭证错误: hao111
2025-06-15 23:59:50.404 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-15 23:59:50.404 [31mWARN [0;39m [http-nio-8080-exec-3] c.e.p.e.GlobalExceptionHandler : 业务异常: 用户名或密码错误
2025-06-15 23:59:50.404 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-15 23:59:50.404 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=用户名或密码错误, success=false, data=null, time=2025-06-15T15:59:50.404901500Z)]
2025-06-15 23:59:50.405 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 用户名或密码错误]
2025-06-15 23:59:50.405 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-15 23:59:50.405 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
