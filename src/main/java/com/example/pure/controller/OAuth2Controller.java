package com.example.pure.controller;

import com.example.pure.common.Result;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.service.OAuth2Service;
import com.example.pure.util.CookieUtil;
import com.xkcoding.http.config.HttpConfig;
import lombok.extern.slf4j.Slf4j;

import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.request.AuthGiteeRequest;
import me.zhyd.oauth.request.AuthGithubRequest;
import me.zhyd.oauth.request.AuthGoogleRequest;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/oauth")
@Slf4j
public class OAuth2Controller {

    private final OAuth2Service oAuth2Service;
    private final WebClient webClient;
    private final CookieUtil cookieUtil;

    @Autowired
    public OAuth2Controller(OAuth2Service oAuth2Service, WebClient webClient, CookieUtil cookieUtil) {
        this.oAuth2Service = oAuth2Service;
        this.webClient = webClient;
        this.cookieUtil = cookieUtil;
    }

    /**
     * 获取配置的HTTP代理
     */
    private HttpConfig getHttpConfig() {
        return HttpConfig.builder()
                .timeout(15000)  // 15秒超时
                .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 7897)))
                .build();
    }

    /**
     * 获取授权请求对象，并配置参数
     */
    private AuthRequest getAuthRequest(String source) {
        AuthRequest authRequest;

        if (source.equals("google")) {
            authRequest = new AuthGoogleRequest(AuthConfig.builder()
                    .clientId("你的Google Client ID")
                    .clientSecret("你的Google Client Secret")
                    .redirectUri("http://localhost:8080/oauth/callback/google")
                    .httpConfig(getHttpConfig())  // Google需要配置代理，开发者使用的，仅在开发环境使用
                    .build());

        } else if (source.equals("github")) {
            authRequest = new AuthGithubRequest(AuthConfig.builder()
                    .clientId(SecurityConstants.GITHUB_CLIENTID)
                    .clientSecret(SecurityConstants.GITHUB_CLIENTSECRET)
                    //重定向到你在github设置的重定向地址
                    .redirectUri("http://localhost:8080/oauth/callback/github")
                    .httpConfig(getHttpConfig())  // Github可能也需要代理
                    .build());

        } else if (source.equals("gitee")) {
            authRequest = new AuthGiteeRequest(AuthConfig.builder()
                    .clientId(SecurityConstants.GITEE_CLENTID)
                    .clientSecret(SecurityConstants.GITEE_CLIENTSECRET)
                    .redirectUri("http://localhost:8080/oauth/callback/gitee")
                    .build());

        } else {
            throw new RuntimeException("未支持的第三方登录类型");
        }

        return authRequest;
    }

    /**
     * 生成授权URL，等于用户在前端点击使用第三方登录按钮，后端就从这个控制器收到这个请求，并生成授权URL
     *
     * @param source 第三方平台标识（google, github, gitee）
     */
    @RequestMapping("/render/{source}")
    public void renderAuth(@PathVariable("source") String source, HttpServletResponse response) throws IOException {
        try {
            AuthRequest authRequest = getAuthRequest(source);
            //生成授权Code和State（防止跨站请求伪造攻击）给URL,生成授权链接
            String authorizeUrl = authRequest.authorize(AuthStateUtils.createState());
            log.info("Generated authorize url: {}", authorizeUrl);
            //重定向前端的当前的页面到第三方平台的授权页面，让用户在第三方平台的授权页面决定授权
            response.sendRedirect(authorizeUrl);
        } catch (Exception e) {
            log.error("授权URL生成失败", e);
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"error\":\"" + e.getMessage() + "\"}");
        }
    }

    /**
     * 用户在第三方授权页面点击同意授权后，第三方平台会重定向到你的回调地址接口
     * 处理回调，登录
     *
     * @param source   第三方平台标识
     * @param callback 回调参数
     */
    @RequestMapping("/callback/{source}")
    public void login(@PathVariable("source") String source, AuthCallback callback, HttpServletResponse response) throws IOException {
        log.info("收到{}的回调: {}", source, callback);
        try {
            AuthRequest authRequest = getAuthRequest(source);

            //因为重定向到回调地址，所以callback对象里包含了Code和State
            // 使用Code向第三方平台换取用户的第三方平台的访问令牌和刷新令牌还有用户信息，让前端和后端获取第三方平台用户的信息类的对象（包括用户的访问Token和刷新Token）
            //AuthResponse<AuthUser>等于AuthResponse<T>把泛型函数里的T data类型改成AuthUser
            AuthResponse<AuthUser> authResponse = authRequest.login(callback);

            if (authResponse.ok()) {
                AuthUser authUser = authResponse.getData();
                log.info("用户信息: {}", authUser);

                // 第二组：我们系统处理后的结果
                Map<String, Object> processedResult = oAuth2Service.handleOAuth2User(authUser);

                // 3. 使用CookieUtil添加安全的HttpOnly Cookie来存储access_token和refresh_token
                String accessToken = processedResult.get("access_token").toString();
                String refreshToken = processedResult.get("refresh_token").toString();
                cookieUtil.addTokenCookies(response, accessToken, refreshToken);

                // 4. 执行重定向，将用户带到你的前端重定向页面在重定向页面实现使用刷新token获取访问token和获取用户信息逻辑
                // 注意：这里的URL应该是你前端应用的地址，而不是后端API地址
                String frontendUrl = "http://localhost:8848/#/result/oauth/callback";
                response.sendRedirect(frontendUrl);

            } else {
                // 5. 如果第三方认证失败，也重定向到前端的错误页面
                log.warn("第三方认证失败: {}", authResponse.getMsg());
                String errorUrl = "http://localhost:8848/#/login?error=" + authResponse.getMsg();
                response.sendRedirect(errorUrl);
            }
        } catch (Exception e) {
            log.error("登录意外失败", e);
            // 6. 如果系统内部发生异常，也重定向到前端的错误页面
            String errorUrl = "http://localhost:8848/login?error=InternalServerError";
            response.sendRedirect(errorUrl);
        }
    }

    /**
     * 刷新第三方平台的访问令牌
     * 当访问令牌过期时，使用刷新令牌获取新的访问令牌
     *
     * @param source 第三方平台标识（google, github, gitee）
     * @param refreshToken 用于刷新访问令牌的刷新令牌
     * @return 返回刷新结果，包含：
     *         - 成功：新的访问令牌信息（access_token, refresh_token, expires_in等）
     *         - 失败：错误信息和状态码
     *
     * 处理流程：
     * 1. 对Gitee平台进行特殊处理：
     *    - 构造特定的刷新token URL
     *    - 发送POST请求到Gitee的token刷新接口
     *    - 解析响应并格式化token信息
     *
     * 2. 其他平台（如Github、Google）：
     *    - 使用JustAuth库的通用刷新方法
     *    - 构建刷新token请求
     *    - 处理响应并格式化结果
     *
     * @throws Exception 当刷新过程发生异常时抛出
     */
    @RequestMapping("/refresh/{source}")
    public Result<Map<String, Object>> refreshAuth(
            @PathVariable("source") String source,
            @RequestParam("refreshToken") String refreshToken) {
        log.info("正在刷新{}的令牌，使用刷新令牌: {}", source, refreshToken);
        try {
            if ("gitee".equals(source)) {
                // Gitee刷新token的特殊处理
                String url = "https://gitee.com/oauth/token?" +
                        "grant_type=refresh_token" +
                        "&refresh_token=" + refreshToken +
                        "&client_id=" + SecurityConstants.GITEE_CLENTID +
                        "&client_secret=" + SecurityConstants.GITEE_CLIENTSECRET;

                // 使用WebClient发送请求
                Map body = webClient.post()
                        .uri(url)
                        .retrieve()
                        .bodyToMono(Map.class)// 把Http请求返回的响应体转Mono参数里的对象例如这里是Map的对象，Mono表示一个将来会产生一个结果或不产生任何结果的异步操作
                        .block();

                if (body != null) {
                    Map<String, Object> result = new HashMap<>();
                    Map<String, Object> tokenInfo = new HashMap<>();

                    tokenInfo.put("accessToken", body.get("access_token"));
                    tokenInfo.put("refreshToken", body.get("refresh_token"));
                    tokenInfo.put("expireIn", body.get("expires_in"));
                    tokenInfo.put("scope", body.get("scope"));
                    tokenInfo.put("tokenType", body.get("token_type"));

                    result.put("token_info", tokenInfo);
                    return Result.success("令牌刷新成功", result);
                }

                Map<String, Object> errorData = new HashMap<>();
                errorData.put("error", "刷新令牌失败");
                return Result.error(500, "刷新令牌失败", errorData);
            }

            // 其他平台的处理保持不变
            AuthRequest authRequest = getAuthRequest(source);
            //添加参数到对象里使用刷新令牌的方法刷新令牌
            AuthResponse<AuthToken> response = authRequest.refresh(AuthToken.builder()
                    .refreshToken(refreshToken)
                    .build());

            if (response.ok()) {
                Map<String, Object> result = new HashMap<>();
                AuthToken newToken = response.getData();
                // 构建新的token信息
                Map<String, Object> tokenInfo = new HashMap<>();
                tokenInfo.put("accessToken", newToken.getAccessToken());
                tokenInfo.put("refreshToken", newToken.getRefreshToken());
                tokenInfo.put("expireIn", newToken.getExpireIn());
                tokenInfo.put("scope", newToken.getScope());
                tokenInfo.put("tokenType", newToken.getTokenType());
                result.put("token_info", tokenInfo);

                return Result.success("令牌刷新成功", result);
            }

            Map<String, Object> errorData = new HashMap<>();
            errorData.put("code", response.getCode());
            errorData.put("msg", response.getMsg());
            return Result.error(response.getCode(), response.getMsg(), errorData);
        } catch (Exception e) {
            log.error("刷新{}的令牌失败", source, e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            return Result.error(500, "刷新令牌失败", errorData);
        }
    }

    /**
     * 撤销第三方平台的授权
     * 用于用户取消对应用的授权，撤销后应用将无法访问用户在第三方平台的数据
     *
     * @param source 第三方平台标识（google, github, gitee）
     * @param accessToken 需要撤销的访问令牌
     * @return 返回撤销结果：
     *         - 成功：撤销成功的确认信息
     *         - 失败：错误信息和状态码
     *
     * 处理流程：
     * 1. Gitee平台特殊处理：
     *    - 构造撤销授权的URL
     *    - 发送POST请求到Gitee的撤销接口
     *    - 处理响应状态（成功：200或204）
     *
     * 2. 其他平台：
     *    - 使用JustAuth库的通用撤销方法
     *    - 构建撤销请求
     *    - 处理响应结果
     *
     * 安全考虑：
     * - 记录撤销操作的日志
     * - 验证请求来源
     * - 确保accessToken的合法性
     *
     * @throws Exception 当撤销过程发生异常时抛出
     */
    @RequestMapping("/revoke/{source}")
    public Result<Map<String, Object>> revokeAuth(
            @PathVariable("source") String source,
            @RequestParam("accessToken") String accessToken,
            HttpServletResponse response) {
        log.info("正在撤销{}的授权", source);
        try {
            if ("gitee".equals(source)) {
                // Gitee撤销授权的特殊处理
                String url = "https://gitee.com/oauth/revoke?" +
                        "access_token=" + accessToken;

                // 使用WebClient发送请求
                Mono<Map> responseMono = webClient.post()
                        .uri(url)
                        .retrieve()
                        .onStatus(status -> status.equals(HttpStatus.OK) || status.equals(HttpStatus.NO_CONTENT),
                                clientResponse -> Mono.empty())
                        .bodyToMono(Map.class)
                        .onErrorResume(e -> Mono.just(new HashMap<>()));

                Map responseBody = responseMono.block();

                // 如果响应为空或已成功处理，则视为成功
                // 清除Cookie
                cookieUtil.clearTokenCookies(response);

                Map<String, Object> result = new HashMap<>();
                result.put("status", "success");
                result.put("message", "授权撤销成功");
                return Result.success("授权撤销成功", result);
            }

            // 其他平台的处理保持不变
            AuthRequest authRequest = getAuthRequest(source);
            AuthResponse<?> authResponse = authRequest.revoke(AuthToken.builder()
                    .accessToken(accessToken)
                    .build());

            if (authResponse.ok()) {
                // 清除Cookie
                cookieUtil.clearTokenCookies(response);

                Map<String, Object> result = new HashMap<>();
                result.put("status", "success");
                return Result.success("授权撤销成功", result);
            }

            Map<String, Object> errorData = new HashMap<>();
            errorData.put("code", authResponse.getCode());
            errorData.put("msg", authResponse.getMsg());
            return Result.error(authResponse.getCode(), authResponse.getMsg(), errorData);
        } catch (Exception e) {
            log.error("撤销{}授权失败", source, e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            return Result.error(500, "撤销授权失败", errorData);
        }
    }

    /**
     * 用户登出
     * 清除Cookie中的令牌
     *
     * @param response HTTP响应对象
     * @return 登出结果
     */
    @RequestMapping("/logout")
    public Result<Void> logout(HttpServletResponse response) {
        try {
            // 清除Cookie
            cookieUtil.clearTokenCookies(response);
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("登出失败", e);
            return Result.error(500, "登出失败: " + e.getMessage());
        }
    }
}

