package com.example.pure.controller;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.FileMetadata;
import com.example.pure.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 文件管理控制器
 * <p>
 * 提供文件上传、列表查询和管理等功能
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "文件上传和管理相关接口")
public class FileManagerController {

    private final FileService fileService;

    @Value("${file.storage.location.upload}")
    private String fileStorageLocation;

    @Value("${file.storage.max-size}") // 默认1GB (1024 * 1024 * 1024)
    private long maxFileSize;



    /**
     * 上传文件（同步实现）
     *
     * @param currentFile 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @return 上传结果
     * @deprecated 推荐使用 {@link #uploadFileAsync(MultipartFile, String)} 代替
     */
    @PostMapping("/upload-general")
    @Operation(summary = "上传文件（传统实现）", description = "同步方式上传文件到服务器")
    public Result<Map<String, Object>> uploadFile(
            //查询请求体的key为file的值反序列化到MultipartFile的实例currentFile里
            @RequestParam("file") MultipartFile currentFile,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        // 文件为空检查
        if (currentFile.isEmpty()) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "上传失败，文件为空");
        }

        // 文件大小检查
        if (currentFile.getSize() > maxFileSize) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER,
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / (1024 * 1024)));
        }

        // 获取当前用户名
        String username = getCurrentUsername();

        // MIME类型检查
        String contentType = currentFile.getContentType();
        if (contentType == null || !SecurityConstants.ALLOWED_MIME_TYPES.contains(contentType.toLowerCase())) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "不支持的文件类型: " + contentType);
        }

        // 获取原始文件名并安全检查
        String originalFilename = currentFile.getOriginalFilename();
        if (originalFilename == null || !fileService.isValidFileName(originalFilename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }

        // 获取文件类型
        String fileExtension = getFileExtension(originalFilename);
        // 设置新的文件名称然后合并字符串
        String safeFileName = UUID.randomUUID().toString() + (fileExtension.isEmpty() ? "" : "." + fileExtension);

        InputStream inputStream = null;
        try {
            // 构建存储路径(包含用户名)，工厂方法创建一个Path对象，根据设置的操作系统创建绝对路径/username/type存放
            Path userRootPath = Paths.get(fileStorageLocation, username, type).toAbsolutePath().normalize();
            // 创建文件夹,如果存在就忽略
            Files.createDirectories(userRootPath);

            // 创建目标文件路径，合并刚刚的存储路径和刚刚新设置的文件名
            Path targetPath = userRootPath.resolve(safeFileName);

            // 保存文件
            inputStream = currentFile.getInputStream();

            // 从输入流的二进制字节数组写入到targetPath目标文件完整路径，StandardCopyOption.REPLACE_EXISTING:如果存在则覆盖文件
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);

            // 构建响应信息
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("originalFilename", originalFilename);
            fileInfo.put("storedFilename", safeFileName);
            fileInfo.put("size", currentFile.getSize());
            fileInfo.put("type", type);
            fileInfo.put("contentType", currentFile.getContentType());
            fileInfo.put("path", targetPath.toString());
            fileInfo.put("uploadedBy", username);

            return Result.success("文件上传成功", fileInfo);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "文件上传失败: " + e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }

    /**
     * 传输文件和元数据: multipart/form-data 的核心优势在于它可以在同一个 HTTP 请求中同时传输二进制文件数据和其他文本形
     * 式的参数（如你的 type 参数，或者文件描述、用户 ID 等）。每一部分（文件或文本字段）都有自己的Content-Disposition头来标识名称和（如果是文件）原始文件名。
     *
     * 异步上传文件
     * <p>
     * 使用异步方式处理文件上传，提高服务器性能
     * </p>
     *
     * @param file 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @return 上传结果，包含文件上传进度和状态
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件（异步）", description = "异步方式上传文件到服务器，提高性能")
    public Result<Map<String, Object>> uploadFileAsync(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        // 文件为空检查
        if (file.isEmpty()) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "上传失败，文件为空");
        }

        // 文件大小检查
        if (file.getSize() > maxFileSize) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER,
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / (1024 * 1024)));
        }

        // 获取当前用户名
        String username = getCurrentUsername();

        // MIME类型检查
        String contentType = file.getContentType();
        if (contentType == null || !SecurityConstants.ALLOWED_MIME_TYPES.contains(contentType.toLowerCase())) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "不支持的文件类型: " + contentType);
        }

        // 获取原始文件名并安全检查
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !fileService.isValidFileName(originalFilename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }

        // 获取文件类型
        String fileExtension = getFileExtension(originalFilename);
        // 设置文件唯一名称，然后拼接文件类型
        String safeFileName = UUID.randomUUID().toString() + (fileExtension.isEmpty() ? "" : "." + fileExtension);

        try {
            // 创建异步任务处理文件上传,子线程运行下面的代码
            CompletableFuture.runAsync(() -> {
                InputStream inputStream = null;
                try {
                    // 构建存储路径 (包含用户名)
                    Path userRootPath = Paths.get(fileStorageLocation, username, type).toAbsolutePath().normalize();
                    Files.createDirectories(userRootPath);

                    // 目标文件路径
                    Path targetPath = userRootPath.resolve(safeFileName);

                    // 保存文件
                    inputStream = file.getInputStream();
                    Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);

                    log.info("文件异步上传完成: {}, 存储为: {}", originalFilename, safeFileName);
                } catch (IOException e) {
                    log.error("异步文件上传处理失败: {}", originalFilename, e);
                } finally {
                    if (inputStream != null) {
                        try {
                            inputStream.close();
                        } catch (IOException e) {
                            log.error("关闭输入流失败", e);
                        }
                    }
                }
            });

            // 立即返回上传已接收的响应,主线程继续运行这的代码
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("originalFilename", originalFilename);
            fileInfo.put("storedFilename", safeFileName);
            fileInfo.put("size", file.getSize());
            fileInfo.put("type", type);
            fileInfo.put("contentType", file.getContentType());
            fileInfo.put("uploadedBy", username);
            fileInfo.put("status", "PROCESSING"); // 标记为处理中
            fileInfo.put("message", "文件上传请求已接收，正在后台处理");

            return Result.success("文件上传请求已接收", fileInfo);
        } catch (Exception e) {
            log.error("处理文件上传请求失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理文件上传请求失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前认证用户的用户名，从认证过的用户上下文获取用户名
     *
     * @return 当前用户名，如未认证则返回"anonymous"
     */
    private String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !"anonymousUser".equals(authentication.getPrincipal())) {
            return authentication.getName();
        }
        return "anonymous";
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名（不包含点号）
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 获取文件列表
     *
     * @param type 文件类型（可选，用于过滤）
     * @return 文件列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "获取指定类型的文件列表")
    public Result<List<Map<String, Object>>> getFileList(
            @RequestParam(value = "type", required = false) String type) {

        try {
            // 获取当前用户名
            String username = getCurrentUsername();

            // 构建文件存储路径
            Path rootPath;
            if (type != null && !type.isEmpty()) {
                rootPath = Paths.get(fileStorageLocation, username, type).toAbsolutePath().normalize();
            } else {
                rootPath = Paths.get(fileStorageLocation, username).toAbsolutePath().normalize();
            }

            // 确保目录存在
            if (!Files.exists(rootPath)) {
                Files.createDirectories(rootPath);
            }

            // 获取文件列表
            List<Map<String, Object>> fileList = new ArrayList<>();

            Files.list(rootPath).forEach(path -> {
                if (Files.isRegularFile(path)) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    String fileName = path.getFileName().toString();
                    try {
                        fileInfo.put("name", fileName);
                        fileInfo.put("size", Files.size(path));
                        fileInfo.put("lastModified", Files.getLastModifiedTime(path).toMillis());
                        fileInfo.put("contentType", fileService.determineContentType(path));
                        fileInfo.put("path", path.toString());
                        fileInfo.put("downloadUrl", "/download/" + fileName);
                        fileList.add(fileInfo);
                    } catch (IOException e) {
                        log.error("读取文件信息失败: {}", fileName, e);
                    }
                }
            });

            return Result.success("获取文件列表成功", fileList);
        } catch (IOException e) {
            log.error("获取文件列表失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 异步获取文件详细信息
     *
     * @param filename 文件名
     * @param type 文件类型（用于定位文件位置）
     * @return 文件详细信息
     */
    @GetMapping("/info/{filename:.+}")
    @Operation(summary = "获取文件详细信息", description = "获取指定文件的详细元数据信息")
    public Result<Map<String, Object>> getFileInfo(
            @PathVariable String filename,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        try {
            // 安全检查
            if (!fileService.isValidFileName(filename)) {
                throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
            }

            // 获取当前用户名
            String username = getCurrentUsername();

            // 先检查文件是否存在
            Path filePath = Paths.get(fileStorageLocation, username, type, filename).toAbsolutePath().normalize();
            if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
                throw BusinessException.of(ResponseCode.NOT_FOUND, "文件不存在");
            }

            // 异步获取文件元数据
            CompletableFuture<FileMetadata> metadataFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return fileService.getFileMetadata(filename);
                } catch (IOException e) {
                    throw new RuntimeException("获取文件元数据失败", e);
                }
            });

            // 处理异步结果
            FileMetadata metadata = metadataFuture.join();

            // 构建响应数据
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("name", metadata.getFileName());
            fileInfo.put("size", metadata.getFileSize());
            fileInfo.put("contentType", metadata.getContentType());
            fileInfo.put("exists", metadata.isExists());
            fileInfo.put("readable", metadata.isReadable());
            fileInfo.put("downloadUrl", "/download/" + filename);
            fileInfo.put("type", type);

            return Result.success("获取文件信息成功", fileInfo);

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", filename, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param filename 文件名
     * @param type 文件类型（用于定位文件位置）
     * @return 删除结果
     */
    @DeleteMapping("/{filename:.+}")
    @Operation(summary = "删除文件", description = "删除指定的文件")
    public Result<Void> deleteFile(
            @PathVariable String filename,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        // 安全检查
        if (!fileService.isValidFileName(filename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }

        try {
            // 获取当前用户名
            String username = getCurrentUsername();

            // 构建文件路径
            Path filePath = Paths.get(fileStorageLocation, username, type, filename).toAbsolutePath().normalize();

            // 验证文件是否存在
            if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
                throw BusinessException.of(ResponseCode.NOT_FOUND, "文件不存在");
            }

            // 异步删除文件
            CompletableFuture.runAsync(() -> {
                try {
                    Files.delete(filePath);
                    log.info("文件删除成功: {}", filename);
                } catch (IOException e) {
                    log.error("异步文件删除失败: {}", filename, e);
                }
            });

            return Result.success("文件删除请求已接收");
        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理文件删除请求失败: {}", filename, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理文件删除请求失败: " + e.getMessage());
        }
    }

    /**
     * 异步批量删除文件
     *
     * @param filenames 文件名列表
     * @param type 文件类型（用于定位文件位置）
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除文件", description = "异步批量删除多个文件")
    public Result<Map<String, Object>> batchDeleteFiles(
            @RequestParam("filenames") List<String> filenames,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        if (filenames == null || filenames.isEmpty()) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名列表为空");
        }

        // 验证所有文件名
        for (String filename : filenames) {
            if (!fileService.isValidFileName(filename)) {
                throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法: " + filename);
            }
        }

        // 获取当前用户名
        String username = getCurrentUsername();

        // 异步批量删除文件
        CompletableFuture.runAsync(() -> {
            List<String> deletedFiles = new ArrayList<>();
            List<String> failedFiles = new ArrayList<>();

            for (String filename : filenames) {
                try {
                    Path filePath = Paths.get(fileStorageLocation, username, type, filename).toAbsolutePath().normalize();
                    if (Files.exists(filePath) && Files.isRegularFile(filePath)) {
                        Files.delete(filePath);
                        deletedFiles.add(filename);
                    } else {
                        failedFiles.add(filename + " (不存在)");
                    }
                } catch (IOException e) {
                    log.error("删除文件失败: {}", filename, e);
                    failedFiles.add(filename + " (" + e.getMessage() + ")");
                }
            }

            log.info("批量删除文件完成, 成功: {}, 失败: {}", deletedFiles.size(), failedFiles.size());
        });

        // 立即返回响应
        Map<String, Object> result = new HashMap<>();
        result.put("status", "PROCESSING");
        result.put("totalFiles", filenames.size());
        result.put("message", "批量删除文件请求已接收，正在后台处理");

        return Result.success("批量删除文件请求已接收", result);
    }
}
