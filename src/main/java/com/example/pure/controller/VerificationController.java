package com.example.pure.controller;


import com.example.pure.common.Result;
import com.example.pure.service.VerificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 验证码相关的控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/verification")
public class VerificationController {

    private final VerificationService verificationService;

    @Autowired
    public VerificationController(VerificationService verificationService) {
        this.verificationService = verificationService;
    }

    /**
     * 发送邮箱验证码
     * @param email 用户邮箱
     * @return 发送结果
     */
    @PostMapping("/send-email-code")
    public Result<Void> sendEmailVerificationCode(@RequestParam String email) {
        try {
            // 简单的邮箱格式验证
            if (email == null || email.trim().isEmpty() || !email.contains("@")) {
                return Result.error(HttpStatus.BAD_REQUEST.value(), "邮箱格式不正确");
            }

            log.info("发送验证码到邮箱: {}", email);
            verificationService.sendEmailVerificationCode(email);
            return Result.success("验证码已发送到您的邮箱");
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: {}", e.getMessage(), e);
            return Result.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "发送验证码失败: " + e.getMessage());
        }
    }
}
