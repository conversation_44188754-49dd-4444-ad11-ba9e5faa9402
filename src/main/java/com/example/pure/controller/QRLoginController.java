package com.example.pure.controller;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.QRLoginDTO.QRCodeResponse;
import com.example.pure.model.dto.QRLoginDTO.QRConfirmRequest;
import com.example.pure.model.dto.QRLoginDTO.QRScanRequest;
import com.example.pure.model.dto.QRLoginDTO.QRStatusResponse;
import com.example.pure.model.dto.TokenResponse;
import com.example.pure.model.dto.UserDTO;
import com.example.pure.service.AuthService;
import com.example.pure.service.UserService;
import com.example.pure.util.JwtUtil;
import com.example.pure.util.QRCodeUtil;
import com.example.pure.util.QRLoginRedisUtil;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;
import com.example.pure.util.QRLoginRedisUtil.QRCodeStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 二维码登录控制器
 * <p>
 * 提供二维码登录相关的API接口和WebSocket消息处理
 * </p>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/qrlogin")
@Tag(name = "二维码登录", description = "二维码登录相关接口")
public class QRLoginController {

    private final QRLoginRedisUtil qrLoginUtil;
    private final QRCodeUtil qrCodeUtil;
    private final UserService userService;
    private final AuthService authService;
    private final JwtUtil jwtUtil;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * 创建二维码登录会话
     *
     * @return 二维码信息，包含二维码ID、内容和过期时间
     */
    @GetMapping("/create")
    @Operation(summary = "创建二维码登录会话", description = "前端点击了二维码登录按钮，返回二维码ID、Base64编码的二维码图片和过期时间")
    public Result<QRCodeResponse> createQRCode() {
        try {
            // 生成二维码唯一标识
            String qrId = qrLoginUtil.generateQRCodeId();

            // 获取二维码信息
            QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);

            // 构建二维码内容，包含应用标识和二维码ID
            // 移动端扫描时需要根据此内容识别并处理
            String qrContent = "qrlogin://" + qrId;

            // 生成二维码图片并转为Base64
            String qrBase64 = qrCodeUtil.generateQRCodeAsDataUri(qrContent);

            // 构建响应
            QRCodeResponse response = QRCodeResponse.builder()
                    .qrId(qrId)
                    .qrContent(qrBase64)
                    .expireTime(qrInfo.getExpireTime())
                    .build();

            return Result.success("二维码创建成功", response);
        } catch (Exception e) {
            log.error("创建二维码失败", e);
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "创建二维码失败: " + e.getMessage());
        }
    }

    /**
     * 查询二维码状态
     * <p>
     * 用于Web端轮询二维码状态（作为WebSocket的后备方案）
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 二维码当前状态信息
     */
    @GetMapping("/status/{qrId}")
    @Operation(summary = "查询二维码状态", description = "用于Web端轮询二维码状态，作为WebSocket的后备方案")
    public Result<QRStatusResponse> getQRCodeStatus(@PathVariable String qrId) {
        // 获取二维码信息
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);

        // 如果二维码不存在，返回错误
        if (qrInfo == null) {
            throw  new BusinessException(ResponseCode.NOT_FOUND, "二维码不存在或已失效");
        }

        // 构建状态响应
        QRStatusResponse response = QRStatusResponse.builder()
                .status(qrInfo.getStatus())
                .expired(qrInfo.isExpired())
                .build();

        // 如果二维码状态为已确认，添加用户信息和令牌
        if (qrInfo.getStatus() == QRCodeStatus.CONFIRMED) {
            UserDTO userInfo = qrInfo.getUserInfo();
            response.setUserInfo(userInfo);

            // 生成JWT令牌
            TokenResponse tokenResponse = authService.createQRLoginTokens(userInfo.getUsername());
            response.setAccessToken(tokenResponse.getAccessToken());
            response.setRefreshToken(tokenResponse.getRefreshToken());

            // 登录成功后移除二维码信息
            qrLoginUtil.removeQRCode(qrId);
        }

        return Result.success("获取二维码状态成功", response);
    }

    /**
     * 扫描二维码
     * <p>
     * 移动端扫描二维码后调用该接口，更新二维码状态为已扫描
     * </p>
     * 移动端扫描网页端SQ码，后端服务器通过WebSocket通知消息发送给网页端SQL已扫描，响应移动端，移动端确认登陆后，通知
     * web网页端消息，如果是确认的话就发送数据（包含登陆信息和Token)让网页端登录账号并更新页面
     *
     * @param request 扫描请求参数，包含二维码ID和用户令牌
     * @return 处理结果
     */
    @PostMapping("/scan")
    @Operation(summary = "扫描二维码", description = "移动端扫描二维码后调用该接口，更新二维码状态为已扫描")
    public Result<Map<String, Object>> scanQRCode(@RequestBody QRScanRequest request) {
        // 验证请求参数
        if (request.getQrId() == null || request.getToken() == null) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "缺少必要参数");
        }


        //验证用户是否已经扫描
        if(qrLoginUtil.checkDuplicateVerificationRequest(request.getQrId(), QRCodeStatus.SCANNED)) {
            throw new BusinessException((ResponseCode.BAD_REQUEST),"二维码已经扫描");
        }

        // 验证用户令牌
        if (!userService.validateToken(request.getToken())) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED, "无效的用户令牌");
        }

        // 从令牌中获取用户信息
        String username = jwtUtil.getUsernameFromToken(request.getToken());
        UserDTO userInfo = userService.findUserByUsername(username);

        if (userInfo == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND, "用户不存在");
        }

        // 更新二维码状态为已扫描
        boolean success = qrLoginUtil.scanQRCode(request.getQrId(), userInfo);

        if (!success) {
            throw new BusinessException(ResponseCode.STATUS_ERROR, "无效或已过期的二维码");
        }

        // 通过WebSocket通知Web端二维码已被扫描
        notifyQRCodeStatusChanged(request.getQrId());

        // 返回成功响应
        Map<String, Object> data = new HashMap<>();
        data.put("qrId", request.getQrId());
        data.put("message", "二维码已扫描，等待确认");

        return Result.success("扫描成功", data);
    }

    /**
     * 确认二维码登录
     * <p>
     * 移动端用户确认或拒绝登录后调用该接口
     * 如果确认登录，则通过WebSocket向Web客户端发送完整的登录凭证，客户端接着重定向页面
     * </p>
     *
     * @param request 确认请求参数，包含二维码ID和确认结果
     * @return 处理结果
     */
    @PostMapping("/confirm")
    @Operation(summary = "确认二维码登录", description = "移动端用户确认或拒绝登录后调用该接口，成功时通过WebSocket返回登录凭证")
    public Result<Map<String, Object>> confirmQRLogin(@RequestBody QRConfirmRequest request) {
        // 验证请求参数
        if (request.getQrId() == null) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "缺少必要参数");
        }

        // 获取二维码信息
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(request.getQrId());

        if (qrInfo == null || qrInfo.isExpired()) {
            throw BusinessException.of(ResponseCode.STATUS_ERROR, "无效或已过期的二维码");
        }

        if (qrInfo.getStatus() != QRCodeStatus.SCANNED) {
            throw BusinessException.of(ResponseCode.STATUS_ERROR, "二维码未被扫描，无法确认");
        }

        // 根据确认结果更新二维码状态
        boolean success;
        if (request.isConfirmed()) {
            // 确认登录
            success = qrLoginUtil.confirmQRCodeLogin(request.getQrId());

            if (success) {
                // 获取用户信息
                UserDTO userInfo = qrInfo.getUserInfo();

                // 生成JWT令牌
                TokenResponse tokenResponse = authService.createQRLoginTokens(userInfo.getUsername());

                // 构建完整的登录响应信息，包含令牌和用户信息
                Map<String, Object> loginData = new HashMap<>();
                loginData.put("status", QRCodeStatus.CONFIRMED);
                loginData.put("qrId", request.getQrId());
                loginData.put("userInfo", userInfo);
                loginData.put("accessToken", tokenResponse.getAccessToken());
                loginData.put("refreshToken", tokenResponse.getRefreshToken());
                loginData.put("tokenExpiration", tokenResponse.getAccessTokenExpiresIn());

                // 通过WebSocket直接发送登录凭证
                messagingTemplate.convertAndSend("/topic/qrlogin/" + request.getQrId(), loginData);

                // 登录成功后移除二维码信息
                qrLoginUtil.removeQRCode(request.getQrId());

                log.info("用户 {} 通过二维码扫描成功登录，QR ID: {}", userInfo.getUsername(), request.getQrId());
            }
        } else {
            // 取消登录
            success = qrLoginUtil.cancelQRCodeLogin(request.getQrId());

            // 通知Web端登录被取消
            if (success) {
                Map<String, Object> cancelData = new HashMap<>();
                cancelData.put("status", QRCodeStatus.CANCELED);
                cancelData.put("qrId", request.getQrId());
                cancelData.put("message", "用户取消了登录");

                // 通过WebSocket发送取消消息
                messagingTemplate.convertAndSend("/topic/qrlogin/" + request.getQrId(), cancelData);

                log.info("用户取消了二维码登录，QR ID: {}", request.getQrId());
            }
        }

        if (!success) {
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理确认请求失败");
        }

        // 返回成功响应
        Map<String, Object> data = new HashMap<>();
        data.put("qrId", request.getQrId());
        data.put("confirmed", request.isConfirmed());

        return Result.success(request.isConfirmed() ? "已确认登录" : "已取消登录", data);
    }

    /**
     * 通过WebSocket通知前端二维码状态变更
     * 该方法用于在二维码状态变化时通知Web客户端
     *
     * @param qrId 二维码ID
     */
    private void notifyQRCodeStatusChanged(String qrId) {
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (qrInfo == null) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER,"二维码无效,请重新获取");
        }

        // 构建状态变更消息
        Map<String, Object> statusMessage = new HashMap<>();
        statusMessage.put("qrId", qrId);
        statusMessage.put("status", qrInfo.getStatus());
        statusMessage.put("expired", qrInfo.isExpired());

        // 如果状态为已确认，添加用户信息
        if (qrInfo.getStatus() == QRCodeStatus.CONFIRMED) {
            statusMessage.put("userInfo", qrInfo.getUserInfo());
        }

        // 发送WebSocket消息到特定的二维码主题，消息发送目的地地址，发送的"对象"（可以是字符串、类、或者map)
        messagingTemplate.convertAndSend("/topic/qrlogin/" + qrId, statusMessage);
    }

    /**
     * WebSocket 消息处理方法 - 订阅二维码状态
     * 客户端可以通过WebSocket订阅此主题获取二维码状态更新
     *
     * @param qrId 二维码ID
     * @return 二维码当前状态
     */
    @MessageMapping("/qrlogin/subscribe")
    @SendTo("/topic/qrlogin/status")
    public Map<String, Object> subscribeQRStatus(String qrId) {
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);

        Map<String, Object> statusMessage = new HashMap<>();
        statusMessage.put("qrId", qrId);

        if (qrInfo == null) {
            statusMessage.put("status", "NOT_FOUND");
            statusMessage.put("expired", true);
        } else {
            statusMessage.put("status", qrInfo.getStatus());
            statusMessage.put("expired", qrInfo.isExpired());

            // 如果状态为已确认，添加用户信息
            if (qrInfo.getStatus() == QRCodeStatus.CONFIRMED) {
                UserDTO userInfo = qrInfo.getUserInfo();
                statusMessage.put("userInfo", userInfo);

                // 如果是确认状态，同时生成并添加令牌信息
                if (!qrInfo.isExpired()) {
                    TokenResponse tokenResponse = authService.createQRLoginTokens(userInfo.getUsername());
                    statusMessage.put("accessToken", tokenResponse.getAccessToken());
                    statusMessage.put("refreshToken", tokenResponse.getRefreshToken());
                    statusMessage.put("tokenExpiration", tokenResponse.getAccessTokenExpiresIn());
                }
            }
        }

        return statusMessage;
    }
}
