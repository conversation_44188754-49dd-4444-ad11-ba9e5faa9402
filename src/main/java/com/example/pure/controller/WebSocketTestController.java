package com.example.pure.controller;

import com.example.pure.exception.BusinessException;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.example.pure.model.dto.WebSocketMessage;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.security.Principal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket测试控制器
 * <p>
 * 提供STOMP消息处理和REST API测试端点
 * </p>
 * <p>
 * 支持三种消息模式：
 * 1. 广播消息/topic：发送给所有订阅特定主题的客户端
 * 2. 点对点消息/queue/user：发送给特定用户
 * 3. 用户消息：通过REST API发送给特定用户
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/ws-test")
@RequiredArgsConstructor
public class WebSocketTestController {

    private final SimpMessagingTemplate messagingTemplate;

    /**
     * 处理client发送消息到server，用"/app/message"
     * 并从server广播消息到client的订阅地址"/topic/messages"
     *
     * @param message 客户端发送的消息
     * @return 广播的消息
     */
    @MessageMapping("/message")
    @SendTo("/topic/messages")
    public Map<String, Object> broadcastMessage(@Payload Map<String, Object> message) {
        log.debug("接收到广播消息请求: {}", message);

        // 添加服务器时间戳
        message.put("timestamp", getCurrentTimestamp());
        message.put("messageType", "BROADCAST");

        return message;
    }

    /**
     * 处理client发送到server的"/app/private-message"的消息
     * 发送到特定用户的队列
     *
     * @param message 客户端发送的消息
     * @param headerAccessor 消息头访问器
     * @param principal 当前发送消息用户
     */
    @MessageMapping("/private-message")
    public void privateMessage(
            @Payload Map<String, Object> message,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {

        log.debug("接收到私信消息请求: {}", message);

        // 获取目标用户
        String recipient = (String) message.get("recipient");
        if (recipient == null || recipient.isEmpty()) {
            log.warn("私信消息没有指定接收人");
            throw new BusinessException("私信消息没有指定接收人");
        }

        // 处理 principal 为 null 的情况
        String senderName = "anonymous";
        if (principal != null) {
            senderName = principal.getName();
        } else {
            // 从消息中获取发送者信息，或使用会话ID作为标识
            if (message.containsKey("sender") && message.get("sender") != null) {
                senderName = message.get("sender").toString();
            } else if (headerAccessor != null && headerAccessor.getSessionId() != null) {
                senderName = "session-" + headerAccessor.getSessionId();
            }
            log.debug("私信消息没有Principal，使用备用发送者名称: {}", senderName);
        }

        // 添加发送者信息和时间戳
        message.put("sender", senderName);
        message.put("timestamp", getCurrentTimestamp());
        message.put("messageType", "PRIVATE");

        // 发送到特定用户的队列
        // 客户端需要订阅: /user/queue/private-messages
        messagingTemplate.convertAndSendToUser(
                recipient,
                "/queue/private-messages",
                message
        );

        // 给发送者一个确认消息
        Map<String, Object> confirmation = new HashMap<>();
        confirmation.put("messageType", "CONFIRMATION");
        confirmation.put("content", "消息已发送给 " + recipient);
        confirmation.put("timestamp", getCurrentTimestamp());

        // 发送确认消息到发送者
        // 如果 principal 为null，发送到一个广播主题
        if (principal != null) {
            messagingTemplate.convertAndSendToUser(
                    senderName,
                    "/queue/private-messages",
                    confirmation
            );
        } else {
            // 为匿名用户发送确认到公共主题
            confirmation.put("forSender", senderName);
            messagingTemplate.convertAndSend(
                    "/topic/private-response",
                    confirmation
            );
        }
    }

    /**
     * REST API端点，用于向特定用户发送WebSocket消息
     * 这个端点可以通过HTTP请求发送消息
     *
     * @param username 目标用户名
     * @param message 消息内容
     * @return 操作结果
     */
    @GetMapping("/api/ws/send/{username}")
    public Map<String, Object> sendMessageToUser(
            @PathVariable String username,
            @RequestParam String message) {

        log.debug("通过REST API发送消息给用户 {}: {}", username, message);

        Map<String, Object> payload = new HashMap<>();
        payload.put("content", message);
        payload.put("timestamp", getCurrentTimestamp());
        payload.put("sender", "Server");
        payload.put("messageType", "SERVER_PUSH");

        // 发送到特定用户的队列
        messagingTemplate.convertAndSendToUser(
                username,
                "/queue/notifications",
                payload
        );

        // 返回操作结果
        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        result.put("message", "消息已发送给用户: " + username);
        return result;
    }

    /**
     * 获取当前时间戳，格式化为字符串
     *
     * @return 格式化的时间戳
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        );
    }

    /**
     * WebSocket消息处理 - 接收客户端消息并回显
     * 客户端发送消息到 /app/echo，服务器回复到 /topic/echo
     *
     * @param message 客户端发送的消息
     * @return 回显的消息
     */
    @MessageMapping("/echo")
    @SendTo("/topic/echo")
    public String echo(@Payload String message) {
        log.info("收到echo消息: {}", message);
        return "Echo: " + message;
    }

    /**
     * WebSocket消息处理 - 使用消息对象
     * 客户端发送消息到 /app/chat，服务器广播到 /topic/chat
     *
     * @param message WebSocketMessage对象
     * @return 处理后的消息
     */
    @MessageMapping("/chat")
    @SendTo("/topic/chat")
    public WebSocketMessage handleChatMessage(@Payload WebSocketMessage message) {
        log.info("收到聊天消息: {}", message);

        // 设置服务器时间戳
        message.setTimestamp(System.currentTimeMillis());
        message.setProcessed(true);

        return message;
    }

    /**
     * WebSocket消息处理 - 发送到特定房间
     * 客户端发送消息到 /app/room/{roomId}，服务器广播到 /topic/room/{roomId}
     *
     * @param roomId 房间ID
     * @param message 消息内容
     * @return 处理后的消息
     */
    @MessageMapping("/room/{roomId}")
    @SendTo("/topic/room/{roomId}")
    public Map<String, Object> sendToRoom(@DestinationVariable String roomId,
                                        @Payload Map<String, Object> message) {
        log.info("收到发送到房间 {} 的消息: {}", roomId, message);

        message.put("roomId", roomId);
        message.put("timestamp", System.currentTimeMillis());

        return message;
    }

    /**
     * RESTful接口 - 发送通知消息
     * 用于测试服务器主动推送通知
     *
     * @return 操作结果
     */
    @GetMapping("/notify")
    public Map<String, Object> sendNotification() {
        log.info("发送通知消息");

        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "notification");
        notification.put("title", "系统通知");
        notification.put("content", "这是一条测试通知消息");
        notification.put("timestamp", System.currentTimeMillis());

        // 发送通知到WebSocket主题
        messagingTemplate.convertAndSend("/topic/notifications", notification);

        // 返回操作结果
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "通知已发送到WebSocket主题");
        return response;
    }
}
