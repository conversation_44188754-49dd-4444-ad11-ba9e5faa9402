package com.example.pure.controller;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.util.QRCodeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成控制器
 * <p>
 * 提供API接口用于生成二维码，支持多种格式：
 * - 图片形式（字节数组）
 * - Base64编码字符串
 *   前端提供内容模式
 *   后端逻辑生成内容模式
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/qrcode")
@RequiredArgsConstructor
public class QRCodeController {

    private final QRCodeUtil qrCodeUtil;

    /**
     * 生成二维码图片
     *
     * @param content 二维码内容
     * @return 二维码图片（PNG格式）
     * (value = "/generate")=("/generate"),因为()有多个参数所以需要value为完整写法
     * produces为声明告诉Spring MVC生成哪些MIME数据，并设置响应这个请求的响应体的Content-Type的MIME类型
     */
    @GetMapping(value = "/generate", produces = MediaType.IMAGE_PNG_VALUE)
    public Result<byte[]> generateQRCode(@RequestParam String content) {
        try {
            byte[] qrCodeImage = qrCodeUtil.generateQRCode(content);
            return Result.success("二维码生成成功", qrCodeImage);
        } catch (Exception e) {
            log.error("二维码生成失败", e);
            return Result.errorTyped(ResponseCode.INTERNAL_SERVER_ERROR, "生成二维码失败");
        }
    }

    /**
     * 生成Base64编码的二维码
     *
     * @param content 二维码内容
     * @return 包含Base64编码二维码的JSON对象
     */
    @GetMapping("/generate/base64")
    public Result<?> generateQRCodeBase64(@RequestParam String content) {
        try {
            String base64QR = qrCodeUtil.generateQRCodeAsBase64(content);
            Map<String, String> response = new HashMap<>();
            response.put("qrcode", base64QR);
            response.put("dataUri", "data:image/png;base64," + base64QR);//加"data:image/png;base64,"拼接字节数组让浏览器自动解码
            return Result.success("Base64二维码生成成功", response);
        } catch (Exception e) {
            log.error("Base64二维码生成失败", e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "二维码生成失败");
            return Result.errorTyped(ResponseCode.OPERATION_FAILED,"Base64二维码生成失败");

        }
    }

    /**
     * 为URL生成二维码
     *
     * @param url 需要生成二维码的URL
     * @return 二维码图片（PNG格式）
     * value = "/url"为（”/url“），因为有多个参数使完整写法
     */
    @GetMapping(value = "/url", produces = MediaType.IMAGE_PNG_VALUE)
    public  Result<byte[]> generateQRCodeForUrl(@RequestParam String url) {
        try {
            byte[] qrCodeImage = qrCodeUtil.generateQRCode(url);
            return  Result.success("URL二维码生成成功",qrCodeImage);
        } catch (Exception e) {
            log.error("URL二维码生成失败", e);
            return  Result.errorTyped(ResponseCode.OPERATION_FAILED,"URL二维码生成失败");
        }
    }
}
