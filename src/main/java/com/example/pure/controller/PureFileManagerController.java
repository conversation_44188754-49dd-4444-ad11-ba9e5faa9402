package com.example.pure.controller;


import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.FilePureService;
import com.example.pure.service.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;


@Slf4j
@RestController
@RequestMapping("/api/files/image")
public class PureFileManagerController {
    FilePureService filePureService;
    private final UserProfileService userProfileService;

    @Value("${file.storage.location.upload}")
    private String fileStorageLocation;

    @Value("${file.storage.max-size}") // 默认1GB (1024 * 1024 * 1024)
    private long maxFileSize;



    @Autowired
    public PureFileManagerController(
            FilePureService filePureService,
            UserProfileService userProfileService


    ) {
        this.filePureService = filePureService;
        this.userProfileService = userProfileService;

    }



    /**
     * 上传文件（同步实现）
     *
     * @param file 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @param authentication 当前认证信息
     * @return 上传结果
     * @deprecated 推荐使用 {@link #uploadFileAsync(MultipartFile, String, Authentication)} 代替
     */
    @PostMapping("/upload-general")
    public Result<Map<String, Object>> uploadFile(
            //查询请求体的key为file的值反序列化到MultipartFile的实例file里
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type,
            Authentication authentication) {

        // 文件为空检查
        if (file.isEmpty()) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "上传失败，文件为空");
        }

        // 文件大小检查
        if (file.getSize() > maxFileSize) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER,
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / (1024 * 1024)));
        }

        // 获取当前用户信息
        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        String username = customUserDetails.getUsername();

        // MIME类型检查
        String contentType = file.getContentType();
        if (contentType == null || !SecurityConstants.ALLOWED_MIME_TYPES.contains(contentType.toLowerCase())) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "不支持的文件类型: " + contentType);
        }

        // 获取原始文件名并安全检查
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !filePureService.isValidFileName(originalFilename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }

        // 获取文件类型
        String fileExtension = filePureService.getFileExtension(originalFilename);
        // 设置新的文件名称然后合并字符串
        String safeFileName = UUID.randomUUID().toString() + (fileExtension.isEmpty() ? "" : "." + fileExtension);

        InputStream inputStream = null;
        try {
            // 构建存储路径(包含用户名)，工厂方法创建一个Path对象，根据设置的操作系统创建绝对路径type存放
            Path userRootPath = Paths.get(fileStorageLocation,type).toAbsolutePath().normalize();
            // 创建文件夹,如果存在就忽略
            Files.createDirectories(userRootPath);

            // 创建目标文件路径，合并刚刚的存储路径和刚刚新设置的文件名
            Path targetPath = userRootPath.resolve(safeFileName);

            // 保存文件
            inputStream = file.getInputStream();

            // 从输入流的二进制字节数组写入到targetPath目标文件完整路径，StandardCopyOption.REPLACE_EXISTING:如果存在则覆盖文件
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件上传完成: {}, 存储为: {}", originalFilename, safeFileName);

            //把头像更新进数据库里
            String setAvatarUrl = null;
            if (SecurityConstants.IMAGE_TYPE.contains(fileExtension)) {
                UserProfile userProfile = userProfileService.findUserProfileByUsername(username);
                setAvatarUrl = "http://localhost:8080/api/view/images/image-general/" + safeFileName;
                userProfile.setAvatar(setAvatarUrl);
                userProfileService.updateUserProfileByUserId(userProfile);
            }

            // 构建响应信息
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("avatarUrl", setAvatarUrl);
            fileInfo.put("originalFilename", originalFilename);
            fileInfo.put("storedFilename", safeFileName);
            fileInfo.put("size", file.getSize());
            fileInfo.put("type", type);
            fileInfo.put("contentType", file.getContentType());
            fileInfo.put("path", targetPath.toString());
            fileInfo.put("uploadedBy", username);

            return Result.success("文件上传成功", fileInfo);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "文件上传失败: " + e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }



    /**
     * 异步上传文件
     * <p>
     * 使用异步方式处理文件上传，提高服务器性能
     * </p>
     *
     * @param file 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @param authentication 当前认证信息
     * @return 上传结果，包含文件上传进度和状态
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFileAsync(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type,
            Authentication authentication) {

        // 文件为空检查
        if (file.isEmpty()) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "上传失败，文件为空");
        }

        // 文件大小检查
        if (file.getSize() > maxFileSize) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER,
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / (1024 * 1024)));
        }

        // 获取当前用户信息
        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        String username = customUserDetails.getUsername();

        // MIME类型检查
        String contentType = file.getContentType();
        if (contentType == null || !SecurityConstants.ALLOWED_MIME_TYPES.contains(contentType.toLowerCase())) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "不支持的文件类型: " + contentType);
        }

        // 获取原始文件名并安全检查
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !filePureService.isValidFileName(originalFilename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }

        // 获取文件类型
        String fileExtension = filePureService.getFileExtension(originalFilename);
        // 设置文件唯一名称，然后拼接文件类型
        String safeFileName = UUID.randomUUID().toString() + (fileExtension.isEmpty() ? "" : "." + fileExtension);

        try {
            // 创建异步任务处理文件上传
            CompletableFuture.runAsync(() -> {
                InputStream inputStream = null;
                try {
                    // 构建存储路径
                    Path userRootPath = Paths.get(fileStorageLocation,type).toAbsolutePath().normalize();
                    Files.createDirectories(userRootPath);

                    // 目标文件路径
                    Path targetPath = userRootPath.resolve(safeFileName);

                    // 保存文件
                    inputStream = file.getInputStream();
                    Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);


                    log.info("文件异步上传完成: {}, 存储为: {}", originalFilename, safeFileName);

                    //把头像更新进数据库里
                    String setAvatarUrl = null;
                    if (SecurityConstants.IMAGE_TYPE.contains(fileExtension)) {
                        UserProfile userProfile = userProfileService.findUserProfileByUsername(username);
                        setAvatarUrl = "http://localhost:8080/image-general/" + safeFileName;
                        userProfile.setAvatar(setAvatarUrl);
                        userProfileService.updateUserProfileByUserId(userProfile);
                    }


                } catch (IOException e) {
                    log.error("异步文件上传处理失败: {}", originalFilename, e);
                } finally {
                    if (inputStream != null) {
                        try {
                            inputStream.close();
                        } catch (IOException e) {
                            log.error("关闭输入流失败", e);
                        }
                    }
                }
            });

            // 立即返回上传已接收的响应
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("originalFilename", originalFilename);
            fileInfo.put("storedFilename", safeFileName);
            fileInfo.put("size", file.getSize());
            fileInfo.put("type", type);
            fileInfo.put("contentType", file.getContentType());
            fileInfo.put("uploadedBy", username);
            fileInfo.put("status", "PROCESSING"); // 标记为处理中
            fileInfo.put("message", "文件上传请求已接收，正在后台处理");

            return Result.success("文件上传请求已接收", fileInfo);
        } catch (Exception e) {
            log.error("处理文件上传请求失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理文件上传请求失败: " + e.getMessage());
        }
    }
}
