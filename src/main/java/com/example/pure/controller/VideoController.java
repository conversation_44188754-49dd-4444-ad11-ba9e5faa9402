package com.example.pure.controller;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.FileMetadata;
import com.example.pure.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * 视频控制器
 * <p>
 * 提供视频流式播放功能的API接口
 * </p>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "视频服务", description = "视频流式播放相关接口")
public class VideoController {

    private final FileService fileService;

    /**
     * 处理视频流请求的端点（传统实现）
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_video.mp4）
     * @param headers  请求头，用于处理范围请求（Range Requests）
     * @return 包含视频流的 ResponseEntity
     * @deprecated 推荐使用 {@link #streamVideoAsync(String, HttpHeaders)} 代替
     */
    @GetMapping("/video-legacy/{fileName:.+}") // {fileName:.+} 允许文件名中包含点（.）
    @Operation(summary = "视频流式播放（传统实现）", description = "使用传统方式支持视频分段加载和范围请求")
    public ResponseEntity<Resource> streamVideo(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("请求视频流（传统方式）: {}", fileName);
            return fileService.streamVideo(fileName, headers);
        } catch (IOException e) {
            log.error("处理视频流时发生错误: {}", fileName, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取视频流失败: " + e.getMessage());
        }
    }

    /**
     * 处理视频流请求的端点（异步实现）
     * <p>
     * 使用ResponseBodyEmitter异步发送视频流，提高性能和资源利用率
     * </p>
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_video.mp4）
     * @param headers  请求头，用于处理范围请求（Range Requests）
     * @return ResponseEntity包含ResponseBodyEmitter，用于异步发送视频流
     */
    @GetMapping("/video/{fileName:.+}")
    @Operation(summary = "视频流式播放（异步）", description = "使用异步方式支持视频分段加载和范围请求，提高性能")
    public ResponseEntity<ResponseBodyEmitter> streamVideoAsync(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("请求视频流（异步）: {}", fileName);

            // 1. 获取文件元数据
            FileMetadata metadata;
            try {
                metadata = fileService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw BusinessException.of(ResponseCode.DATA_NOT_FOUND, "无法获取文件信息: " + e.getMessage());
            }

            if (!metadata.isExists() || !metadata.isReadable()) {
                log.error("请求的视频文件不存在或不可读: {}", fileName);
                throw BusinessException.of(ResponseCode.NOT_FOUND, "视频文件不存在或不可读: " + fileName);
            }

            // 2. 设置响应头
            HttpHeaders responseHeaders = new HttpHeaders();

            // 设置内容类型
            String contentType = metadata.getContentType();
            if (contentType == null || contentType.isEmpty()) {
                contentType = "video/mp4"; // 默认类型
                // 根据文件扩展名确定内容类型
                if (fileName.endsWith(".webm")) {
                    contentType = "video/webm";
                } else if (fileName.endsWith(".ogg")) {
                    contentType = "video/ogg";
                } else if (fileName.endsWith(".mp3")) {
                    contentType = "audio/mpeg";
                }
            }
            responseHeaders.setContentType(MediaType.parseMediaType(contentType));

            // 3. 检查是否有Range请求头并设置相应的响应头
            String range = headers.getFirst(HttpHeaders.RANGE);
            HttpStatus status = HttpStatus.OK;

            if (range != null && range.startsWith("bytes=")) {
                // 启用范围请求支持
                responseHeaders.add(HttpHeaders.ACCEPT_RANGES, "bytes");

                String[] ranges = range.substring("bytes=".length()).split("-");
                long rangeStart = Long.parseLong(ranges[0]);
                long rangeEnd = metadata.getFileSize() - 1;

                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]);
                }

                // 计算内容长度
                long contentLength = rangeEnd - rangeStart + 1; // 因为rangeStart字节也在请求范围内，内容长度按端点的区间来计算，比如范围11-13，13-11=2+1，3个字节

                // 设置Content-Range头
                responseHeaders.add(HttpHeaders.CONTENT_RANGE,
                    "bytes " + rangeStart + "-" + rangeEnd + "/" + metadata.getFileSize());

                // 设置内容长度
                responseHeaders.setContentLength(contentLength);

                // 使用206 Partial Content状态码
                status = HttpStatus.PARTIAL_CONTENT;
            } else {
                // 设置完整内容长度
                responseHeaders.setContentLength(metadata.getFileSize());
            }

            // 4. 获取异步发送器
            CompletableFuture<ResponseBodyEmitter> emitterFuture = fileService.streamFileAsync(fileName, headers);

             /*
             主线程调用子线程完成异步后回来接着运行下面的代码
             处理异步返回结果，由于返回的已完成状态的对象所以包含ResponseBodyEmitter对象，所以不是null，
             直接返回http响应给Spring Mvc然后和客户端建立长连接，并将响应体的空的ResponseBodyEmitter对象与
             改连接关联，子线程通过emitter.send(dataChunk);持续发送数据给客户端
             */
            ResponseBodyEmitter emitter = emitterFuture.getNow(null);
            if (emitter != null) {
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter);
            } else {
                // 如果emitter还没准备好，等待它完成
                ResponseBodyEmitter emitter2 = emitterFuture.join();
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter2);
            }

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理视频流时发生错误: {}", fileName, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取视频流失败: " + e.getMessage());
        }
    }

    /**
     * 根据视频ID获取视频流的API接口（异步实现）
     * <p>
     * 此接口允许通过视频标识符而不是直接文件名获取视频流
     * </p>
     *
     * @param videoId 视频标识符
     * @param headers 请求头，用于处理范围请求（Range Requests）
     * @return ResponseEntity包含ResponseBodyEmitter，用于异步发送视频流
     */
    @GetMapping("/api/videos/stream/{videoId}")
    @Operation(summary = "根据ID获取视频流", description = "通过视频ID访问视频流，支持分段加载和范围请求")
    public ResponseEntity<ResponseBodyEmitter> streamVideoByIdAsync(
            @PathVariable String videoId,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("根据ID请求视频流: {}", videoId);

            // 1. 根据视频ID映射到实际文件名（在实际应用中，应从数据库中获取）
            String fileName = mapVideoIdToFileName(videoId);

            // 2. 获取文件元数据
            FileMetadata metadata;
            try {
                metadata = fileService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw BusinessException.of(ResponseCode.DATA_NOT_FOUND, "无法获取视频信息: " + e.getMessage());
            }

            if (!metadata.isExists() || !metadata.isReadable()) {
                log.error("请求的视频不存在或不可读: ID={}, 文件={}", videoId, fileName);
                throw BusinessException.of(ResponseCode.NOT_FOUND, "视频不存在或不可读: ID=" + videoId);
            }

            // 3. 设置响应头
            HttpHeaders responseHeaders = new HttpHeaders();

            // 设置内容类型
            String contentType = metadata.getContentType();
            if (contentType == null || contentType.isEmpty()) {
                contentType = "video/mp4"; // 默认类型
                // 根据文件扩展名确定内容类型
                if (fileName.endsWith(".webm")) {
                    contentType = "video/webm";
                } else if (fileName.endsWith(".ogg")) {
                    contentType = "video/ogg";
                } else if (fileName.endsWith(".mp3")) {
                    contentType = "audio/mpeg";
                }
            }
            responseHeaders.setContentType(MediaType.parseMediaType(contentType));

            // 4. 检查是否有Range请求头并设置相应的响应头
            String range = headers.getFirst(HttpHeaders.RANGE);
            HttpStatus status = HttpStatus.OK;

            if (range != null && range.startsWith("bytes=")) {
                // 启用范围请求支持
                responseHeaders.add(HttpHeaders.ACCEPT_RANGES, "bytes");

                String[] ranges = range.substring("bytes=".length()).split("-");
                long rangeStart = Long.parseLong(ranges[0]);
                long rangeEnd = metadata.getFileSize() - 1;

                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]);
                }

                // 计算内容长度
                long contentLength = rangeEnd - rangeStart + 1;

                // 设置Content-Range头
                responseHeaders.add(HttpHeaders.CONTENT_RANGE,
                    "bytes " + rangeStart + "-" + rangeEnd + "/" + metadata.getFileSize());

                // 设置内容长度
                responseHeaders.setContentLength(contentLength);

                // 使用206 Partial Content状态码
                status = HttpStatus.PARTIAL_CONTENT;
            } else {
                // 设置完整内容长度
                responseHeaders.setContentLength(metadata.getFileSize());
            }

            // 5. 获取异步发送器
            CompletableFuture<ResponseBodyEmitter> emitterFuture = fileService.streamFileAsync(fileName, headers);

            // 处理异步返回结果
            ResponseBodyEmitter emitter = emitterFuture.getNow(null);
            if (emitter != null) {
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter);
            } else {
                // 如果emitter还没准备好，等待它完成
                ResponseBodyEmitter emitter2 = emitterFuture.join();
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter2);
            }

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理视频流时发生错误: ID={}", videoId, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取视频流失败: " + e.getMessage());
        }
    }

    /**
     * 获取视频信息的API接口
     *
     * @param videoId 视频标识符
     * @return 视频信息响应
     */
    @GetMapping("/api/videos/info/{videoId}")
    @Operation(summary = "获取视频信息", description = "获取视频文件的元数据信息，如大小、类型等")
    public Result<VideoInfo> getVideoInfo(@PathVariable String videoId) {
        try {
            log.debug("请求视频信息: ID={}", videoId);

            // 根据视频ID映射到实际文件名
            String fileName = mapVideoIdToFileName(videoId);

            // 获取文件元数据
            FileMetadata metadata;
            try {
                metadata = fileService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw new BusinessException(ResponseCode.DATA_NOT_FOUND, "无法获取视频信息: " + e.getMessage());
            }

            // 检查文件是否存在和可读
            if (!metadata.isExists() || !metadata.isReadable()) {
                log.error("请求的视频不存在或不可读: ID={}, 文件={}", videoId, fileName);
                throw new BusinessException(ResponseCode.NOT_FOUND, "视频不存在或不可读");
            }

            // 构建返回信息
            VideoInfo info = new VideoInfo();
            info.setVideoId(videoId);
            info.setFileName(fileName);
            info.setFileSize(metadata.getFileSize());
            info.setContentType(metadata.getContentType());
            info.setStreamUrl("/api/videos/stream/" + videoId);

            return Result.success("获取视频信息成功", info);

        } catch (Exception e) {
            log.error("获取视频信息失败: ID={}", videoId, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取视频信息失败: " + e.getMessage());
        }
    }

    /**
     * 将视频ID映射到文件名（示例实现）
     * <p>
     * 在实际应用中，应该从数据库中查询视频记录以获取正确的文件名
     * </p>
     *
     * @param videoId 视频ID
     * @return 对应的文件名
     */
    private String mapVideoIdToFileName(String videoId) {
        // 示例实现，根据视频ID前缀确定视频类型
        // 实际应用中应该查询数据库获取正确的文件名
        if (videoId.startsWith("demo")) {
            return "demo.mp4";
        } else if (videoId.startsWith("intro")) {
            return "introduction.mp4";
        } else if (videoId.startsWith("tutorial")) {
            return "tutorial.mp4";
        } else {
            // 默认返回测试视频
            return "sample.mp4";
        }
    }

    /**
     * 视频信息数据传输对象
     */
    @Data
    public static class VideoInfo {
        private String videoId;      // 视频ID
        private String fileName;     // 文件名
        private long fileSize;       // 文件大小（字节）
        private String contentType;  // 内容类型
        private String streamUrl;    // 流式播放URL
    }
}
