package com.example.pure.controller;


import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.FileMetadata;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.FilePureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.security.core.Authentication;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * 图片控制器
 * <p>
 * 提供图片查看功能的API接口
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/view/images")
public class PureImageFileController {

    private final FilePureService filePureService;


    @Autowired
    PureImageFileController(FilePureService filePureService) {
        this.filePureService = filePureService;

    }



    /**
     * 处理图片查看请求的端点（传统实现）
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_image.jpg）
     * @param headers  请求头，用于处理范围请求（Range Requests）
     * @return 包含图片数据的 ResponseEntity
     * @deprecated 推荐使用viewImageAsync方法代替
     */
    @GetMapping("/image-general/{fileName:.+}")
    public ResponseEntity<Resource> viewImage(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers,Authentication authentication) {

        try {
            log.debug("请求图片查看（传统方式）: {}", fileName);
            return filePureService.streamVideo(fileName, headers);
        } catch (IOException e) {
            log.error("处理图片查看时发生错误: {}", fileName, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 处理图片查看请求的端点（异步实现）
     * <p>
     * 使用ResponseBodyEmitter异步发送图片数据，提高性能和资源利用率
     * </p>
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_image.jpg）
     * @param headers  请求头，用于处理范围请求（Range Requests）
     * @return ResponseEntity包含ResponseBodyEmitter，用于异步发送图片数据
     */
    @GetMapping("/image/{fileName:.+}")
    public ResponseEntity<ResponseBodyEmitter> viewImageAsync(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers,
            Authentication authentication) {

        try {
            CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
            String currentUsername = customUserDetails.getUsername();
            log.debug("请求图片查看（异步）用户: {}, 文件名: {}", currentUsername, fileName);

            // 1. 获取文件元数据
            FileMetadata metadata;
            try {
                metadata = filePureService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw BusinessException.of(ResponseCode.DATA_NOT_FOUND, "无法获取图片信息: " + e.getMessage());
            }

            if (!metadata.isExists() || !metadata.isReadable()) {
                log.error("请求的图片不存在或不可读: {}", fileName);
                throw BusinessException.of(ResponseCode.NOT_FOUND, "图片文件不存在或不可读: " + fileName);
            }

            // 2. 设置响应头
            HttpHeaders responseHeaders = new HttpHeaders();

            // 设置内容类型
            String contentType = metadata.getContentType();
            if (contentType == null || contentType.isEmpty()) {
                contentType = "image/jpeg"; // 默认类型
                // 根据文件扩展名确定内容类型
                if (fileName.endsWith(".png")) {
                    contentType = "image/png";
                } else if (fileName.endsWith(".gif")) {
                    contentType = "image/gif";
                } else if (fileName.endsWith(".webp")) {
                    contentType = "image/webp";
                } else if (fileName.endsWith(".svg")) {
                    contentType = "image/svg+xml";
                }
            }

            responseHeaders.setContentType(MediaType.parseMediaType(contentType));

            // 3. 检查是否有Range请求头并设置相应的响应头
            String range = headers.getFirst(HttpHeaders.RANGE);
            HttpStatus status = HttpStatus.OK;

            if (range != null && range.startsWith("bytes=")) {
                // 启用范围请求支持
                responseHeaders.add(HttpHeaders.ACCEPT_RANGES, "bytes");

                String[] ranges = range.substring("bytes=".length()).split("-");
                long rangeStart = Long.parseLong(ranges[0]);
                long rangeEnd = metadata.getFileSize() - 1;

                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]);
                }

                // 计算内容长度
                long contentLength = rangeEnd - rangeStart + 1;

                // 设置Content-Range头用于指示响应体中返回的是资源的哪一部分数据，以及资源的完整大小。 它通常与HTTPRange请求一起使用
                responseHeaders.add(HttpHeaders.CONTENT_RANGE,
                        "bytes " + rangeStart + "-" + rangeEnd + "/" + metadata.getFileSize());

                // 设置内容长度,响应体里的ContentLength，用于下载显示进度条、连接管理、错误检测
                responseHeaders.setContentLength(contentLength);

                // 使用206 Partial Content状态码
                status = HttpStatus.PARTIAL_CONTENT;
            } else {
                // 设置完整内容长度
                responseHeaders.setContentLength(metadata.getFileSize());
            }

            // 再次记录用户名，确认调用 service 方法前的状态
            log.debug("准备调用 streamFileAsync，用户: {}, 文件名: {}", customUserDetails.getUsername(), fileName);

            // 4. 获取异步发送器
            CompletableFuture<ResponseBodyEmitter> emitterFuture = filePureService.streamFileAsync(fileName, headers);

            // 处理异步返回结果
            ResponseBodyEmitter emitter = emitterFuture.getNow(null);
            if (emitter != null) {
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter);
            } else {
                // 如果emitter还没准备好，等待它完成，阻塞并获取已完成状态的值
                ResponseBodyEmitter emitter2 = emitterFuture.join();
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter2);
            }

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理图片查看时发生错误: {}", fileName, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取图片失败: " + e.getMessage());
        }
    }

}
