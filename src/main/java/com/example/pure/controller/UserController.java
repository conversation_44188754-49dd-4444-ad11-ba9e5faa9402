// src/main/java/com/example/pure/controller/UserController.java
package com.example.pure.controller;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.Result;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.*;
import com.example.pure.model.entity.User;
import com.example.pure.service.*;
import com.example.pure.util.IpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;



/**
 * 用户控制器
 * 处理用户相关的请求
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user")
public class UserController {

    private final UserService userService;
    private final AccessLogService accessLogService;
    private final UserRateLimitService userRateLimitService;
    private final IpUtil ipUtil;
    private final IpRateLimitService ipRateLimitService;
    private final VerificationService verificationService;
    private final UserProfileService userProfileService;

    // Spring容器创建Bean实例时，自动注入匹配的Bean进构造函数的参数里,相当于UserService userService=new UserService();
    @Autowired
    public UserController(UserService userService, AccessLogService accessLogService, UserRateLimitService userRateLimitService,
                          IpUtil ipUtil, IpRateLimitService ipRateLimitService, VerificationService verificationService, UserProfileService userProfileService) {
        this.userService = userService;
        this.accessLogService = accessLogService;
        this.userRateLimitService = userRateLimitService;
        this.ipUtil=ipUtil;
        this.ipRateLimitService=ipRateLimitService;
        this.verificationService=verificationService;
        this.userProfileService = userProfileService;
    }

    /**
     * 获取当前用户信息
     *  AuthenticationPrincipal:获取用户的认证信息，通常存储在 SecurityContextHolder 的 Authentication 对象的Principal对象中
     */
    @GetMapping("/me")
    @PreAuthorize("isAuthenticated()")
    public Result<UserDTO> getCurrentUser(@AuthenticationPrincipal UserDetails userDetails) {
        //用Redis检查访问次数，如超出次数就限制访问
        userRateLimitService.checkUserRateLimit("getCurrentUser");
        log.debug("获取当前用户信息: {}", userDetails.getUsername());
        UserDTO user = userService.findUserByUsername(userDetails.getUsername());
        return Result.success("获取成功", user);

    }

    /**
     * 用户注册
     * info() 方法：表示记录一条 INFO 级别 的日志（用于常规信息输出）
     * 括号里手动填写字符串用于清晰描述业务，括号里添加{}动态替换变量（把后面的变量参数替换到{}里）
     *
     */

    @PostMapping("/createUser")
    @Operation(summary = "用户注册",description = "用户注册账号方法")
    @ApiResponse(responseCode = "200", description = "成功")
    public Result<UserDTO> createUser(
            @Parameter(description = "用户注册信息", required = true)
            @Valid @RequestBody RegisterRequest createRequest,
            @Parameter(description = "获取用户注册IP信息", required = true)
            HttpServletRequest httpRequest) {
        String clientIp = ipUtil.getClientIp(httpRequest);
        ipRateLimitService.checkIpRateLimit(clientIp, "createUser");

        log.info("用户注册: {}", createRequest.getUsername());
        UserDTO newUser = userService.createUser(createRequest);
        return Result.success("注册成功", newUser);
    }



    // 获取users表和user_profile表的关联数据
    @GetMapping("/UserWithUserProfile")
    @Operation(summary = "通过用户名查询用户",description = "查询用户，通过名称查询，没有写就查询当前上下文用户")
    @ApiResponse(responseCode = "200", description = "成功")
    // 方法级别的验证，从SecurityContextHolder获取当前角色，用来检查当前角色信息
    @PreAuthorize("hasRole('ADMIN') or isAuthenticated()")
    public Result<UserWithUserProfileDTO> getUserWithUserProfileByUsername(HttpServletRequest request, @Parameter(description = "用户查询的用户名", required = true)
            @AuthenticationPrincipal UserDetails userDetails) {
        userRateLimitService.checkUserRateLimit("getUserWithUserProfile");
        String clientIp=ipUtil.getClientIp(request);
        String username=userDetails.getUsername();
        if (username.isEmpty()){
            throw new BusinessException("没有查询到用户信息");
        }
        UserWithUserProfileDTO userWithUserProfile=userService.getUserWithUserProfileDTOByUsername(username);
        log.debug("获取用户信息：{}",username);
        // 记录访问日志
        accessLogService.logAccess(userWithUserProfile.getId(), "GET_USER_WITH_USER_PROFILE",clientIp);
        return Result.success("查询用户名获取全部资料成功", userWithUserProfile);
    }

    /**
     * 获取用户信息
     * PreAuthorize声明需要需要管理员权限或当前用户id到SpringSecurity，请求到控制器的时候SpringSecurity会根据上下文来判断是否满足权限
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isCurrentUser(#id)")
    public Result<UserDTO> getUser(@PathVariable Long id, HttpServletRequest request) {
        userRateLimitService.checkUserRateLimit("getUser");
        String ClientIp=ipUtil.getClientIp(request);
        log.debug("获取用户信息: {}", id);
        UserDTO user = userService.findById(id);

        // 记录访问日志
        accessLogService.logAccess(id, "GET_USER_INFO",ClientIp);

        return Result.success("获取成功", user);

    }

    /**
     * 获取所有用户基本信息
     * Spring Security会在验证用户权限的时候，添加ROLE_前缀给ADMIN
     */
    @GetMapping("/AllUsers")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<List<UserDTO>> getAllUsers() {
        userRateLimitService.checkUserRateLimit("getAllUsers");
        log.debug("获取所有用户");
        List<UserDTO> users = userService.findAll();
        if (users.isEmpty()) {
            log.error("查询失败");
            throw new BusinessException("查询失败");
        }
        return Result.success("获取成功", users);
    }



    /**
     * 更新用户密码,或找回密码
     */
    @PutMapping("/password")
    public Result<?> updatePassword(
            @Valid @RequestBody PasswordUpdateDTO passwordDTO
    ) {
        userRateLimitService.checkUserRateLimit("updatePassword");
        boolean verify=verificationService.verifyEmailCode(passwordDTO.getEmail(),passwordDTO.getVerifyCode());
        if (!verify) {
            throw new BusinessException("验证码不正确或已过期");
        }

        Long userId=userProfileService.findUserIdByEmail(passwordDTO.getEmail());
        User curentUser=userService.findUserWithPasswordByUserId(userId);
        userService.updatePassword(passwordDTO,curentUser);
        return Result.success("密码更新成功");
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteUser(@PathVariable Long id) {
        log.debug("删除用户: {}", id);
        userService.deleteById(id);
        return Result.success("删除成功");
    }

    /**
     * 手动分页查询用户列表
     * <p>
     * 使用offset和limit实现手动分页，不依赖PageHelper插件
     * </p>
     *
     * @param pageRequest 分页请求参数
     * @return 分页结果，包含用户DTO列表
     */
    @PostMapping("/page")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "分页查询用户", description = "管理员可以分页查询所有用户，支持关键字搜索")
    public Result<PageFinalResult<UserDTO>> getUsersByPage(
            @Parameter(description = "分页参数") @Valid @RequestBody PageRequestDTO pageRequest) {

        // 检查访问频率限制
        userRateLimitService.checkUserRateLimit("getUsersByPage");

        // 记录操作日志
        log.debug("分页查询用户列表: 页码={}, 每页大小={}, 关键字={}",
                pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());

        // 调用服务执行分页查询
        PageFinalResult<UserDTO> pageResult = userService.findByPage(pageRequest);

        return Result.success("查询成功", pageResult);
    }
}
