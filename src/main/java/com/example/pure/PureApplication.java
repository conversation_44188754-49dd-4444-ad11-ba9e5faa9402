// src/main/java/com/example/demo13/PureApplication.java
package com.example.pure;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 应用程序入口类
 *
 * 功能特性：
 * - MyBatis Mapper 扫描
 * - 缓存支持
 * - 异步任务支持
 * - 定时任务支持
 * - 事务管理
 *
 * <AUTHOR> Name
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication(exclude = JmxAutoConfiguration.class)
@EnableTransactionManagement
@EnableAsync
@MapperScan("com.example.pure.mapper")
public class PureApplication {

    /**
     * 应用程序主入口
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(PureApplication.class, args);
    }
}
