package com.example.pure.security;

import com.example.pure.model.entity.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;

/**
 * 自定义UserDetails实现
 * <p>
 * 实现Spring Security的UserDetails接口，提供用户认证和授权所需的信息。
 * 不直接暴露User实体，而是存储必要的用户信息，符合封装原则和安全最佳实践。
 * </p>
 */
public class CustomUserDetails implements UserDetails {

    // 存储必要的用户信息，而不是整个User实体
    private final Long userId;
    private final String username;
    private final String password;
    private final boolean accountNonExpired;
    private final boolean accountNonLocked;
    private final boolean credentialsNonExpired;
    private final boolean enabled;

    // 用户权限集合
    private final Collection<? extends GrantedAuthority> authorities;

    /**
     * 构造函数，从User实体提取必要信息
     *
     * @param user 用户实体
     * @param authorities 用户权限集合
     */
    public CustomUserDetails(User user, Collection<? extends GrantedAuthority> authorities) {
        // 从User实体提取必要信息，避免直接引用
        this.userId = user.getId();
        this.username = user.getUsername();
        this.password = user.getPassword();
        this.accountNonExpired = user.isAccountNonExpired();
        this.accountNonLocked = user.isAccountNonLocked();
        this.credentialsNonExpired = user.isCredentialsNonExpired();
        this.enabled = user.isEnabled();
        this.authorities = authorities;
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public Long getUserId() {
        return userId;
    }


    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }
}
