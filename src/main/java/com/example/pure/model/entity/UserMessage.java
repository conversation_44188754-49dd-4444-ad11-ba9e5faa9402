package com.example.pure.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserMessage {

    private Long id;

    private Long userId;

    private Long messageId;

    private Boolean isRead;

    private LocalDateTime readTime;
}