package com.example.pure.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 角色实体类
 * 用于定义系统中的用户角色
 */
@Data
@Schema(description = "用户的角色信息")
public class Role {
    /**
     * 角色ID
     */
    @Schema(description = "ID",example = "1",required = true)
    private Long id;

    /**
     * 角色名称
     * 必须以 ROLE_ 开头，只能包含大写字母、数字和下划线
     */
    @NotBlank(message = "角色名称不能为空")
    @Length(min = 5, max = 50, message = "角色名称长度必须在5-50个字符之间")
    @Pattern(regexp = "^ROLE_[a-z0-9_]*$", message = "角色名称必须以ROLE_开头，且只能包含大写字母、数字和下划线")
    @Schema(description = "角色名称",example = "Admin",required = true)
    private String name;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @Schema(description = "创建角色时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @Schema(description = "更新角色时间")
    private LocalDateTime updatedTime;
}

/**
 * 以下是@Data注解自动生成的代码说明：
 *
 * 1. 自动生成的获取方法：
 *    public Long getId() {
 *        return this.id;
 *    }
 *    public String getName() {
 *        return this.name;
 *    }
 *    public LocalDateTime getCreateTime() {
 *        return this.createTime;
 *    }
 *    public LocalDateTime getUpdateTime() {
 *        return this.updateTime;
 *    }
 *
 * 2. 自动生成的设置方法：
 *    public void setId(Long id) {
 *        this.id = id;
 *    }
 *    public void setName(String name) {
 *        this.name = name;
 *    }
 *    public void setCreateTime(LocalDateTime createTime) {
 *        this.createTime = createTime;
 *    }
 *    public void setUpdateTime(LocalDateTime updateTime) {
 *        this.updateTime = updateTime;
 *    }
 *
 * 3. 自动生成的对象比较方法：
 *    public boolean equals(Object o) {
 *        if (o == this) return true;
 *        if (!(o instanceof Role)) return false;
 *        Role other = (Role) o;
 *        if (!other.canEqual((Object)this)) return false;
 *        if (this.getId() == null ? other.getId() != null : !this.getId().equals(other.getId())) return false;
 *        if (this.getName() == null ? other.getName() != null : !this.getName().equals(other.getName())) return false;
 *        if (this.getCreateTime() == null ? other.getCreateTime() != null : !this.getCreateTime().equals(other.getCreateTime())) return false;
 *        if (this.getUpdateTime() == null ? other.getUpdateTime() != null : !this.getUpdateTime().equals(other.getUpdateTime())) return false;
 *        return true;
 *    }
 *
 * 4. 自动生成的类型比较方法：
 *    protected boolean canEqual(Object other) {
 *        return other instanceof Role;
 *    }
 *
 * 5. 自动生成的哈希码方法：
 *    public int hashCode() {
 *        final int PRIME = 59;
 *        int result = 1;
 *        result = (result * PRIME) + (this.getId() == null ? 43 : this.getId().hashCode());
 *        result = (result * PRIME) + (this.getName() == null ? 43 : this.getName().hashCode());
 *        result = (result * PRIME) + (this.getCreateTime() == null ? 43 : this.getCreateTime().hashCode());
 *        result = (result * PRIME) + (this.getUpdateTime() == null ? 43 : this.getUpdateTime().hashCode());
 *        return result;
 *    }
 *
 * 6. 自动生成的字符串转换方法：
 *    public String toString() {
 *        return "Role(id=" + this.getId() + ", name=" + this.getName() +
 *               ", createTime=" + this.getCreateTime() + ", updateTime=" + this.getUpdateTime() + ")";
 *    }
 */
