package com.example.pure.model.entity;

import com.example.pure.annotation.DataMasking;
import com.example.pure.annotation.MaskingStrategy;
import com.example.pure.serializer.GenericDataMaskingSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

@Data
public class UserProfile {

    /** 用户ID */
    private Long id;

    /** 用户名 */
    private String username;

    /** 手机号 */
    private String phone;


    /**
     * 电子邮箱
     * message为错误消息提示
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Length(max = 100, message = "邮箱长度不能超过100个字符")
    @Schema(description = "邮箱", required = true,example = "<EMAIL>")
    @JsonSerialize(using = GenericDataMaskingSerializer.class) // 不使用默认序列化使用GenericDataMaskingSerializer类来序列化，并使用createContextual拿到@DataMasking 注解的 strategy 属性
    @DataMasking(strategy = MaskingStrategy.EMAIL)    // @DataMasking注解的实例都将拥有一个名为 strategy 的属性，并且这个属性必须是MaskingStrategy类型
    private String email;

    /**
     * 昵称
     */
    @Length(max = 50, message = "昵称长度不能超过50个字符")
    @Schema(description = "用户昵称",example = "AAA")
    @JsonSerialize(using = GenericDataMaskingSerializer.class)
    @DataMasking(strategy = MaskingStrategy.USERNAME) // 应用通用 Serializer，并指定用户名策略
    private String nickname;

    /**
     * 头像URL
     */
    @Length(max = 255, message = "头像URL长度不能超过255个字符")
    @Schema(description = "用户头像Url地址")
    private String avatar;

    /** 用户简介 */
    private String description;
}
