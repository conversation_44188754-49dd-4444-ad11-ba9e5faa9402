package com.example.pure.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Message {

    private Long id;

    private Long senderId;
    // USER,GROUP
    private String targetType;

    private Long targetId;

    private String title;

    private String content;

    // 消息类型,notification,chat
    private String messageType;

    private LocalDateTime createdTime;
}
