package com.example.pure.model.entity;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
public class VideoEpisodes {

    Long id;
    Long videoInfoId;
    String number;
    /**
     * R2存储中的对象键，用于标识视频文件在云存储中的位置
     */
    String objectKey;
    String duration;
    /**
     * 雪碧图在R2存储中的对象键
     */
    String spriteSheetObjectKey;
    LocalDateTime createdTime;
    LocalDateTime updatedTime;
}
