package com.example.pure.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户操作日志实体类
 * 对应数据库operating_log表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperatingLog {
    private Long id;          // 主键ID
    private Long userId;      // 操作用户ID
    private String ip;        // 用户IP地址
    private String address;   // 用户地理位置
    private String systemInfo;    // 用户操作系统
    private String browser;   // 用户浏览器
    private String summary;   // 操作摘要
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime operatingTime; // 操作时间
}
