package com.example.pure.model.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 消息数据传输对象，用于展示消息列表中的详细信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageDTO {

    private String senderName;

    /**
     * 消息ID
     */
    private Long id;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者头像
     */
    private String avatar;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 消息创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 用户是否已读
     */
    private Boolean isRead;

    /**
     * 用户读取时间
     */
    private LocalDateTime readTime;
}
