package com.example.pure.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)  // 忽略未知字段
public class LoginRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 人机验证令牌
     */
    @NotBlank(message = "人机验证令牌不能为空")
    private String recaptchaToken;

    /**
     * 邮箱
     */
    private String email;

    // 设备ID
    private String deviceId=null;
}

/**
 * 以下是@Data注解自动生成的代码说明：
 *
 * 1. 自动生成的获取方法：
 *    public String getUsername() {
 *        return this.username;
 *    }
 *    public String getPassword() {
 *        return this.password;
 *    }
 *
 * 2. 自动生成的设置方法：
 *    public void setUsername(String username) {
 *        this.username = username;
 *    }
 *    public void setPassword(String password) {
 *        this.password = password;
 *    }
 *
 * 3. 自动生成的对象比较方法：
 *    public boolean equals(Object o) {
 *        if (o == this) return true;
 *        if (!(o instanceof LoginRequest)) return false;
 *        LoginRequest other = (LoginRequest) o;
 *        if (!other.canEqual((Object)this)) return false;
 *        if (this.getUsername() == null ? other.getUsername() != null : !this.getUsername().equals(other.getUsername())) return false;
 *        if (this.getPassword() == null ? other.getPassword() != null : !this.getPassword().equals(other.getPassword())) return false;
 *        return true;
 *    }
 *
 * 4. 自动生成的类型比较方法：
 *    protected boolean canEqual(Object other) {
 *        return other instanceof LoginRequest;
 *    }
 *
 * 5. 自动生成的哈希码方法：
 *    public int hashCode() {
 *        final int PRIME = 59;
 *        int result = 1;
 *        result = (result * PRIME) + (this.getUsername() == null ? 43 : this.getUsername().hashCode());
 *        result = (result * PRIME) + (this.getPassword() == null ? 43 : this.getPassword().hashCode());
 *        return result;
 *    }
 *
 * 6. 自动生成的字符串转换方法：
 *    public String toString() {
 *        return "LoginRequest(username=" + this.getUsername() + ", password=" + this.getPassword() + ")";
 *    }
 */
