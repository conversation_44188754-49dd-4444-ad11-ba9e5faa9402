package com.example.pure.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息DTO
 * <p>
 * 用于WebSocket通信的消息数据传输对象
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 发送者
     */
    private String sender;

    /**
     * 接收者
     */
    private String recipient;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 是否已处理
     */
    private Boolean processed;
}
