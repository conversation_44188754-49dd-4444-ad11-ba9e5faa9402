package com.example.pure.model.dto;


import lombok.Data;

    /**
     * 文件元数据类
     */
    @Data
    public class FileMetadata {
        private String fileName;
        private String contentType;
        private long fileSize;
        private boolean exists;
        private boolean readable;

        public FileMetadata() {}

        public FileMetadata(String fileName, String contentType, long fileSize, boolean exists, boolean readable) {
            this.fileName = fileName;
            this.contentType = contentType;
            this.fileSize = fileSize;
            this.exists = exists;
            this.readable = readable;
        }

    }

