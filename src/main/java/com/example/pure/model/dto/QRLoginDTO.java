package com.example.pure.model.dto;

import com.example.pure.util.QRLoginRedisUtil.QRCodeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 二维码登录相关数据传输对象
 * 嵌套静态类
 */
public class QRLoginDTO {

    /**
     * 二维码创建请求响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRCodeResponse {
        /**
         * 二维码唯一标识
         */
        private String qrId;

        /**
         * 二维码内容 (可能是URL或Base64编码的图片)
         */
        private String qrContent;

        /**
         * 二维码过期时间
         */
        private LocalDateTime expireTime;

        private boolean expired;

        public boolean isExpired(){
            return expired = LocalDateTime.now().isAfter(expireTime);
        }
    }

    /**
     * 二维码状态响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRStatusResponse {
        /**
         * 二维码状态
         */
        private QRCodeStatus status;

        /**
         * 二维码是否过期
         */
        private boolean expired;

        /**
         * 用户信息 (仅当状态为CONFIRMED时有值)
         */
        private UserDTO userInfo;

        /**
         * JWT访问令牌 (仅当状态为CONFIRMED时有值)
         */
        private String accessToken;

        /**
         * JWT刷新令牌 (仅当状态为CONFIRMED时有值)
         */
        private String refreshToken;
    }

    /**
     * 二维码扫描请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRScanRequest {
        /**
         * 二维码唯一标识
         */
        @NotBlank(message = "二维码ID不能为空")
        private String qrId;

        /**
         * 用户令牌
         */
        @NotBlank(message = "用户令牌不能为空")
        private String token;
    }

    /**
     * 二维码确认请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRConfirmRequest {
        /**
         * 二维码唯一标识
         */
        private String qrId;

        /**
         * 确认结果 (true: 确认登录, false: 拒绝登录)
         */
        private boolean confirmed;
    }
}
