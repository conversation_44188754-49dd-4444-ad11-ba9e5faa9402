package com.example.pure.model.dto;

import com.example.pure.annotation.DataMasking;
import com.example.pure.annotation.MaskingStrategy;
import com.example.pure.serializer.GenericDataMaskingSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserWithUserProfileDTO
{
    @Schema(description = "用户ID",example = "1")

    private Long id;


    private String username;

    /** 手机号 */
    private String phone;

    private String nickname;

    @JsonSerialize(using = GenericDataMaskingSerializer.class) // 不使用默认序列化使用GenericDataMaskingSerializer类来序列化，并使用createContextual拿到@DataMasking 注解的 strategy 属性
    @DataMasking(strategy = MaskingStrategy.EMAIL)    // @DataMasking注解的实例都将拥有一个名为 strategy 的属性，并且这个属性必须是MaskingStrategy类型
    private String email;


    private String avatar;

    /** 用户简介 */
    private String description;

    private List<String> roles;


    private List<String> permissions;

    /**
     * 最后登录时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime lastLoginTime;


    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime updatedTime;


}
