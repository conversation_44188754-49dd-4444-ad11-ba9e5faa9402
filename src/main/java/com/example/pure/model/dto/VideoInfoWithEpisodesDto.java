package com.example.pure.model.dto;

import com.example.pure.model.entity.VideoEpisodes;
import com.example.pure.model.entity.VideoType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 视频上传请求DTO
 */
@JsonIgnoreProperties(ignoreUnknown = true) // JSON反序列话成java类时忽略未知的JSON字段
@Data
public class VideoInfoWithEpisodesDto {
    /**
     * 视频分集信息列表
     */
    @NotEmpty(message = "视频分集信息不能为空")
    private List<VideoEpisodesDTO> videoEpisodes;

    /**
     * 视频标题
     */
    @NotBlank(message = "视频标题不能为空")
    @Size(max = 100, message = "视频标题不能超过100个字符")
    private String title;

    /**
     * 视频描述
     */
    @Size(max = 500, message = "视频描述不能超过500个字符")
    private String description;

    /**
     * 视频封面图片URL
     */
    @NotBlank(message = "视频封面URL不能为空")
    private String coverImageUrl;

    /**
     * 视频类型列表
     */
    @NotEmpty(message = "视频类型不能为空")
    private List<VideoType> videoTypes;
}
