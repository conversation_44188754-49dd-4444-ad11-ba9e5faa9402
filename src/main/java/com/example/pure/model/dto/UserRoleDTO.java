package com.example.pure.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 用户角色关联DTO
 * 用于处理用户和角色之间的关联关系
 * DTO（数据传输对象），用来让后端和前端相互传输数据
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRoleDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long roleId;
}

/**
 * 以下是@Data注解自动生成的代码说明：
 *
 * 1. 自动生成的获取方法：
 *    public Long getUserId() {
 *        return this.userId;
 *    }
 *    public Long getRoleId() {
 *        return this.roleId;
 *    }
 *
 * 2. 自动生成的设置方法：
 *    public void setUserId(Long userId) {
 *        this.userId = userId;
 *    }
 *    public void setRoleId(Long roleId) {
 *        this.roleId = roleId;
 *    }
 *
 * 3. 自动生成的对象比较方法：
 *    public boolean equals(Object o) {
 *        if (o == this) return true;
 *        if (!(o instanceof UserRoleDTO)) return false;
 *        UserRoleDTO other = (UserRoleDTO) o;
 *        if (!other.canEqual((Object)this)) return false;
 *        if (this.getUserId() == null ? other.getUserId() != null : !this.getUserId().equals(other.getUserId())) return false;
 *        if (this.getRoleId() == null ? other.getRoleId() != null : !this.getRoleId().equals(other.getRoleId())) return false;
 *        return true;
 *    }
 *
 * 4. 自动生成的类型比较方法：
 *    protected boolean canEqual(Object other) {
 *        return other instanceof UserRoleDTO;
 *    }
 *
 * 5. 自动生成的哈希码方法：
 *    public int hashCode() {
 *        final int PRIME = 59;
 *        int result = 1;
 *        result = (result * PRIME) + (this.getUserId() == null ? 43 : this.getUserId().hashCode());
 *        result = (result * PRIME) + (this.getRoleId() == null ? 43 : this.getRoleId().hashCode());
 *        return result;
 *    }
 *
 * 6. 自动生成的字符串转换方法：
 *    public String toString() {
 *        return "UserRoleDTO(userId=" + this.getUserId() + ", roleId=" + this.getRoleId() + ")";
 *    }
 */
