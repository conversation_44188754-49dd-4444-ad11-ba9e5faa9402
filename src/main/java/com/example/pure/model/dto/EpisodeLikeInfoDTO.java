package com.example.pure.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用于返回单个视频分集点赞信息的DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单个视频分集的点赞信息响应体")
public class EpisodeLikeInfoDTO {

    @Schema(description = "当前分集的总点赞数", example = "123")
    private Long likesCount;

    @Schema(description = "当前登录用户是否已点赞", example = "true")
    private Boolean status;
}
