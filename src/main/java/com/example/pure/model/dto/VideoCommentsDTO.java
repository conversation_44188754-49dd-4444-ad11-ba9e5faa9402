package com.example.pure.model.dto;

import lombok.Data;

import java.time.Instant;

@Data
public class VideoCommentsDTO {

    Long id;
    Long videoEpisodesId;
    Long userId;
    Long parentCommentId;
    String content;
    // 仅在获取视频评论时候需要用户名和头像
    String username;
    String avatar;
    Long likesCount;
    Long dislikesCount;
    Boolean likeStatus;
    Instant createdTime;
    Instant updatedTime;
    String userLikeType;
}
