package com.example.pure.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 令牌响应DTO
 */
@Data
@NoArgsConstructor
public class TokenResponse {
    private String username;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    // 使用注解Json格式化成标准化：ISO 8601 是国际标准格式，所有现代编程语言和框架均支持解析
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date accessTokenExpiresIn;

    private String tokenType = "Bearer";

    public TokenResponse(String username,String accessToken, String refreshToken, Date accessTokenExpiresIn) {
        this.username = username;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.accessTokenExpiresIn = accessTokenExpiresIn;
    }


}


/**
 * 以下是@Data、@NoArgsConstructor、@AllArgsConstructor注解自动生成的代码说明：
 *
 * 1. 自动生成的获取方法：
 *    public String getAccessToken() {
 *        return this.accessToken;
 *    }
 *    public String getRefreshToken() {
 *        return this.refreshToken;
 *    }
 *
 * 2. 自动生成的设置方法：
 *    public void setAccessToken(String accessToken) {
 *        this.accessToken = accessToken;
 *    }
 *    public void setRefreshToken(String refreshToken) {
 *        this.refreshToken = refreshToken;
 *    }
 *
 * 3. 自动生成的无参构造方法（@NoArgsConstructor）：
 *    public TokenResponse() {
 *    }
 *
 * 4. 自动生成的全参构造方法（@AllArgsConstructor）：
 *    public TokenResponse(String accessToken, String refreshToken) {
 *        this.accessToken = accessToken;
 *        this.refreshToken = refreshToken;
 *    }
 *
 * 5. 自动生成的对象比较方法：
 *    public boolean equals(Object o) {
 *        if (o == this) return true;
 *        if (!(o instanceof TokenResponse)) return false;
 *        TokenResponse other = (TokenResponse) o;
 *        if (!other.canEqual((Object)this)) return false;
 *        if (this.getAccessToken() == null ? other.getAccessToken() != null : !this.getAccessToken().equals(other.getAccessToken())) return false;
 *        if (this.getRefreshToken() == null ? other.getRefreshToken() != null : !this.getRefreshToken().equals(other.getRefreshToken())) return false;
 *        return true;
 *    }
 *
 * 6. 自动生成的类型比较方法：
 *    protected boolean canEqual(Object other) {
 *        return other instanceof TokenResponse;
 *    }
 *
 * 7. 自动生成的哈希码方法：
 *    public int hashCode() {
 *        final int PRIME = 59;
 *        int result = 1;
 *        result = (result * PRIME) + (this.getAccessToken() == null ? 43 : this.getAccessToken().hashCode());
 *        result = (result * PRIME) + (this.getRefreshToken() == null ? 43 : this.getRefreshToken().hashCode());
 *        return result;
 *    }
 *
 * 8. 自动生成的字符串转换方法：
 *    public String toString() {
 *        return "TokenResponse(accessToken=" + this.getAccessToken() + ", refreshToken=" + this.getRefreshToken() + ")";
 *    }
 */
