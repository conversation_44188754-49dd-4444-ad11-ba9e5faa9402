package com.example.pure.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Builder
@Schema(description = "视频评论分页请求参数")
public class VideoComentsPageRequest {

    /**
     * 当前页码，从1开始
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @Schema(description = "页码，从1开始", example = "1", required = true, minimum = "1")
    private Integer pageNum;

    /**
     * 每页显示条数，默认为10
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小最小为1")
    @Max(value = 100, message = "每页大小最大为100")
    @Schema(description = "每页条数，默认10", example = "10", required = true, minimum = "1", maximum = "100")
    private Integer pageSize;

    /**
     * 查询关键字，可选、查询username、nickname、email
     */
    @Schema(description = "查询关键字，可用于模糊搜索", example = "关键字")
    private Long videoEpisodesId;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createdTime")
    private String orderBy;

    /**
     * 排序方向：asc(升序)/desc(降序)
     */
    @Schema(description = "排序方向：asc(升序)/desc(降序)", example = "desc", allowableValues = {"asc", "desc"})
    @Pattern(regexp = "^(asc|desc)$", message = "排序方向只能是asc或desc")
    @Builder.Default
    private String orderDirection = "desc";

    /**
     * 计算当前页的起始行数
     * <p>
     * 在手动分页查询中，需要计算跳过的记录数
     * </p>
     *
     * @return 起始行数（从0开始）
     */
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 创建默认的分页请求
     *
     * @return 默认分页请求，第1页，每页10条
     */
    public static PageRequestDTO defaultPage() {
        return PageRequestDTO.builder()
                .pageNum(1)
                .pageSize(10)
                .build();
    }
}
