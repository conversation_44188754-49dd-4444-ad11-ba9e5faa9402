package com.example.pure.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评论点赞信息的响应数据传输对象 (DTO)
 * <p>
 * 用于向客户端返回一条评论的点赞和踩的统计数据，以及当前用户的点赞状态。
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommentLikeInfoDTO {
    /**
     * 当前评论的总点赞数 (仅统计状态为激活的)
     */
    private long likeCount;

    /**
     * 当前评论的总踩数 (仅统计状态为激活的)
     */
    private long dislikeCount;


    // 用户点赞类型
    private String userLikeType;

    /**
     * 当前登录用户的点赞状态。
     * 可能的值: "liked", "disliked", "none"。
     */
    private Boolean LikeStatus; // "liked", "disliked", "none"
}
