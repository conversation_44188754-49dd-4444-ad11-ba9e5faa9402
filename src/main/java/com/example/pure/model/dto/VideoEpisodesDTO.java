package com.example.pure.model.dto;

import lombok.Data;

import java.time.Instant;


@Data
public class VideoEpisodesDTO {

    Long id;
    Long videoInfoId;
    String number;
    /**
     * R2存储中的对象键，用于标识视频文件在云存储中的位置
     */
    String objectKey;
    /**
     * 临时播放URL，由R2预签名URL生成，用于前端播放视频
     */
    String playUrl;
    String duration;
    /**
     * R2存储中雪碧图的对象键
     */
    String spriteSheetObjectKey;
    /**
     * 雪碧图的临时访问URL
     */
    String spriteSheetUrl;
    Instant createdTime;
    Instant updatedTime;
    Long likesCount;
    /**
     * 当前登录用户是否已点赞此分集
     * true: 已点赞, false: 未点赞/未登录
     */
    Boolean status = false;
}
