package com.example.pure.model.dto;

import lombok.Data;

import java.time.Instant;


@Data
public class VideoEpisodesDTO {

    Long id;
    Long videoInfoId;
    String number;
    String playUrl;
    String duration;
    String spriteSheetUrl;
    Instant createdTime;
    Instant updatedTime;
    Long likesCount;
    /**
     * 当前登录用户是否已点赞此分集
     * true: 已点赞, false: 未点赞/未登录
     */
    Boolean status = false;
}
