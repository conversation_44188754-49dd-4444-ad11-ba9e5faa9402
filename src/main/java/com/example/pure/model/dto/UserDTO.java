package com.example.pure.model.dto;

import com.example.pure.annotation.DataMasking;
import com.example.pure.annotation.MaskingStrategy;
import com.example.pure.model.entity.User;
import com.example.pure.serializer.GenericDataMaskingSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 用户数据传输对象，常用于返回给客户端请求使用
 * <p>
 * 用于在API响应中返回用户信息，不包含敏感数据如密码
 * </p>
 * 数据载体 (DTOs, Entities):
 * 通常不作为 Spring Bean 管理。
 * DTOs：主要用于数据传输，经常需要 new（尤其是在测试、服务层构建响应、手动创建时）。框架在反序列化请求时也会创建它们。
 * Entities：代表持久化数据，通常由 ORM 框架（通过 Repository）或通过 new（在准备保存新数据时）创建。、
 * 在Mybatis的XML表里UPDATE里不要把id和username添加到列里就不会执行更新这些列的数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    @JsonSerialize(using = GenericDataMaskingSerializer.class)
    @DataMasking(strategy = MaskingStrategy.USERNAME) // 应用通用 Serializer，并指定用户名策略
    private String username;

    @JsonSerialize(using = GenericDataMaskingSerializer.class)
    @DataMasking(strategy = MaskingStrategy.PASSWORD)
    private String password;


    /**
     * 最后登录时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant lastLoginTime;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant updatedTime;


    /**
     * 从User实体创建UserDTO对象
     *
     * @param user 用户实体
     * @return 用户DTO
     */
    public static UserDTO fromUser(User user) {
        if (user == null) {
            return null;
        }

        return UserDTO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .lastLoginTime(user.getLastLoginTime())
                .createdTime(user.getCreatedTime())
                .build();
    }
}
