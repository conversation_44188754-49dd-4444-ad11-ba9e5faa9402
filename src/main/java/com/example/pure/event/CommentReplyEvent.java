package com.example.pure.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class CommentReplyEvent extends ApplicationEvent {

    private final Long replierUserId;
    private final Long parentCommentId;
    private final String commentContent;
    private final Long videoEpisodesId; // To provide context for the notification

    public CommentReplyEvent(Object source, Long replierUserId, Long parentCommentId, String commentContent, Long videoEpisodesId) {
        super(source);
        this.replierUserId = replierUserId;
        this.parentCommentId = parentCommentId;
        this.commentContent = commentContent;
        this.videoEpisodesId = videoEpisodesId;
    }
} 