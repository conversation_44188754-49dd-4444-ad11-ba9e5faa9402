package com.example.pure.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 评论被点赞事件
 * <p>
 * 当一个用户首次对一条评论进行点赞时，由应用发布此事件。
 * 此事件用于触发后续操作，例如向被点赞的用户发送通知。
 * </p>
 */
@Getter
public class CommentLikedEvent extends ApplicationEvent {
    /**
     * 点赞用户的ID
     */
    private final Long likingUserId;
    /**
     * 点赞用户的用户名
     */
    private final String likingUsername;
    /**
     * 被点赞评论的作者ID
     */
    private final Long commentAuthorId;
    /**
     * 被点赞的评论ID
     */
    private final Long commentId;


    /**
     * 构造一个新的 CommentLikedEvent
     *
     * @param source          事件源 (通常是发布事件的服务实例)
     * @param likingUserId    点赞用户的ID
     * @param likingUsername  点赞用户的用户名
     * @param commentAuthorId 被点赞评论的作者ID
     * @param commentId       被点赞的评论ID
     */
    public CommentLikedEvent(Object source, Long likingUserId, String likingUsername, Long commentAuthorId, Long commentId) {
        super(source);
        this.likingUserId = likingUserId;
        this.likingUsername = likingUsername;
        this.commentAuthorId = commentAuthorId;
        this.commentId = commentId;
    }
} 