package com.example.pure.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class UserRegisteredEvent extends ApplicationEvent {

    private final Long userId;
    private final String username;
    // Mo<PERSON><PERSON><PERSON> dodać inne potrzebne dane, np. email, jeśli są potrzebne do wiadomości powitalnej

    /**
     * 创建一个新的 UserRegisteredEvent.
     * @param source 事件的来源对象 (通常是发布事件的服务实例)
     * @param userId 新注册用户的ID
     * @param username 新注册用户的用户名
     */
    public UserRegisteredEvent(Object source, Long userId, String username) {
        super(source);
        this.userId = userId;
        this.username = username;
    }
} 