package com.example.pure.mapper.primary;

import com.example.pure.model.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口
 * 处理角色相关的数据库操作
 */
@Mapper
public interface RoleMapper {
    /**
     * 插入新角色
     */
    void insertRole(Role role);

    /**
     * 根据ID查找角色
     */
    Role findById(Long id);

    /**
     * 根据角色名查找角色
     */
    Role findByName(String name);

    /**
     * 获取所有角色
     */
    List<Role> findAll();

    /**
     * 更新角色信息
     */
    void updateRole(Role role);

    /**
     * 删除角色
     */
    void deleteRole(Long id);

    /**
     * 删除角色关联的所有用户
     */
    void deleteUserRoles(Long roleId);


    /**
     * 查询指定用户ID的所有角色
     *
     * @param userId 用户ID
     * @return 该用户拥有的所有角色列表
     */
    List<Role> findByUserId(@Param("userId") Long userId);
}
