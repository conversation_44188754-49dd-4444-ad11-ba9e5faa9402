package com.example.pure.mapper.primary;

import com.example.pure.model.entity.VideoInfo;
import com.example.pure.model.entity.VideoType;

import java.util.List;

public interface PureVideoTypeMapper {

    int insertVideoType(VideoType videoType);

    VideoType findVideoTypeByName(String name);

    List<VideoType> findVideoTypeByLinkWithVideoId(Long videoId);

    List<VideoType> findAllVideoType();
}
