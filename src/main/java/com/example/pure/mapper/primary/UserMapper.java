// src/main/java/com/example/demo13/mapper/UserMapper.java
package com.example.pure.mapper.primary;

import com.example.pure.model.dto.UserWithUserProfileDTO;
import com.example.pure.model.entity.User;
import com.example.pure.model.dto.UserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;

import java.util.List;

/**
 * 用户数据访问层接口
 * 处理用户相关的数据库操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UserMapper {

    /**
     * 插入新用户
     *
     * @param user 用户信息
     */
    void insert(User user);

    /**
     * 根据ID查找用户信息
     *
     * @param id 用户ID
     * @return 用户信息，如果不存在返回null
     * value为缓存分组（cacheNames()的别名），key为redis的key，condition为缓存条件，unless为不缓存的条件
     */
    @Cacheable(value = "user:info", key = "#id", unless = "#result == null")
    UserDTO findById(@Param("id") Long id);

    @Cacheable(value = "user:info", key = "#username", unless = "#result == null")
    UserDTO findByUsername (@Param("username") String username);

    /**
     * 根据用户名查找用户全部资料
     *
     * @param username 用户名
     * @return 用户信息，如果不存在返回null
     */
    @Cacheable(value = "user:UserWithUserProfile", key = "#username", unless = "#result == null")
    UserWithUserProfileDTO UserWithUserProfileDTOByUsername(@Param("username") String username);




    @Cacheable(value = "user:info:UserwithPassword", key = "#username", unless = "#result == null")
    User findByUserWithPasswordByUsername(@Param("username") String username);

    /**
     * 根据ID查找用户信息，包含密码，只能修改密码用
     *
     * @param id 用户ID
     * @return 用户信息，如果不存在返回null
     */
    @Cacheable(value = "user:info:UserwithPassword", key = "#id", unless = "#result == null")
    User findUserWithPasswordByUserId(@Param("id") Long id);

    /**
     * 查找所有用户
     *
     * @return 用户列表
     */
    List<UserDTO> findAll();

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     */
    @CacheEvict(value = {"user:info", "user:info:UserwithPassword"}, key = "#user.id")
    int update(User user);

    /**
     * 删除用户
     * @CacheEvict为清除缓存
     * @param id 用户ID
     */
    @CacheEvict(value = "user:info", key = "#id")
    void deleteById(@Param("id") Long id);

    /**
     * 统计用户名数量
     * 用于检查用户名是否已存在
     *
     * @param username 用户名
     * @return 匹配的用户数量
     */
    int countByUsername(@Param("username") String username);

    /**
     * Count users with the given email address
     *
     * @param email the email address to check
     * @return the number of users with the given email
     */


    /**
     * 插入用户角色关系
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    int insertUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 删除用户的所有角色关系
     *
     * @param userId 用户ID
     */
    void deleteUserRoles(@Param("userId") Long userId);

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return true如果用户名已存在，否则返回false
     */
    default boolean existsByUsername(@Param("username") String username) {
        return countByUsername(username) > 0;
    }


    /**
     * 更新用户的刷新令牌
     *
     * @param user 用户对象，包含id和新的refreshToken
     */
    void updateRefreshToken(User user);

    /**
     * 手动分页查询用户列表
     * <p>
     * 根据偏移量和大小查询指定范围的用户记录
     * </p>
     *
     * @param offset  偏移量，从0开始
     * @param limit   返回记录数量限制
     * @param keyword 查询关键字（可选，用于模糊搜索用户名、昵称和邮箱）
     * @param orderBy 排序字段（可选，默认按ID排序）
     * @param orderDirection 排序方向（可选，默认降序）
     * @return 用户列表
     */
    List<User> findByPage(@Param("offset") int offset,
                          @Param("limit") int limit,
                          @Param("keyword") String keyword,
                          @Param("orderBy") String orderBy,
                          @Param("orderDirection") String orderDirection);

    /**
     * 统计符合条件的用户总数
     * <p>
     * 用于手动分页查询时计算总页数
     * </p>
     *
     * @param keyword 查询关键字（可选，用于模糊搜索用户名、昵称和邮箱）
     * @return 符合条件的用户总数
     */
    int countUsers(@Param("keyword") String keyword);
}
