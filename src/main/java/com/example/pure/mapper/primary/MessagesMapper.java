package com.example.pure.mapper.primary;

import com.example.pure.model.dto.MessageDTO;
import com.example.pure.model.entity.Message;
import com.example.pure.model.entity.UserMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessagesMapper {
    // Methods will be added later based on service layer requirements

    /**
     * 插入消息到 messages 表
     *
     * @param message 消息实体
     * @return 影响的行数
     */
    int insertMessage(Message message);

    /**
     * 插入消息到 user_messages 表
     *
     * @param userMessage 用户消息实体
     * @return 影响的行数
     */
    int insertUserMessage(UserMessage userMessage);

    /**
     * 统计指定用户未读消息的数量
     *
     * @param userId 用户ID
     * @return 未读消息数量
     */
    Long countUnreadMessagesByUserId(Long userId);
    /**
     * 将指定用户的所有未读消息标记为已读
     *
     * @param userId 用户ID
     * @param readTime 读取时间
     * @return 影响的行数
     */
    int markAllMessagesAsReadByUserId(@Param("userId") Long userId, @Param("readTime") java.time.LocalDateTime readTime);
/**
     * 将指定用户的特定消息标记为已读，并记录读取时间
     *
     * @param userId 用户ID
     * @param messageId 消息ID
     * @param readTime 读取时间
     * @return 影响的行数
     */
    int markMessageAsReadByUserIdAndMessageId(@Param("userId") Long userId, @Param("messageId") Long messageId, @Param("readTime") java.time.LocalDateTime readTime);

    /**
     * 分页查询用户消息详情，可按已读/未读/全部状态筛选
     *
     * @param userId 用户ID
     * @param status 状态 ("unread", "read", "all")
     * @param offset 偏移量
     * @param limit  每页数量
     * @return 消息DTO列表
     */
    List<MessageDTO> selectUserMessagesWithDetails(@Param("userId") Long userId, @Param("status") String status, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计符合条件的用户消息总数
     *
     * @param userId 用户ID
     * @param status 状态 ("unread", "read", "all")
     * @return 符合条件的消息总数
     */
    Long countUserMessagesWithDetails(@Param("userId") Long userId, @Param("status") String status);


    int deleteReadedMessagesByUserId(Long userId);

    int deleteMessageById(@Param("userId") Long userId, @Param("messageId") Long messageId);
}
