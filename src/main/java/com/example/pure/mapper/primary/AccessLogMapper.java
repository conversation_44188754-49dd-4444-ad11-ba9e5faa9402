package com.example.pure.mapper.primary;

import com.example.pure.model.entity.AccessLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

@Mapper
public interface AccessLogMapper {

    @Cacheable(value = "access_log", key = "#userId + ':' + #accessType + ':' + #accessDate", unless = "#result == null")
    AccessLog findByUserIdAndTypeAndDate(
            // 多个参数绑定到mybatis的xml表需要加@Param,单个可以不加.
            @Param("userId") Long userId,
            @Param("accessType") String accessType,
            @Param("accessDate") String accessDate);

    void insert(AccessLog accessLog);

    @CacheEvict(value = "access_log", key = "#accessLog.userId + ':' + #accessLog.accessType + ':' + #accessLog.accessDate")
    void updateAccessCount(AccessLog accessLog);
}
