package com.example.pure.mapper.primary;


import com.example.pure.model.entity.VideoInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PureVideoUrlMapper {

    // 插入视频信息
    int insertVideoInfo(VideoInfo videoInfo);

    // 根据标题查找视频信息
    VideoInfo findVideoInfoByTitle(String title);

    // 分页查询视频信息
    List<VideoInfo> findVideoInfoWithPagination(@Param("offset") int offset, @Param("limit") int limit, @Param("keyword") String keyword);

    // 查询视频总数
    int countVideoInfo(@Param("keyword") String keyword);

    // 根据类型查询视频信息
    List<VideoInfo> findVideoInfoByTypeWithPagination(@Param("offset") int offset, @Param("limit") int limit, @Param("keyword") String keyword);

    // 查询视频总数
    int countVideoInfoByType(@Param("keyword") String keyword);
}
