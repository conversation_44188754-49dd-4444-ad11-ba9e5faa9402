package com.example.pure.mapper.primary;

import com.example.pure.model.dto.VideoEpisodesDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PureVideoEpisodesMapper {
    // 按Param值去映射到对应的collection的值实现遍历
int insertVideoEpisodes(@Param("videoEpisodesList") List<VideoEpisodesDTO> videoEpisodes);

    /**
     * 根据视频信息ID查找其所有的分集信息。
     * <p>
     * 经过重构，此方法现在通过一次JOIN查询，同时获取每个分集的点赞总数，
     * 以及当前登录用户（如果存在）对每个分集的点赞状态。
     *
     * @param videoInfoId 视频信息的ID
     * @param userId      当前登录用户的ID (可以为null，用于查询点赞状态)
     * @return 包含完整信息（包括点赞数和个人点赞状态）的分集DTO列表
     */
List<VideoEpisodesDTO> findVideoEpisodesByVideoInfoId(@Param("videoInfoId") Long videoInfoId, @Param("userId") Long userId);

int deleteVideoEpisodesByVideoInfoId(Long videoInfoId);

/**
 * 更新指定视频分集的雪碧图对象键
 * @param videoInfoId 视频ID
 * @param episodeNumber 分集编号
 * @param spriteSheetObjectKey 雪碧图对象键
 * @return 更新的行数
 */
int updateSpriteSheetObjectKey(@Param("videoInfoId") Long videoInfoId,
                              @Param("episodeNumber") String episodeNumber,
                              @Param("spriteSheetObjectKey") String spriteSheetObjectKey);

/**
 * 更新指定视频分集的对象键
 * @param videoInfoId 视频ID
 * @param episodeNumber 分集编号
 * @param objectKey 视频文件对象键
 * @return 更新的行数
 */
int updateEpisodeObjectKey(@Param("videoInfoId") Long videoInfoId,
                          @Param("episodeNumber") String episodeNumber,
                          @Param("objectKey") String objectKey);
}
