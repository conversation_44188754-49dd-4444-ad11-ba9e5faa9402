package com.example.pure.mapper.primary;

import com.example.pure.model.entity.UserProfile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserProfileMapper {
    // 根据用户ID查找详细信息
    UserProfile findUserProfileByUserId(@Param("id") Long id);

    UserProfile findUserProfileByUsername(@Param("username") String username);
    /**
     * 根据邮箱查找用户ID
     * @param email 邮箱
     * @return 用户ID，如果不存在返回null
     */
    Long findUserIdByEmail(String email);


    // 创建新用户详细资料 (expects UserProfile object with userId set)
    int createNewUserProfile(UserProfile UserProfile);

    // 根据用户ID更新详细信息
    int updateUserProfileByUserId(UserProfile UserProfile);

    int updateUserProfileByUserUsername(UserProfile UserProfile);

    // 根据用户ID删除详细信息
    int deleteUserProfileByUserId(@Param("id") Long id);
}
