package com.example.pure.mapper.primary;

import com.example.pure.model.dto.CommentLikeData;
import com.example.pure.model.dto.VideoCommentRequest;
import com.example.pure.model.dto.VideoCommentsDTO;
import com.example.pure.model.entity.VideoCommentLikes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频评论点赞/踩的MyBatis Mapper接口
 * <p>
 * 定义了与 `video_comment_likes` 表相关的数据库操作。
 * </p>
 */
@Mapper
public interface VideoCommentInteractionMapper {

    int insertVideoComments(VideoCommentRequest videoCommentRequest);

    List<VideoCommentsDTO> getBasicComments(@Param("offset") int offset, @Param("limit") int limit,
                                            @Param("videoEpisodesId") Long videoEpisodesId);

    List<CommentLikeData>getCommentsLikeData(@Param("commentIds") List<Long> commentsList,@Param("userId") Long userId);

    List<VideoCommentsDTO> getComments(@Param("offset") int offset, @Param("limit") int limit,
                                       @Param("videoEpisodesId") Long videoEpisodesId,@Param("userId") Long userId);

    int deleteComment(@Param("commentId") Long commentId);

    Long getUserIdByCommentId(@Param("commentId") Long commentId);


    /**
     * 根据评论ID和用户ID查找唯一的点赞/踩记录。
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 存在则返回 {@link VideoCommentLikes} 对象，否则返回 null
     */
    VideoCommentLikes findByCommentIdAndUserId(@Param("commentId") Long commentId, @Param("userId") Long userId);

    /**
     * 插入一条新的点赞/踩记录。
     *
     * @param like 要插入的记录对象
     * @return 影响的行数
     */
    int insert(VideoCommentLikes like);

    /**
     * 更新已有的点赞/踩记录（例如，切换状态或类型）。
     *
     * @param like 要更新的记录对象
     * @return 影响的行数
     */
    int update(VideoCommentLikes like);

    /**
     * 统计指定评论下特定类型且状态为激活的记录数。
     *
     * @param commentId 评论ID
     * @param type      操作类型 ("like" 或 "dislike")
     * @param status    状态 (通常为 true，表示激活)
     * @return 符合条件的记录总数
     */
    long countByCommentIdAndTypeAndStatus(@Param("commentId") Long commentId, @Param("type") String type, @Param("status") boolean status);
}
