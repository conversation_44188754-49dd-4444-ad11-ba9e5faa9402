package com.example.pure.mapper.primary;


import com.example.pure.model.entity.OperatingLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OperatingLogMapper {

    /**
     * 插入操作日志
     * @param operatingLog 操作日志对象
     * @return 影响的行数
     */
    int insertOperatingLog(OperatingLog operatingLog);

    /**
     * 根据用户ID查询操作日志
     * @param userId 用户ID
     * @return 操作日志列表
     */
    List<OperatingLog> findOperatingLogsByUserId(Long userId,@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取特定用户的操作日志数量
     * @param userId 用户ID
     * @return 操作日志数量
     */
    int countOperatingLogsByUserId(Long userId);
}
