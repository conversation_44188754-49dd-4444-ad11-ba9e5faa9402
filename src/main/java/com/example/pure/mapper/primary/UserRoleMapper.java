package com.example.pure.mapper.primary;

import com.example.pure.model.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserRoleMapper {

    /**
     * 为用户分配角色
     */
    int assignRoleToUser(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 获取用户的所有角色
     */
    List<Role> findRolesByUserId(Long userId);

    /**
     * 移除用户的角色
     */
    void removeRoleFromUser(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 获取具有指定角色的所有用户ID
     */
    List<Long> findUserIdsByRoleId(Long roleId);
}
