package com.example.pure.annotation;


import com.example.pure.serializer.GenericDataMaskingSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据脱敏注解
 * <p>
 * 用于标记需要在JSON序列化时进行脱敏处理的字段。
 * 必须与 {@link JsonSerialize} 注解一起使用，指定使用 {@link GenericDataMaskingSerializer} 序列化器。
 * </p>
 * <p>
 * 使用示例:
 * <pre>
 * {@code
 * @JsonSerialize(using = GenericDataMaskingSerializer.class)
 * @DataMasking(strategy = MaskingStrategy.USERNAME)
 * private String username;
 * }
 * </pre>
 * </p>
 */
@Target({ElementType.FIELD}) // 注解可以应用到字段上
@Retention(RetentionPolicy.RUNTIME) // 在运行时保留注解信息
// 使用注解为@DataMasking
public @interface DataMasking {
    /**
     * 指定要应用的脱敏策略
     *
     * @return 脱敏策略，默认为 {@link MaskingStrategy#DEFAULT}
     */
    MaskingStrategy strategy(); // strategy(): strategy 是属性的 名称。 () 类似于方法定义，但在注解中，它表示一个属性的声明。

}
