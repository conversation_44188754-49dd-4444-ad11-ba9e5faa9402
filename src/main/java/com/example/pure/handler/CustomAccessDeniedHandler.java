package com.example.pure.handler;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 自定义访问拒绝处理器
 * 处理已认证用户访问无权限资源时的响应
 * 例如：普通用户访问管理员接口时，返回403错误
 */
@Slf4j  // 使用Lombok提供的日志注解
@Component  // 注册为Spring组件
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    // 用于JSON序列化的ObjectMapper，通过构造器注入
    private final ObjectMapper objectMapper;

    // 构造器注入ObjectMapper
    public CustomAccessDeniedHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 处理访问拒绝异常
     * 当已认证用户访问无权限的资源时会调用此方法
     *
     * @param request 当前HTTP请求
     * @param response HTTP响应
     * @param accessDeniedException 访问拒绝异常信息
     */
    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                      AccessDeniedException accessDeniedException) throws IOException, ServletException {
        // 记录访问被拒绝的日志信息
        log.warn("访问被拒绝: {}, URI: {}", request.getRequestURI(), accessDeniedException.getMessage());

        // 设置响应状态码为403（禁止访问）
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        // 设置响应编码
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        // 设置响应类型为JSON
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        // 构建错误响应对象,<?>泛型返回任何类型
        Result<?> result = Result.error(
            ResponseCode.FORBIDDEN,
            "您没有权限访问此资源"
        );

        // 将错误响应写入响应体，writeValueAsString将传入的对象序列化成Json格式的string
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
