package com.example.pure.handler;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import com.example.pure.exception.JwtAuthenticationException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 自定义认证入口点
 * 处理未认证用户访问需要认证的资源时的响应
 * 例如：未登录用户访问需要登录的接口时，返回401错误
 */
@Slf4j
@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    public CustomAuthenticationEntryPoint(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 处理认证异常
     * 当未认证用户访问需要认证的资源时会调用此方法
     *
     * @param request 当前HTTP请求
     * @param response HTTP响应
     * @param authException 认证异常信息
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        // 记录认证失败的日志信息
        log.warn("认证失败: {}, URI: {}", authException.getMessage(), request.getRequestURI());

        // 设置响应状态码为401（未认证）
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        // 设置响应编码
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        // 设置响应类型为JSON
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        Result<?> result;
        // 判断是否为JWT相关的认证异常
        if (authException instanceof JwtAuthenticationException) {
            // 如果是，使用异常中自带的消息
            result = Result.error(ResponseCode.UNAUTHORIZED, authException.getMessage());
        } else {
            // 对于其他认证异常（如坏的凭证），返回通用消息
            result = Result.error(
                ResponseCode.UNAUTHORIZED,
                "认证失败，请检查您的凭证或Token"
            );
        }

        // 将错误响应写入响应体
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
