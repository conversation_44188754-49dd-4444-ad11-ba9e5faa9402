package com.example.pure.util;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
public class CryptoUtil {
    private final PasswordEncoder encoder;


    public CryptoUtil(PasswordEncoder encoder) {
        this.encoder = encoder;
    }

    public String passwordEncrypt(String password){
        return encoder.encode(password);
    }

    public Boolean passwordMatch(String rawPassword, String encodedPassword){
          return encoder.matches(rawPassword, encodedPassword);

    }
}
