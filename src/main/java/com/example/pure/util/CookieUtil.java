package com.example.pure.util;

import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Optional;

/**
 * Cookie工具类
 * 用于处理JWT相关的Cookie操作
 */
@Component
public class CookieUtil {

    private static final String ACCESS_TOKEN_COOKIE_NAME = "accessToken";
    private static final String REFRESH_TOKEN_COOKIE_NAME = "refreshToken";
    private static final int ACCESS_TOKEN_MAX_AGE = 604800; // 1天，单位：秒
    private static final int REFRESH_TOKEN_MAX_AGE = 604800; // 7天，单位：秒
    private static final String COOKIE_PATH = "/";
    private static final boolean HTTP_ONLY = false;  // 防止客户端 JavaScript 读取该 cookie，有助于缓解 XSS 攻击
    private static final boolean SECURE = false;    // 生产环境应设为true，要求使用HTTPS
    private static final String SAME_SITE = "Lax"; // Strict, Lax, None

    /**
     * 添加JWT令牌到Cookie
     *
     * @param response HTTP响应对象
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     */
    public void addTokenCookies(HttpServletResponse response, String accessToken, String refreshToken) {
        try {
            // 移除Bearer前缀并进行URL编码，避免空格和特殊字符问题
            String encodedAccessToken = encodeTokenForCookie(accessToken);
            String encodedRefreshToken = encodeTokenForCookie(refreshToken);

            // 创建访问令牌Cookie
            ResponseCookie accessTokenCookie = ResponseCookie.from(ACCESS_TOKEN_COOKIE_NAME, encodedAccessToken)
                    .maxAge(ACCESS_TOKEN_MAX_AGE)
                    .httpOnly(HTTP_ONLY)
                    .secure(SECURE)
                    .path(COOKIE_PATH)
                    .sameSite(SAME_SITE)
                    .build();

            // 创建刷新令牌Cookie
            ResponseCookie refreshTokenCookie = ResponseCookie.from(REFRESH_TOKEN_COOKIE_NAME, encodedRefreshToken)
                    .maxAge(REFRESH_TOKEN_MAX_AGE)
                    .httpOnly(HTTP_ONLY)
                    .secure(SECURE)
                    .path(COOKIE_PATH)
                    .sameSite(SAME_SITE)
                    .build();

            // 添加Cookie到响应头
            response.addHeader("Set-Cookie", accessTokenCookie.toString());
            response.addHeader("Set-Cookie", refreshTokenCookie.toString());
        } catch (Exception e) {
            // 记录异常但不中断流程
            System.err.println("添加Cookie失败: " + e.getMessage());
        }
    }

    /**
     * 从请求中获取访问令牌
     *
     * @param request HTTP请求对象
     * @return 访问令牌，如果不存在则返回null
     */
    public String getAccessTokenFromCookies(HttpServletRequest request) {
        String cookieValue = getCookieValue(request, ACCESS_TOKEN_COOKIE_NAME);
        return decodeTokenFromCookie(cookieValue);
    }

    /**
     * 从请求中获取刷新令牌
     *
     * @param request HTTP请求对象
     * @return 刷新令牌，如果不存在则返回null
     */
    public String getRefreshTokenFromCookies(HttpServletRequest request) {
        String cookieValue = getCookieValue(request, REFRESH_TOKEN_COOKIE_NAME);
        return decodeTokenFromCookie(cookieValue);
    }

    /**
     * 清除JWT相关的Cookie
     *
     * @param response HTTP响应对象
     */
    public void clearTokenCookies(HttpServletResponse response) {
        // 创建过期的访问令牌Cookie
        ResponseCookie accessTokenCookie = ResponseCookie.from(ACCESS_TOKEN_COOKIE_NAME, "")
                .maxAge(0)
                .httpOnly(HTTP_ONLY)
                .secure(SECURE)
                .path(COOKIE_PATH)
                .sameSite(SAME_SITE)
                .build();

        // 创建过期的刷新令牌Cookie
        ResponseCookie refreshTokenCookie = ResponseCookie.from(REFRESH_TOKEN_COOKIE_NAME, "")
                .maxAge(0)
                .httpOnly(HTTP_ONLY)
                .secure(SECURE)
                .path(COOKIE_PATH)
                .sameSite(SAME_SITE)
                .build();

        // 添加Cookie到响应头
        response.addHeader("Set-Cookie", accessTokenCookie.toString());
        response.addHeader("Set-Cookie", refreshTokenCookie.toString());
    }

    /**
     * 从请求中获取指定名称的Cookie值
     *
     * @param request HTTP请求对象
     * @param name Cookie名称
     * @return Cookie值，如果不存在则返回null
     */
    private String getCookieValue(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();

        if (cookies != null) {
            Optional<Cookie> cookieOpt = Arrays.stream(cookies)
                    .filter(c -> name.equals(c.getName()))
                    .findFirst();

            if (cookieOpt.isPresent()) {
                return cookieOpt.get().getValue();
            }
        }

        return null;
    }

    /**
     * 为Cookie编码令牌，确保符合RFC2616规范
     *
     * @param token JWT令牌
     * @return 编码后的令牌
     */
    private String encodeTokenForCookie(String token) {
        if (token == null) {
            return "";
        }

        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        // URL编码确保没有非法字符
        try {
            return URLEncoder.encode(token, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            return token.replace(" ", "");  // 简单替换空格作为备选方案
        }
    }

    /**
     * 从Cookie值解码令牌
     *
     * @param cookieValue Cookie中存储的编码后令牌
     * @return 解码后的原始令牌
     */
    private String decodeTokenFromCookie(String cookieValue) {
        if (!StringUtils.hasText(cookieValue)) {
            return null;
        }

        try {
            return URLDecoder.decode(cookieValue, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            return cookieValue;  // 如果解码失败，返回原值
        }
    }
}
