package com.example.pure.util;

import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
public class HashingUtil {
    private final PasswordEncoder encoder;

    // @Lazy 注解可以告诉 Spring：“不要在应用启动时就立即创建这个 Bean，等到第一次实际使用它的时候再创建”。这可以有效地打破启动时的依赖循环。
    public HashingUtil(@Lazy PasswordEncoder encoder) {
        this.encoder = encoder;
    }

    public String passwordEncoder(String password){
        return encoder.encode(password);
    }

    public Boolean passwordMatch(String rawPassword, String encodedPassword){
        return encoder.matches(rawPassword, encodedPassword);

    }
}
