package com.example.pure.util;

import com.example.pure.exception.BusinessException;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Component
public class HttpUtil {


    /**
     * 获取当前HTTP请求
     * @return 当前HTTP请求
     */
    public HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (!ObjectUtils.isEmpty(attributes)) {
            return attributes.getRequest();
        }
        throw new BusinessException("无法获取当前请求信息");
    }
}
