package com.example.pure.util;

import com.example.pure.constant.SecurityConstants;
import org.springframework.stereotype.Component;

import java.util.Random;

/**
 * 随机数工具类
 * 用于生成验证码等随机字符串
 */
@Component
public class RandomUtil {


    public String generateRandemName(int length){
        StringBuilder randemName= new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++){
            int index = random.nextInt(SecurityConstants.RANDEM.length());
            randemName.append(SecurityConstants.RANDEM.charAt(index));
        }
        return randemName.toString();
    }





    /**
     * 生成指定长度的验证码（字母和数字组合）
     */
    public String generateVerificationCode(int length) {
        StringBuilder verificationCode = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            // 生成0-35的随机整数
            int index = random.nextInt(SecurityConstants.CHARACTERS.length());
            // 用获取到的随机整数当作索引从字符串获取字符来拼接
            verificationCode.append(SecurityConstants.CHARACTERS.charAt(index));
        }

        return verificationCode.toString();
    }
}
