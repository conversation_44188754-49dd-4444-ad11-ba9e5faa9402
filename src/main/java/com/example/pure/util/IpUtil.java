package com.example.pure.util;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@Component
public class IpUtil {

    private static final String IP_CACHE_PREFIX = "ip_limit:";  // IP缓存前缀
    private static final long IP_CACHE_DURATION = 30;  // 缓存时间（分钟）
    private final RedisTemplate<String, String> redisTemplate;
    public IpUtil(
            RedisTemplate<String, String> redisTemplate
    ) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取客户端真实IP地址
     * 按优先级依次从不同 header 中获取，支持多级代理
     * 优先从缓存获取，缓存不存在则从请求头获取并缓存
     *
     * @param request HTTP请求对象
     * @return 客户端真实IP地址
     */
    public String getClientIp(HttpServletRequest request) {
        // 先尝试从请求头获取IP
        String ip = extractIpFromRequest(request);

        // 转换IPv6的localhost为IPv4格式
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }

        // 使用IP本身作为缓存键的一部分
        String cacheKey = IP_CACHE_PREFIX + ip;

        // 检查是否已经缓存了这个IP
        String cachedIp = redisTemplate.opsForValue().get(cacheKey);
        if (cachedIp != null) {
            return cachedIp;
        }

        // 缓存IP地址
        redisTemplate.opsForValue().set(cacheKey, ip, IP_CACHE_DURATION, TimeUnit.MINUTES);

        return ip;
    }

    /**
     * 从请求头提取IP地址
     */
    private String extractIpFromRequest(HttpServletRequest request) {
        // 可能包含客户端IP的请求头，按优先级排序
        String[] headers = {
                "X-Forwarded-For",    // 优先使用，适用于大多数代理服务器
                "X-Real-IP",          // Nginx代理一般会添加此头
                "Proxy-Client-IP",    // Apache 代理服务器
                "WL-Proxy-Client-IP", // WebLogic 代理
                "HTTP_CLIENT_IP",     // 一些代理服务器
                "HTTP_X_FORWARDED_FOR" // 兼容性考虑
        };

        String ip;
        for (String header : headers) {
            ip = request.getHeader(header);
            if (isValidIp(ip)) {
                // 处理多IP场景，取第一个非unknown的IP
                if (ip.contains(",")) {
                    //如果IP数组里包含,按,分成数组
                    String[] ips = ip.split(",");
                    for (String subIp : ips) {
                        //去除前后的空格，防止服务器额外添加空格
                        subIp = subIp.trim();
                        if (isValidIp(subIp)) {
                            //返回第一个ip,在代理链中，第一个ip是客户端真实ip，后面的ip是各级代理服务器ip
                            return subIp;
                        }
                    }
                }
                //ip有效返回ip跳出extractIpFromRequest方法返回ip值
                return ip;
            }
        }

        // 如果所有header都没有获取到有效IP，则使用远程地址
        return request.getRemoteAddr();
    }

    /**
     * 验证IP地址是否有效，并进行格式验证
     *
     * @param ip IP地址
     * @return 如果IP有效返回true，否则返回false
     */
    private boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty() ||
                //比较字符串，返回布尔值
                ip.equalsIgnoreCase("unknown")) {
            return false;
        }

        // 本地开发环境,接受localhost的IPv4和IPv6地址
        if (ip.equals("127.0.0.1") ||
                ip.equals("0:0:0:0:0:0:0:1")) {
            return true;  // 允许本地测试地址
        }

        // IPv4格式验证
        String ipRegex = "^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                "([01]?\\d\\d?|2[0-4]\\d|25[0-5])$";
        return ip.matches(ipRegex);
    }
}
