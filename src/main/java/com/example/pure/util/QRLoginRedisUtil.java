package com.example.pure.util;

import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 二维码登录Redis工具类
 * <p>
 * 使用Redis存储二维码登录相关信息，相比内存存储有以下优势：
 * 1. 支持分布式部署：多实例间可共享二维码状态
 * 2. 数据持久性：服务重启后二维码状态不会丢失
 * 3. 自动过期：利用Redis过期机制自动处理过期的二维码
 * 4. 减少内存占用：二维码信息存储在Redis中而非应用内存
 * </p>
 *
 * <p>
 * 二维码登录流程：
 * 1. 客户端请求创建二维码，服务端生成唯一ID并返回
 * 2. 客户端展示二维码，包含唯一ID
 * 3. 移动端扫描二维码，获取唯一ID，发送到服务端验证
 * 4. 服务端更新二维码状态为"已扫描"
 * 5. 客户端通过定时轮询或WebSocket获取状态更新
 * 6. 移动端确认登录，服务端更新状态为"已确认"
 * 7. 客户端检测到"已确认"状态，自动登录
 * </p>
 */
@Slf4j
@Component
public class QRLoginRedisUtil {

    /**
     * 二维码状态枚举
     * <p>
     * 定义二维码在整个登录流程中可能的状态：
     * </p>
     */
    public enum QRCodeStatus {
        /**
         * 等待扫描 - 初始状态，二维码已创建但未被扫描
         */
        PENDING,
        /**
         * 已扫描，等待确认 - 移动端已扫描二维码，但用户尚未确认登录
         */
        SCANNED,
        /**
         * 已确认登录 - 用户在移动端已确认登录，Web端可以执行登录流程
         */
        CONFIRMED,
        /**
         * 已过期 - 二维码已经超过有效期，需要重新生成
         */
        EXPIRED,
        /**
         * 已取消 - 用户在移动端已取消登录
         */
        CANCELED,

        NOT_FOUND
    }

    /**
     * 二维码信息类
     * <p>
     * 封装二维码登录相关的所有信息，包括状态、用户信息、时间等。
     * 此类的实例会被序列化为JSON存储在Redis中。
     * </p>
     */
    @JsonIgnoreProperties(ignoreUnknown = true) // 由于isExpired()序列化把结果名为expired参数包含到JSON中，反序列化java类没有这个参数，所以需要忽略未知属性
    public static class QRCodeInfo {
        /**
         * 二维码唯一标识
         * 使用UUID生成，确保唯一性
         */
        private String qrId;

        /**
         * 二维码创建时间
         * 用于计算二维码是否过期
         */
        private LocalDateTime createdTime;

        /**
         * 二维码过期时间
         * 超过此时间二维码将失效
         */
        private LocalDateTime expireTime;

        /**
         * 二维码当前状态
         * @see QRCodeStatus
         */
        private QRCodeStatus status;

        /**
         * 扫描用户信息
         * 当移动端扫描二维码后，会将用户信息存储在此字段
         */
        private UserDTO userInfo;

        private boolean expired ;

        /**
         * 无参构造函数
         * 用于Jackson JSON反序列化
         */
        public QRCodeInfo() {
        }

        /**
         * 构造二维码信息
         * 创建新的二维码信息对象，设置初始状态和过期时间
         *
         * @param qrId 二维码唯一标识
         * @param expirationSeconds 过期时间（秒）
         */
        public QRCodeInfo(String qrId, int expirationSeconds) {
            this.qrId = qrId;
            this.createdTime = LocalDateTime.now();
            this.expireTime = createdTime.plusSeconds(expirationSeconds);
            this.status = QRCodeStatus.PENDING;
            this.userInfo = null;
        }

        // Getters and Setters
        public String getQrId() {
            return qrId;
        }

        public void setQrId(String qrId) {
            this.qrId = qrId;
        }

        public LocalDateTime getCreateTime() {
            return createdTime;
        }

        public void setCreatedTime(LocalDateTime createdTime) {
            this.createdTime = createdTime;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(LocalDateTime expireTime) {
            this.expireTime = expireTime;
        }

        public QRCodeStatus getStatus() {
            return status;
        }

        public void setStatus(QRCodeStatus status) {
            this.status = status;
        }

        public UserDTO getUserInfo() {
            return userInfo;
        }

        public void setUserInfo(UserDTO userInfo) {
            this.userInfo = userInfo;
        }

        /**
         * 检查二维码是否过期
         * 比较当前时间与过期时间
         *
         * @return 如果当前时间晚于过期时间，返回true；否则返回false
         */
        public boolean isExpired() {
            return expired=LocalDateTime.now().isAfter(expireTime);
        }

        public void setExpired(boolean expired) {
            this.expired = expired;
        }
    }

    /**
     * Redis操作工具类
     * 用于读写Redis数据
     */
    private final RedisUtil redisUtil;

    /**
     * JSON序列化/反序列化工具
     * 用于在Java对象和JSON字符串之间转换
     */
    private final ObjectMapper objectMapper;

    /**
     * Redis键前缀
     * 用于区分不同类型的数据，方便管理和查询
     */
    private static final String REDIS_KEY_PREFIX = "qrlogin:";

    /**
     * 二维码在Redis中的过期时间（秒）
     * 从配置文件中注入，默认300秒（5分钟）
     * 此时间应长于二维码展示的有效期，以便于查询过期状态
     */
    @Value("${qrlogin.redis.expire-seconds:300}")
    private int qrCodeExpireSeconds;

    /**
     * 二维码展示的有效期（秒）
     * 从配置文件中注入，默认120秒（2分钟）
     * 此时间是二维码实际可用的时间
     */
    @Value("${qrlogin.timeout.display:120}")
    private int qrCodeDisplaySeconds;

    /**
     * 构造函数
     * 注入依赖并初始化JSON序列化工具
     *
     * @param redisUtil Redis操作工具
     */
    @Autowired
    public QRLoginRedisUtil(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
        // 初始化ObjectMapper，用于JSON序列化和反序列化
        this.objectMapper = new ObjectMapper();
        // 注册Java 8时间模块，支持LocalDateTime的序列化与反序列化
        this.objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * 生成二维码唯一标识
     * <p>
     * 创建新的二维码登录会话：
     * 1. 生成唯一的UUID作为二维码ID
     * 2. 创建初始状态的二维码信息对象
     * 3. 将信息存储到Redis中
     * </p>
     *
     * @return 生成的二维码唯一标识
     */
    public String generateQRCodeId() {
        // 使用UUID生成唯一标识
        String qrId = UUID.randomUUID().toString();
        // 创建二维码信息对象，设置过期时间为配置的展示有效期
        QRCodeInfo qrInfo = new QRCodeInfo(qrId, qrCodeDisplaySeconds);
        // 保存到Redis
        saveQRCodeInfo(qrInfo);
        return qrId;
    }

    /**
     * 保存二维码信息到Redis
     * <p>
     * 将二维码信息序列化为JSON并存储到Redis，设置过期时间。
     * 如果序列化过程出错，会记录日志但不会抛出异常。
     * </p>
     *
     * @param qrInfo 要保存的二维码信息对象
     */
    private void saveQRCodeInfo(QRCodeInfo qrInfo) {
        // 构造Redis键
        String key = REDIS_KEY_PREFIX + qrInfo.getQrId();
        // 直接将 QRCodeInfo 对象传递给 redisUtil.set
        // RedisTemplate 配置的 GenericJackson2JsonRedisSerializer 会负责将其序列化为JSON
        redisUtil.set(key, qrInfo, qrCodeExpireSeconds);
        // 注意：原有的 try-catch (JsonProcessingException e) 是针对 objectMapper.writeValueAsString 的。
        // 如果 redisUtil.set() 或底层的 RedisTemplate 在序列化时抛出需要在此处处理的检查型异常，
        // 则需要相应的 try-catch。通常，Spring RedisTemplate 将此类异常包装为运行时异常。
    }

    /**
     * 获取二维码信息
     * <p>
     * 从Redis中获取二维码信息，并检查是否过期：
     * 1. 根据二维码ID构造Redis键
     * 2. 从Redis获取对象 (RedisTemplate 应已完成反序列化)
     * 3. 检查对象类型并转换为 QRCodeInfo
     * 4. 检查是否过期，如果过期则更新状态
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 二维码信息对象，如果不存在或转换失败则返回null
     */
    public QRCodeInfo getQRCodeInfo(String qrId) {
        // 构造Redis键
        String key = REDIS_KEY_PREFIX + qrId;
        // 从Redis获取值，由于RedisTemplate配置了GenericJackson2JsonRedisSerializer并启用了默认类型处理，
        // 返回的Object理论上应该是QRCodeInfo的实例
        Object deserializedObject = redisUtil.get(key);

        // 如果值不存在，返回null
        if (deserializedObject == null) {
            return null;
        }

        QRCodeInfo qrInfo;

        if (deserializedObject instanceof QRCodeInfo) {
            qrInfo = (QRCodeInfo) deserializedObject;
        } else {
            // 如果获取到的对象不是 QRCodeInfo 实例，可能原因：
            // 1. Redis中存储的并非预期的 QRCodeInfo 对象。
            // 2. Jackson 的默认类型处理未完全按预期工作，可能返回了一个 Map。
            // 3. Redis中可能存在旧格式的数据（手动序列化的JSON字符串）。
            log.warn("从Redis获取的对象不是预期的QRCodeInfo实例, qrId={}, 实际类型={}. 将尝试转换...",
                    qrId, deserializedObject.getClass().getName());
            try {
                // 尝试使用ObjectMapper进行转换，它可以处理 Map -> POJO，或者 String (JSON) -> POJO
                if (deserializedObject instanceof String) {
                    // 如果是字符串，按旧方式（或明确的JSON字符串）解析
                    qrInfo = objectMapper.readValue((String) deserializedObject, QRCodeInfo.class);
                } else {
                    // 否则，尝试通用的 convertValue，适合 Map -> POJO 等情况
                    qrInfo = objectMapper.convertValue(deserializedObject, QRCodeInfo.class);
                }
            } catch (Exception e) { // 包括 JsonProcessingException, IllegalArgumentException 等
                log.error("无法将从Redis获取的数据转换为QRCodeInfo: qrId={}, data='{}', error='{}'",
                        qrId, deserializedObject.toString().substring(0, Math.min(deserializedObject.toString().length(), 200)), e.getMessage(), e); // 限制日志中原始数据的长度
                return null;
            }
        }
        
        if (qrInfo == null) { // 如果转换失败或未能得到对象
             log.error("QRCodeInfo对象在转换后仍为null, qrId={}", qrId);
            return null;
        }


        // 检查二维码是否过期，如果过期且状态不是EXPIRED，则更新状态为过期
        // 注意：saveQRCodeInfo 此时会使用新的直接对象持久化方式
        if (qrInfo.isExpired() && qrInfo.getStatus() != QRCodeStatus.EXPIRED) {
            qrInfo.setStatus(QRCodeStatus.EXPIRED);
            // 保存更新后的状态到Redis
            saveQRCodeInfo(qrInfo);
        }

        return qrInfo;
    }

    /**
     * 更新二维码状态为已扫描
     * <p>
     * 当移动端扫描二维码后调用此方法：
     * 1. 获取二维码信息
     * 2. 检查二维码是否存在且未过期
     * 3. 更新状态为SCANNED，关联用户信息
     * 4. 保存更新后的信息到Redis
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @param userInfo 扫描用户的信息
     * @return 是否成功更新状态
     */
    public boolean scanQRCode(String qrId, UserDTO userInfo) {
        // 获取二维码信息
        QRCodeInfo qrInfo = getQRCodeInfo(qrId);
        // 检查二维码是否存在且未过期
        if (qrInfo == null || qrInfo.isExpired()) {
            return false;
        }

        // 更新状态为已扫描
        qrInfo.setStatus(QRCodeStatus.SCANNED);
        // 关联用户信息
        qrInfo.setUserInfo(userInfo);
        // 保存更新后的信息
        saveQRCodeInfo(qrInfo);
        return true;
    }

    /**
     * 确认二维码登录
     * <p>
     * 当用户在移动端确认登录后调用此方法：
     * 1. 获取二维码信息
     * 2. 检查二维码是否存在、未过期、且状态为已扫描
     * 3. 更新状态为CONFIRMED
     * 4. 保存更新后的信息到Redis
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 是否成功确认登录
     */
    public boolean confirmQRCodeLogin(String qrId) {
        // 获取二维码信息
        QRCodeInfo qrInfo = getQRCodeInfo(qrId);
        // 检查二维码是否存在、未过期、且状态为已扫描
        if (qrInfo == null || qrInfo.isExpired() || qrInfo.getStatus() != QRCodeStatus.SCANNED) {
            return false;
        }

        // 更新状态为已确认
        qrInfo.setStatus(QRCodeStatus.CONFIRMED);
        // 保存更新后的信息
        saveQRCodeInfo(qrInfo);
        return true;
    }

    /**
     * 取消二维码登录
     * <p>
     * 当用户在移动端取消登录后调用此方法：
     * 1. 获取二维码信息
     * 2. 检查二维码是否存在
     * 3. 更新状态为CANCELED
     * 4. 保存更新后的信息到Redis
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 是否成功取消登录
     */
    public boolean cancelQRCodeLogin(String qrId) {
        // 获取二维码信息
        QRCodeInfo qrInfo = getQRCodeInfo(qrId);
        // 检查二维码是否存在
        if (qrInfo == null) {
            return false;
        }

        // 更新状态为已取消
        qrInfo.setStatus(QRCodeStatus.CANCELED);
        // 保存更新后的信息
        saveQRCodeInfo(qrInfo);
        return true;
    }
    public boolean checkDuplicateVerificationRequest(String qrId,QRCodeStatus targetStatus) {
        QRCodeInfo qrInfo=getQRCodeInfo(qrId);

        if (qrInfo == null) { // 增加空值检查，更健壮
           throw new BusinessException("获取二维码信息失败"); // 或者抛出异常，取决于你的业务需求
        }

        switch (targetStatus){
            case SCANNED:
                if(qrInfo.getStatus() == QRCodeStatus.SCANNED) {
                    return true;
                }
                break;
            case CONFIRMED:
                if(qrInfo.getStatus() == QRCodeStatus.CONFIRMED) {
                    return true;
                }
                break;
            case EXPIRED:
                if(qrInfo.getStatus() == QRCodeStatus.EXPIRED) {
                    return true;
                }
                break;
            default:
                return false;


        }
        return false; // 如果所有 case 都不匹配 (并且没有 return true)，则返回 false
    }

    /**
     * 移除二维码信息
     * <p>
     * 在不再需要二维码信息时调用此方法，从Redis中删除：
     * 1. 构造Redis键
     * 2. 从Redis中删除对应的键值对
     * </p>
     * <p>
     * 常见的使用场景：
     * - 登录成功后清理二维码信息
     * - 手动取消二维码登录
     * - 二维码过期后的清理工作
     * </p>
     *
     * @param qrId 二维码唯一标识
     */
    public void removeQRCode(String qrId) {
        // 构造Redis键
        String key = REDIS_KEY_PREFIX + qrId;
        // 从Redis中删除
        redisUtil.delete(key);
    }
}
