package com.example.pure.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成工具类
 *
 * <p>提供生成二维码图片并转换为多种格式（字节数组、Base64字符串）的功能。
 * 使用Google ZXing库实现二维码生成，支持自定义尺寸和纠错级别。</p>
 *
 * <p>主要功能：
 * <ul>
 *   <li>生成二维码图片为字节数组</li>
 *   <li>生成二维码图片并转换为Base64编码字符串</li>
 *   <li>生成带图片前缀的Base64数据URI，可直接在前端使用</li>
 * </ul>
 * </p>
 *
 * <p>用法示例：
 * <pre>{@code
 * // 生成二维码并在Web页面显示
 * String dataUri = qrCodeUtil.generateQRCodeAsDataUri("https://example.com");
 * // HTML中使用： <img src="dataUri" />
 *
 * // 生成二维码字节数据并作为API响应
 * byte[] qrCodeBytes = qrCodeUtil.generateQRCode("Hello World");
 * // 在Spring Controller中： return ResponseEntity.ok().contentType(MediaType.IMAGE_PNG).body(qrCodeBytes);
 * }</pre>
 * </p>
 *
 * <p>此工具类可用于：
 * <ul>
 *   <li>登录验证 - 生成二维码用于扫码登录</li>
 *   <li>数据分享 - 将URL或文本信息编码为二维码</li>
 *   <li>支付场景 - 生成支付二维码</li>
 *   <li>资源链接 - 将资源链接编码为二维码</li>
 * </ul>
 * </p>
 */
@Slf4j
@Component
public class QRCodeUtil {

    /**
     * 默认二维码图片宽度（像素）
     * 300像素宽度在大多数手机上可以正常扫描
     */
    private static final int DEFAULT_WIDTH = 300;

    /**
     * 默认二维码图片高度（像素）
     * 与宽度保持一致，确保二维码为正方形
     */
    private static final int DEFAULT_HEIGHT = 300;

    /**
     * 默认图片格式
     * PNG格式支持透明背景，且为无损压缩，适合二维码图片
     */
    private static final String DEFAULT_FORMAT = "PNG";

    /**
     * 生成二维码图片为字节数组
     * <p>
     * 使用默认尺寸(300x300像素)生成二维码图片。
     * </p>
     *
     * @param content 二维码内容，可以是文本、URL、JSON等字符串内容
     * @return 二维码图片的字节数组，可直接用于HTTP响应
     * @throws WriterException 编码过程中出现错误时抛出
     * @throws IOException 图片处理过程中出现I/O错误时抛出
     */
    public byte[] generateQRCode(String content) throws WriterException, IOException {
        return generateQRCode(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成指定尺寸的二维码图片为字节数组
     * <p>
     * 根据指定的宽度和高度生成二维码。推荐宽高相等，以确保最佳的可扫描性。
     * 使用了H级别的纠错能力，允许二维码有约30%的损坏仍能被正确识别。
     * </p>
     *
     * @param content 二维码内容，可以是文本、URL、JSON等字符串内容
     * @param width 二维码宽度(像素)，建议不小于200像素，确保可扫描性
     * @param height 二维码高度(像素)，建议与宽度相等
     * @return 二维码图片的字节数组，可直接用于HTTP响应
     * @throws WriterException 二维码编码过程中出现错误时抛出
     * @throws IOException 图片转换为字节数组过程中出现I/O错误时抛出
     */
    public byte[] generateQRCode(String content, int width, int height) throws WriterException, IOException {
        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        // 使用H级别纠错，容错率约30%，适合添加logo等装饰
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        // 使用UTF-8编码，支持中文、日文、韩文等各国字符
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        // 设置二维码边距，默认为4，这里设置为2使二维码占据更多空间
        hints.put(EncodeHintType.MARGIN, 1);
        //不设置二维码版本，自动选择最佳版本

        // 生成二维码位矩阵
        // MultiFormatWriter支持多种条码格式，这里指定使用QR码格式
        BitMatrix bitMatrix = new MultiFormatWriter().encode(
            content,
            BarcodeFormat.QR_CODE,
            width,
            height,
            hints
        );

        // 将位矩阵转换为图片
        // MatrixToImageWriter提供位矩阵到图像的转换能力
        BufferedImage qrImage = MatrixToImageWriter.toBufferedImage(bitMatrix);

        // 将图片转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // image 对象以 PNG 格式编码，并将编码后的 PNG 图像数据写入到内存中的输出流outputStream 中
        ImageIO.write(qrImage, DEFAULT_FORMAT, outputStream);
        // 把流转为字节数组
        return outputStream.toByteArray();
    }

    /**
     * 生成二维码并转换为Base64字符串
     * <p>
     * 方便在Web应用中直接使用，无需单独处理图片文件。
     * 使用默认尺寸生成二维码，并将图片转换为Base64编码字符串。
     * </p>
     *
     * @param content 二维码内容，可以是文本、URL等
     * @return Base64编码的二维码图片字符串，失败返回null
     */
    public String generateQRCodeAsBase64(String content) {
        try {
            byte[] qrCodeBytes = generateQRCode(content);
            return Base64.getEncoder().encodeToString(qrCodeBytes);
        } catch (Exception e) {
            log.error("生成二维码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成二维码并转换为带图片前缀的Base64字符串(Data URI)
     * <p>
     * 生成完整的Data URI格式的二维码，可以直接在HTML的img标签的src属性中使用。
     * 格式为：data:image/png;base64,xxxxx
     * </p>
     * <p>
     * 使用示例：
     * <pre>{@code
     * <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhE..." alt="QR Code" />
     * }</pre>
     * </p>
     *
     * @param content 二维码内容，可以是文本、URL等
     * @return 带图片前缀的Base64编码二维码Data URI，失败返回null
     */
    public String generateQRCodeAsDataUri(String content) {
        String base64QR = generateQRCodeAsBase64(content);
        if (base64QR != null) {
            return "data:image/png;base64," + base64QR;
        }
        return null;
    }
}
