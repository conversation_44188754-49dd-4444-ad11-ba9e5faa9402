package com.example.pure.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import javax.crypto.SecretKey;
import com.example.pure.exception.JwtAuthenticationException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 用于生成、验证和解析JWT令牌
 */
@Slf4j  /* Lombok注解，自动生成日志对象 */
@Component  /* Spring注解，标记这个类为组件，会被Spring自动扫描并创建实例 */
public class JwtUtil {

    /* 从application.yml中注入JWT密钥 */
    @Value("${jwt.secret}")
    private String secret;

    /* 从application.yml中注入访问令牌过期时间（秒）*/
    @Value("${jwt.expiration}")
    private Long expiration;

    /* 从application.yml中注入刷新令牌过期时间（秒）*/
    @Value("    ${jwt.refresh-expiration}")
    private Long refreshExpiration;

    /**
     * 获取用于签名JWT的密钥
     * 将配置的密钥字符串转换为字节数组
     * 使用密钥字节数组创建一个HMAC-SHA256算法（HMAC算法+SHA256哈希函数算法）使用的SecretKey对象
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 生成访问令牌
     * 包含用户信息和权限的JWT令牌
     * 创建存储声明的Map
     * 添加用户名到声明中
     * 添加用户权限到声明中
     * 令牌格式：Header(Base64编码).Payload(Base64编码).Signature
     * 使用HMAC-SHA256算法对Header和Payload计算生成Signature，这可以确保令牌不会被篡改(最常见是修改Payload的用户名和过期时间)
     * 如果修改了令牌，进入JWT令牌验证，使用修改后的Header或Payload(Signature还是原来的)使用密钥获得新的Signature与
     * 你Signature进行比较(如果修改了Header和Payload会导致Signature错误)，如果不同说明令牌被篡改了
     * 由于Header和Payload是Base64编码可以被解码,所以要加密或哈希后再存储到数据库
     */
    public String generateToken(UserDetails userDetails) {
        //添加用户名和用户权限信息
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", userDetails.getUsername());
        claims.put("authorities", userDetails.getAuthorities());

        /* 构建JWT令牌
         * 设置声明（payload部分）
         * 设置主题（通常是用户名）
         * 设置令牌签发时间
         * 设置令牌过期时间（当前时间 + 配置的过期时间）
         * 使用HS256算法和密钥签名JWT
         * 生成最终的JWT字符串
         */
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 生成刷新令牌
     * 用于获取新的访问令牌的JWT令牌
     * 构建刷新令牌，结构比访问令牌简单
     * 设置主题（用户名）
     * 设置签发时间
     * 设置过期时间（使用刷新令牌的过期时间配置）
     * 使用密钥和HS256算法签名并生成JWT字符串
     */
    public String generateRefreshToken(UserDetails userDetails) {
        return Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + refreshExpiration * 1000))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 从JWT令牌中提取用户名
     * 创建JWT解析器并解析token
     * 设置签名密钥
     * 构建解析器
     * 解析JWT字符串
     * 获取JWT的payload部分
     * 获取主题（用户名）
     */
    public String getUsernameFromToken(String token) throws JwtException {
        String NewToken=TokenCheck(token); // 检查JwtToken格式;
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(NewToken)
                .getBody()
                .getSubject();
    }

    /**
     * 验证JWT令牌是否有效
     * 尝试解析JWT令牌
     * 设置签名密钥
     * 构建解析器
     * 解析JWT字符串
     * 如果没有抛出异常，则令牌有效
     * 签名验证失败
     * 令牌已过期
     * 不支持的JWT格式
     * JWT字符串为空
     */
    public boolean validateToken(String token) {
        String NewToken=TokenCheck(token); // 检查JwtToken格式;
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(NewToken);
            return true;
        } catch (io.jsonwebtoken.security.SignatureException | MalformedJwtException e) {
            log.warn("JWT签名无效: {}", e.getMessage());
            throw JwtAuthenticationException.tampered();
        } catch (ExpiredJwtException e) {
            log.warn("JWT已过期: {}", e.getMessage());
            throw JwtAuthenticationException.expired();
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT格式: {}", e.getMessage());
            throw new JwtAuthenticationException("不支持的JWT令牌格式");
        } catch (IllegalArgumentException e) {
            log.warn("JWT claims字符串为空: {}", e.getMessage());
            throw new JwtAuthenticationException("JWT claims为空或无效");
        }
    }

    /**
     * 检查JWT令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        String NewToken=TokenCheck(token);
        try {
            /* 解析JWT令牌获取声明 */
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(NewToken)
                    .getBody();
            /* 检查过期时间是否在当前时间之前 */
            return claims.getExpiration().before(new Date());
        } catch (ExpiredJwtException e) {
            /* 捕获过期异常，说明令牌已过期 */
            return true;
        } catch (Exception e) {
            /* 其他异常情况，记录日志并认为令牌已过期 */
            log.warn("Error checking token expiration: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 获取令牌的过期时间
     * @param token JWT令牌
     * @return 令牌的过期时间，如果令牌无效或已过期则返回null
     */
    public Date getTokenExpirationDate(String token) {
    try{
        if(token.startsWith("Bearer")){
        token = token.substring(7);
    }
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        return  claims.getExpiration();
    } catch (ExpiredJwtException e) {
        log.warn("JWT token is expired: {}", e.getMessage());
    }
        return null;
    }

    // 检查令牌起始有没有Bearer开头
    public String TokenCheck(String token) {
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        return token;
    }

    /**
     * 检查令牌是否即将过期
     * @param token JWT令牌
     * @param minutesThreshold 过期阈值（分钟）
     * @return true如果令牌即将在指定分钟数内过期
     */
    public boolean isTokenAboutToExpire(String token, int minutesThreshold) {
        token=TokenCheck(token);
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            Date expirationDate = claims.getExpiration();
            Date thresholdDate = new Date(System.currentTimeMillis() + minutesThreshold * 60 * 1000);

            return expirationDate.before(thresholdDate);
        } catch (Exception e) {
            log.warn("Error checking token expiration threshold: {}", e.getMessage());
            return true;
        }
    }
}
