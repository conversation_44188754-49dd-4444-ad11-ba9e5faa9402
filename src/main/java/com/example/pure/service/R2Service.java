package com.example.pure.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.model.S3Object;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.services.s3.model.Bucket;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.UUID;

/**
 * R2 上传服务
 * <p>
 * 封装了与Cloudflare R2交互的两种核心上传逻辑。
 */
@Service
public class R2Service {

    // 注入后端直传所需的S3Client

    final private S3Client s3Client;

    // 注入生成预签名URL所需的S3Presigner

    final private S3Presigner s3Presigner;

    // 从配置中注入存储桶名称等参数
    @Value("${cloudflare.r2.bucket}")
    private String bucketName;
    @Value("${cloudflare.r2.presign-duration-minutes}")
    private long presignDuration;
    @Value("${cloudflare.r2.public-url}")
    private String publicUrlBase;

    @Autowired
    R2Service(S3Client s3Client, S3Presigner s3Presigner) {
        this.s3Client = s3Client;
        this.s3Presigner = s3Presigner;
    }





    /**
     * 功能一：生成预签名上传URL (客户端直接上传模式)
     * <p>
     * 这是性能最佳、最安全的方式。后端不接触文件内容，只负责授权。
     *
     * @param fileName    客户端提供的原始文件名
     * @param contentType 客户端提供的文件MIME类型 (e.g., "image/jpeg")
     * @return 一个有时效性的、可用于HTTP PUT上传的URL
     */
    public String generatePresignedUrl(String fileName, String contentType) {
        // 1. 创建一个标准的PutObjectRequest，描述将要上传的对象信息
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(generateUniqueKey(fileName)) // 使用唯一键以防止覆盖
                .contentType(contentType)
                .build();

        // 2. 创建一个预签名请求，设定URL的有效期
        PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(presignDuration))
                .putObjectRequest(objectRequest)
                .build();

                // 3. 使用S3Presigner生成URL并返回其字符串形式
                return s3Presigner.presignPutObject(presignRequest).url().toString();
            }

            /**
             * 生成预签名下载URL (客户端临时访问模式)
             * <p>
             * 用于为存储在R2中的私有文件生成临时访问URL，允许客户端在指定时间内直接访问文件。
             * 这对于需要控制访问权限的文件（如付费内容、私人文件等）非常有用。
             *
             * @param objectKey R2中存储的对象键（文件路径）
             * @return 一个有时效性的、可用于HTTP GET访问的URL
             */
            public String generatePresignedGetUrl(String objectKey) {
                // 1. 创建一个GetObjectRequest，描述要访问的对象信息
                GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                        .bucket(bucketName)
                        .key(objectKey)
                        .build();

                // 2. 创建一个预签名请求，设定URL的有效期
                GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                        .signatureDuration(Duration.ofMinutes(presignDuration))
                        .getObjectRequest(getObjectRequest)
                        .build();

                // 3. 使用S3Presigner生成URL并返回其字符串形式
                return s3Presigner.presignGetObject(presignRequest).url().toString();
            }


    /**
     * 功能二：从服务器中转上传文件 (后端处理模式)
     * <p>
     * 适用于文件需要经过后端处理（如加水印、病毒扫描、内容修改）的场景。
     * 最佳实践是使用流(InputStream)来处理，以避免将大文件完全加载到内存中。
     *
     * @param file 从Controller层接收到的MultipartFile对象
     * @return 文件成功上传到R2后的公开访问URL
     * @throws IOException 当从MultipartFile获取输入流失败时抛出
     */
    public String uploadFileFromServer(MultipartFile file, String folderName) throws IOException {
        // 为上传到R2的对象生成一个唯一的键（上传文件名称.类型）
        final String objectKeyName = generateUniqueKey(file.getOriginalFilename());

        // 准备上传请求对象
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(folderName + "/" + objectKeyName)
                .contentType(file.getContentType())
                .build();

        // 按照您的要求，我们使用 try-finally 结构来手动管理资源。
        final InputStream inputStream = file.getInputStream();
        try {
            // 在这里，您可以对inputStream进行包装以实现流式修改。
            // 例如: InputStream modifiedStream = new MyWatermarkingInputStream(inputStream);
            // 然后将 modifiedStream 传递给下面的 RequestBody。
            // 为简单起见，我们直接上传原始流。

            // s3Client.putObject是阻塞方法，它会读取流中的所有数据并完成上传。
            // RequestBody.fromInputStream是最高效的方式，它直接将输入流对接到底层HTTP客户端，
            // 避免了内存中的大量缓冲。
            s3Client.putObject(objectRequest, RequestBody.fromInputStream(inputStream, file.getSize()));
        } finally {
            // finally块确保无论try块中是否发生异常，这个关闭操作都会被执行。
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    // 记录关闭流时发生的次要异常，但不影响主流程
                    // 在生产环境中，这里应该使用日志框架，例如：
                    // log.warn("Failed to close upload input stream", e);
                    e.printStackTrace();
                }
            }
        }

        // 上传成功后，拼接并返回公开可访问的URL
        return publicUrlBase + "/" + folderName + "/" + objectKeyName;
    }

    /**
     * 内部辅助方法：为上传的文件生成一个唯一的键（文件名）
     *
     * @param originalFilename 原始文件名
     * @return "UUID-原始文件名" 格式的唯一键
     */
    private String generateUniqueKey(String originalFilename) {
        return UUID.randomUUID().toString() + "-" + (originalFilename != null ? originalFilename.replaceAll("\\s+", "_") : "file");
    }
    /**
     * 上传视频相关文件（如雪碧图）到指定的UUID和剧集编号路径下
     *
     * @param file 要上传的文件 (MultipartFile)
     * @param uuid 视频级的唯一标识符，作为父文件夹
     * @param episodeNumber 剧集编号，作为文件名（不含扩展名）
     * @return 文件上传后的公开访问URL
     * @throws IOException IO异常
     */
    public String uploadVideoAsset(MultipartFile file, String uuid, String episodeNumber) throws IOException {
        // 1. 从原始文件名中获取文件扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);

        // 2. 构建在R2中存储的对象键 (Object Key)
        // 格式: video/uuid/episodeNumber.extension
        String objectKey = "video/" + uuid + "/" + episodeNumber + extension;

        // 3. 创建S3上传请求
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .contentType(file.getContentType())
                .build();

        // 4. 执行上传 (复用现有逻辑，使用流式上传以提高效率)
        final InputStream inputStream = file.getInputStream();
        try {
            s3Client.putObject(objectRequest, RequestBody.fromInputStream(inputStream, file.getSize()));
        } finally {
            // 确保输入流被关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    // 记录关闭流时发生的次要异常，但不影响主流程
                    e.printStackTrace();
                }
            }
        }

        // 5. 拼接并返回公开可访问的URL
        return publicUrlBase + "/" + objectKey;
    }

    /**
     * 上传视频文件（使用字节数组）到指定的UUID和剧集编号路径下
     *
     * @param videoData 视频文件的字节数组
     * @param uuid 视频级的唯一标识符，作为父文件夹
     * @param episodeNumber 剧集编号，作为文件名（不含扩展名）
     * @param contentType 文件的MIME类型
     * @return 文件上传后的公开访问URL
     * @throws IOException IO异常
     */
    public String uploadVideoAssetFromBytes(byte[] videoData, String uuid, String episodeNumber, String contentType) throws IOException {
        // 1. 构建在R2中存储的对象键 (Object Key)
        // 格式: video/uuid/episodeNumber.mp4 (默认使用mp4扩展名)
        String objectKey = "video/" + uuid + "/" + episodeNumber + ".mp4";

        // 2. 创建S3上传请求
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .contentType(contentType != null ? contentType : "video/mp4")
                .build();

        // 3. 执行上传
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(videoData)) {
            s3Client.putObject(objectRequest, RequestBody.fromInputStream(inputStream, videoData.length));
        }

        // 4. 拼接并返回公开可访问的URL
        return publicUrlBase + "/" + objectKey;
    }

    /**
     * 内部辅助方法：从文件名中提取扩展名
     * @param filename 原始文件名
     * @return 文件扩展名 (e.g., ".jpg")
     */
    private String getFileExtension(String filename) {
        if (filename != null && filename.contains(".")) {
            return filename.substring(filename.lastIndexOf('.'));
        }
        return ""; // 如果没有扩展名，返回空字符串
    }

    /**
     * 列出所有存储桶
     */
    public List<Bucket> listBuckets() {
        try {
            return s3Client.listBuckets().buckets();
        } catch (S3Exception e) {
            throw new RuntimeException("Failed to list buckets: " + e.getMessage(), e);
        }
    }

    /*
     * 列出存储桶中的所有对象
     */
    public List<S3Object> listObjects(String bucketName) {
        try {
            ListObjectsV2Request request = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .build();

            return s3Client.listObjectsV2(request).contents();
        } catch (S3Exception e) {
            throw new RuntimeException("Failed to list objects in bucket " + bucketName + ": " + e.getMessage(), e);
        }
    }
}
