package com.example.pure.service;

import com.example.pure.model.entity.Role;
import com.example.pure.model.dto.UserRoleDTO;

import java.util.List;

/**
 * 用户角色关联服务接口
 * 处理用户和角色之间的关联关系
 */
public interface UserRoleService {

    /**
     * 为用户分配角色
     *
     * @param userRoleDTO 用户角色关联信息
     */
    void assignRole(UserRoleDTO userRoleDTO);

    /**
     * 移除用户的角色
     *
     * @param userRoleDTO 用户角色关联信息
     */
    void removeRole(UserRoleDTO userRoleDTO);

    /**
     * 获取用户的所有角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getUserRoles(Long userId);

    /**
     * 获取具有指定角色的所有用户ID
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> getRoleUserIds(Long roleId);
}
