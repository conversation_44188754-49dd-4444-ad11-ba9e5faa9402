package com.example.pure.service.impl;

import com.example.pure.mapper.primary.UserRoleMapper;
import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.model.entity.Role;
import com.example.pure.model.dto.UserRoleDTO;
import com.example.pure.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户角色关联服务实现类
 */
@Slf4j
@Service
public class UserRoleServiceImpl implements UserRoleService {

    private final UserMapper userMapper;
    private final UserRoleMapper userRoleMapper;

    public UserRoleServiceImpl(UserMapper userMapper, UserRoleMapper userRoleMapper) {
        this.userMapper = userMapper;
        this.userRoleMapper = userRoleMapper;
    }

    @Override
    @Transactional
    public void assignRole(UserRoleDTO userRoleDTO) {
        log.debug("为用户分配角色: userId={}, roleId={}", userRoleDTO.getUserId(), userRoleDTO.getRoleId());
        userRoleMapper.assignRoleToUser(userRoleDTO.getUserId(), userRoleDTO.getRoleId());
    }

    @Override
    @Transactional
    public void removeRole(UserRoleDTO userRoleDTO) {
        log.debug("移除用户的角色: userId={}, roleId={}", userRoleDTO.getUserId(), userRoleDTO.getRoleId());
        // 需要在UserRoleMapper中添加此方法
        userRoleMapper.removeRoleFromUser(userRoleDTO.getUserId(), userRoleDTO.getRoleId());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Role> getUserRoles(Long userId) {
        log.debug("获取用户的所有角色: userId={}", userId);
        return userRoleMapper.findRolesByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Long> getRoleUserIds(Long roleId) {
        log.debug("获取具有指定角色的所有用户ID: roleId={}", roleId);
        // 需要在UserRoleMapper中添加此方法
        return userRoleMapper.findUserIdsByRoleId(roleId);
    }
}
