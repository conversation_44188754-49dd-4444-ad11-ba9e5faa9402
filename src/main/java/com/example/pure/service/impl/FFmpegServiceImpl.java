package com.example.pure.service.impl;

import com.example.pure.exception.BusinessException;
import com.example.pure.service.FFmpegService;
import com.example.pure.service.R2Service;
import com.example.pure.util.FileAsMultipartFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class FFmpegServiceImpl implements FFmpegService {

    private final R2Service r2UploadService;
    private final String spriteBasePath;

    @Autowired
    public FFmpegServiceImpl(R2Service r2UploadService, @Value("${sprite.generation.path}") String spriteBasePath) {
        this.r2UploadService = r2UploadService;
        this.spriteBasePath = spriteBasePath;
    }

    @Override
    public String generateAndUploadSprite(String videoUrl, Long videoId, String episodeNumber, String uuid) {
        Path outputDir = Paths.get(spriteBasePath, String.valueOf(videoId));
        Path outputPath = outputDir.resolve("output_" + episodeNumber + ".jpg");

        File spriteFile = outputPath.toFile();
        try {
            Files.createDirectories(outputDir);

            // ffmpeg -i '视频播放地址' -vf "fps=1/3,scale=160:-1,tile=23x23" -q:v 2 '输出文件名.jpg'
            List<String> command = new ArrayList<>();
            command.add("ffmpeg");
            command.add("-i");
            command.add(videoUrl);
            command.add("-vf");
            command.add("fps=1/3,scale=160:-1,tile=23x23");
            command.add("-q:v");
            command.add("2");
            command.add("-y"); // 如果输出文件已存在，则覆盖
            command.add(outputPath.toString());

            executeCommand(command);

            if (!spriteFile.exists() || spriteFile.length() == 0) {
                throw new BusinessException("Failed to generate sprite, file not created or is empty.");
            }

            MultipartFile multipartFile = new FileAsMultipartFile(spriteFile, "image/jpeg");
            return r2UploadService.uploadVideoAsset(multipartFile, uuid, episodeNumber);

        } catch (IOException e) {
            log.error("Error during sprite generation for videoId {} episode {}", videoId, episodeNumber, e);
            throw new BusinessException("Error generating sprite: " + e.getMessage());
        } finally {
            if (spriteFile.exists()) {
                try {
                    Files.delete(outputPath);
                } catch (IOException e) {
                    log.warn("未能删除临时的雪碧图文件: {}", outputPath, e);
                }
            }
        }
    }

    private void executeCommand(List<String> command) {
        log.info("Executing command: {}", String.join(" ", command));
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.debug(line);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("命令执行失败，退出码: {}. 命令: {}\n输出:\n{}", exitCode, String.join(" ", command), output.toString());
                throw new BusinessException("FFmpeg 命令执行失败，退出码: " + exitCode);
            }
            log.info("命令成功执行。");
        } catch (IOException | InterruptedException e) {
            log.error("执行命令时发生错误: {}", String.join(" ", command), e);
            Thread.currentThread().interrupt();
            throw new BusinessException("执行命令时出错: " + e.getMessage());
        }
    }
}
