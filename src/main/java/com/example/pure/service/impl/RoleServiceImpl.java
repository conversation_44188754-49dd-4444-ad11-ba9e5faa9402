package com.example.pure.service.impl;

import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.RoleMapper;
import com.example.pure.model.entity.Role;
import com.example.pure.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色服务实现类
 * 实现角色相关的业务逻辑
 */
@Slf4j
@Service
public class RoleServiceImpl implements RoleService {

    private final RoleMapper roleMapper;

    public RoleServiceImpl(RoleMapper roleMapper) {
        this.roleMapper = roleMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    // 创建新的role角色(ROLE_USER、ROLE_ADMIN同类角色)，不是分配给用用户新的角色
    public Role createRole(Role role) {
        log.info("开始创建角色: {}", role.getName());

        // 参数校验
        validateRoleParams(role);

        // 检查角色名是否已存在
        if (existsByName(role.getName())) {
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色名已存在");
        }

        role.setCreatedTime(LocalDateTime.now());
        role.setUpdatedTime(LocalDateTime.now());

        try {
            roleMapper.insertRole(role);
            log.info("角色创建成功: {}", role.getName());
            return role;
        } catch (Exception e) {
            log.error("角色创建失败: {}", e.getMessage(), e);
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色创建失败");
        }
    }

    // 根据角色的id查找角色，不是根据用户的id
    @Override
    public Role findById(Long id) {
        if (id == null) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色ID不能为空");
        }
        return roleMapper.findById(id);
    }

    // 根据角色的名称查找角色，不是根据用户的名称
    @Override
    public Role findByName(String name) {
        if (!StringUtils.hasText(name)) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色名称不能为空");
        }
        return roleMapper.findByName(name);
    }

    @Override
    public List<Role> findAll() {
        return roleMapper.findAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(Role role) {
        if (role.getId() == null) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色ID不能为空");
        }

        Role existingRole = findById(role.getId());
        if (existingRole == null) {
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色不存在");
        }

        // 如果要更新角色名，检查新名称是否已存在
        if (!existingRole.getName().equals(role.getName()) && existsByName(role.getName())) {
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色名已存在");
        }

        role.setUpdatedTime(LocalDateTime.now());

        try {
            roleMapper.updateRole(role);
            log.info("角色更新成功: {}", role.getId());
        } catch (Exception e) {
            log.error("角色更新失败: {}", e.getMessage(), e);
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        if (id == null) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色ID不能为空");
        }

        Role existingRole = findById(id);
        if (existingRole == null) {
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色不存在");
        }

        try {
            roleMapper.deleteUserRoles(id); // 先删除用户-角色关联
            roleMapper.deleteRole(id);      // 再删除角色
            log.info("角色删除成功: {}", id);
        } catch (Exception e) {
            log.error("角色删除失败: {}", e.getMessage(), e);
            throw new BusinessException(ResponseCode.BUSINESS_ERROR, "角色删除失败");
        }
    }

    @Override
    public boolean existsByName(String name) {
        return StringUtils.hasText(name) && roleMapper.findByName(name) != null;
    }

    /**
     * 验证角色参数
     */
    private void validateRoleParams(Role role) {
        if (role == null) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色信息不能为空");
        }

        if (!StringUtils.hasText(role.getName())) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色名称不能为空");
        }

        if (!role.getName().startsWith("ROLE_")) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "角色名称必须以ROLE_开头");
        }
    }
}
