package com.example.pure.service.impl;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.PageResult;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.*;
import com.example.pure.model.dto.PageRequestDTO;
import com.example.pure.model.dto.VideoEpisodesDTO;
import com.example.pure.model.dto.VideoInfoWithEpisodesDto;
import com.example.pure.model.entity.VideoInfo;
import com.example.pure.model.entity.VideoInfoTypeLink;
import com.example.pure.model.entity.VideoType;
import com.example.pure.service.FFmpegService;
import com.example.pure.service.PureVideoUrlService;
import com.example.pure.service.R2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 视频上传服务实现类
 */
@Service
@Slf4j
public class PureVideoUrlServiceImpl implements PureVideoUrlService {

    private final PureVideoUrlMapper pureUrlMapper;
    private final PureVideoEpisodesMapper pureUploadEpisodesMapper;
    private final PureVideoTypeMapper pureVideoTypeMapper;
    private final PureVideoInfoTypeLinkMapper pureVideoInfoTypeLinkMapper;
    private final PureVideoInteractionMapper pureVideoInteractionMapper;
    private final FFmpegService ffmpegService;
    private final WebClient webClient;
    private final R2Service r2Service;

    // 注入自身代理以解决@Async自调用问题
    @Autowired
    @Lazy
    private PureVideoUrlService self;

    @Autowired
    public PureVideoUrlServiceImpl(
            PureVideoUrlMapper pureUrlMapper,
            PureVideoEpisodesMapper pureUploadEpisodesMapper,
            PureVideoTypeMapper pureVideoTypeMapper,
            PureVideoInfoTypeLinkMapper pureVideoInfoTypeLinkMapper,
            PureVideoInteractionMapper pureVideoInteractionMapper,
            FFmpegService ffmpegService,
            @Qualifier("directWebClient") WebClient webClient,
            R2Service r2Service) {
        this.pureUrlMapper = pureUrlMapper;
        this.pureUploadEpisodesMapper = pureUploadEpisodesMapper;
        this.pureVideoTypeMapper = pureVideoTypeMapper;
        this.pureVideoInfoTypeLinkMapper = pureVideoInfoTypeLinkMapper;
        this.pureVideoInteractionMapper = pureVideoInteractionMapper;
        this.ffmpegService = ffmpegService;
        this.webClient = webClient;
        this.r2Service = r2Service;
    }

    /**
     * 上传视频及其分集信息
     * @param videoUploadRequest 视频上传请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadVideoWithEpisodes(VideoInfoWithEpisodesDto videoUploadRequest) {
        if (videoUploadRequest == null) {
            throw new BusinessException("上传视频失败：缺少视频信息");
        }

        // 1. 创建视频基本信息
        VideoInfo videoInfo = createVideoInfo(videoUploadRequest);

        // 2. 处理视频类型关联
        processVideoTypes(videoInfo.getId(), videoUploadRequest.getVideoTypes());

        // 3. 处理视频分集信息
        processVideoEpisodes(videoInfo.getId(), videoUploadRequest.getVideoEpisodes());
    }

    // 更新视频服务，如果视频已存在，就要更新的列表数量大于数据库已有的分集数量，才更新
    @Override
    public void uploadVideoEpisodes(VideoInfoWithEpisodesDto videoUploadRequest){
        if (videoUploadRequest == null) {
            throw new BusinessException("上传视频失败：缺少视频信息");
        }
        VideoInfo videoInfo=pureUrlMapper.findVideoInfoByTitle(videoUploadRequest.getTitle());

        if (videoInfo==null) {
                throw new BusinessException("查找不到视频信息");
        }
        // 获取上传的视频分集信息
        List<VideoEpisodesDTO> newEpisodes = videoUploadRequest.getVideoEpisodes();
        // 获取数据库的视频分集信息
        List<VideoEpisodesDTO> existingEpisodes = pureUploadEpisodesMapper.findVideoEpisodesByVideoInfoId(videoInfo.getId(), null);
        if (newEpisodes.size() >= existingEpisodes.size()) {

                //  删除原视频集数后再更新
                int deletedEpisodes=pureUploadEpisodesMapper.deleteVideoEpisodesByVideoInfoId(videoInfo.getId());
                if (deletedEpisodes<0){
                    throw new BusinessException("删除视频分集信息失败，视频ID: {}");
                }
                // 更新视频集数
                processVideoEpisodes(videoInfo.getId(), newEpisodes);
            }
        else {
            throw new BusinessException("视频数量低于当前数据库拥有数量");
        }

    }

    /**
     * 获取视频的详细信息，包括所有分集以及当前用户的点赞状态。
     *
     * @param title  视频标题
     * @param userId 当前登录用户的ID (如果未登录，则为null)
     * @return 包含视频完整信息的DTO
     */
    @Override
    public VideoInfoWithEpisodesDto getVideoInfoWithEpisodes(String title, Long userId){
        // 获取视频信息
        VideoInfo videoInfo=pureUrlMapper.findVideoInfoByTitle(title);
        if (videoInfo==null) {
            throw new BusinessException("查找不到视频信息");
        }

        /*
            通过一次高效的数据库查询，直接获取包含点赞总数和当前用户点赞状态的分集列表,
            这种和获取单个视频点赞数的接口不同是通过聚合函数获取多个视频集数的点赞数
         */
        List<VideoEpisodesDTO> videoEpisodes = pureUploadEpisodesMapper.findVideoEpisodesByVideoInfoId(videoInfo.getId(), userId);

        VideoInfoWithEpisodesDto videoUploadDto=new VideoInfoWithEpisodesDto();
        videoUploadDto.setTitle(videoInfo.getTitle());
        videoUploadDto.setDescription(videoInfo.getDescription());
        videoUploadDto.setCoverImageUrl(videoInfo.getCoverImageUrl());
        videoUploadDto.setVideoEpisodes(videoEpisodes);
        videoUploadDto.setVideoTypes(pureVideoTypeMapper.findVideoTypeByLinkWithVideoId(videoInfo.getId()));
        return videoUploadDto;
    }


    /**
     * 创建视频基本信息
     * @param request 视频上传请求
     * @return 创建后的视频信息对象
     */
    private VideoInfo createVideoInfo(VideoInfoWithEpisodesDto request) {
        // 创建视频信息
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setTitle(request.getTitle());
        videoInfo.setDescription(request.getDescription());
        videoInfo.setCoverImageUrl(request.getCoverImageUrl());

        //预防重新上传重复视频
        VideoInfo existingVideoInfo=pureUrlMapper.findVideoInfoByTitle(videoInfo.getTitle());
        if (existingVideoInfo!=null){
            throw new BusinessException("视频已存在，请别重新上传");
        }

        // 插入视频信息
        int videoInfoInsertedRows = pureUrlMapper.insertVideoInfo(videoInfo);
        if (videoInfoInsertedRows <= 0) {
            log.error("插入视频{}信息失败", videoInfo.getTitle());
            throw new BusinessException("上传视频失败：插入视频信息失败");
        }
        log.info("插入视频信息成功：{}", videoInfo.getTitle());

        // 查询刚插入的视频信息
        VideoInfo createdVideoInfo = pureUrlMapper.findVideoInfoByTitle(videoInfo.getTitle());
        if (createdVideoInfo == null) {
            throw new BusinessException("上传视频失败：无法获取刚创建的视频信息");
        }

        return createdVideoInfo;
    }

    /**
     * 处理视频类型关联
     * @param videoId 视频ID
     * @param videoTypes 视频类型列表
     */
    private void processVideoTypes(Long videoId, List<VideoType> videoTypes) {
        // 验证视频类型列表
        if (ObjectUtils.isEmpty(videoTypes)) {
            throw new BusinessException("上传视频失败：缺少视频类型");
        }

        // 创建视频信息和类型连接列表
        List<VideoInfoTypeLink> linksToCreate = new ArrayList<>();

        for (VideoType videoType : videoTypes) {
            if (videoType == null || videoType.getName() == null) {
                continue;
            }

            // 从数据库验证是否有这个类型
            VideoType existingVideoType = pureVideoTypeMapper.findVideoTypeByName(videoType.getName());
            Long typeId;

            // 处理类型不存在的情况
            if (existingVideoType == null || existingVideoType.getId() == null) {
                // 插入新类型
                int insertedRows = pureVideoTypeMapper.insertVideoType(videoType);
                if (insertedRows <= 0) {
                    throw new BusinessException("上传视频失败：插入视频类型失败");
                }

                // 重新查询获取ID
                existingVideoType = pureVideoTypeMapper.findVideoTypeByName(videoType.getName());
                if (existingVideoType == null || existingVideoType.getId() == null) {
                    throw new BusinessException("上传视频失败：插入视频类型后无法获取类型ID");
                }
            }

            typeId = existingVideoType.getId();

            // 创建视频信息和类型连接
            VideoInfoTypeLink link = new VideoInfoTypeLink();
            link.setVideoInfoId(videoId);
            link.setTypeId(typeId);
            linksToCreate.add(link);
        }

        // 如果有类型关联需要创建
        if (!linksToCreate.isEmpty()) {
            int linkInsertedRows = pureVideoInfoTypeLinkMapper.insertVideoInfoTypeLink(linksToCreate);
            if (linkInsertedRows <= 0) {
                log.error("插入视频类型关联信息失败，视频ID: {}", videoId);
                throw new BusinessException("上传视频失败：插入视频类型关联信息失败");
            }
            log.info("插入视频类型关联信息成功，视频ID: {}", videoId);
        }
    }

    /**
     * 处理视频分集信息
     * @param videoId 视频ID
     * @param episodes 分集列表
     */
    private void processVideoEpisodes(Long videoId, List<VideoEpisodesDTO> episodes) {
        // 验证分集列表
        if (ObjectUtils.isEmpty(episodes)) {
            log.warn("视频没有提供分集信息，视频ID: {}", videoId);
            return;
        }

        // 为本次处理的所有剧集生成一个共享的UUID
        String folderUuid = UUID.randomUUID().toString();

        // 为每个分集设置视频ID
        List<VideoEpisodesDTO> episodeList = new ArrayList<>();
        for (VideoEpisodesDTO episode : episodes) {
            if (ObjectUtils.isEmpty(episode)) {
                continue;
            }

            // 设置关联的视频ID
            episode.setVideoInfoId(videoId);

            // 如果episode有playUrl，先异步下载并上传到R2
            if (StringUtils.hasText(episode.getPlayUrl())) {
                try {
                    // 保存原始URL用于后续处理
                    String originalUrl = episode.getPlayUrl();

                    // self方法有@Async注解，主线程跳过这些方法使用线程池调用其他线程来运行
                    // 异步下载视频文件并上传到R2
                    self.downloadAndUploadVideoAsync(episode, videoId, folderUuid);

                    // 异步生成并上传雪碧图（使用原始URL，因为此时episode的playUrl可能还未更新）
                    // 使用代理对象调用，确保@Async生效
                    // 主线程跳过个有@Async线程池的代码，使用线程池里的其中一个线程去执行有@Async注解的方法
                    self.generateSpriteAsync(originalUrl, videoId, episode.getNumber(), folderUuid);
                } catch (Exception e) {
                    log.error("处理视频分集失败 - videoId: {}, episode: {}, 错误: {}", videoId, episode.getNumber(), e.getMessage(), e);
                    // 如果下载上传失败，继续处理其他分集，但记录错误
                }
            }
            // 主线程接着执行这个任务
            episodeList.add(episode);
        }

        // 如果有有效的分集数据，则插入数据库
        if (!ObjectUtils.isEmpty(episodeList)) {
            int episodesInsertedRows = pureUploadEpisodesMapper.insertVideoEpisodes(episodeList);
            if (episodesInsertedRows <= 0) {
                log.error("插入视频分集信息失败，视频ID: {}", videoId);
                throw new BusinessException("上传视频失败：插入视频分集信息失败");
            }
            log.info("插入视频分集信息成功，共{}集，视频ID: {}", episodeList.size(), videoId);
        }
    }

    // 获取视频信息按分页获取
    @Override
    public PageFinalResult<VideoInfo>  getVideoInfoWithPagination(PageRequestDTO pageRequest){
        // 计算分页参数
        int offset=pageRequest.getOffset();
        int limit=pageRequest.getPageSize();
        String keyword=pageRequest.getKeyword();

        List<VideoInfo> videoInfoList = pureUrlMapper.findVideoInfoWithPagination(offset,limit,keyword);
        // 获取视频总数
        int total=pureUrlMapper.countVideoInfo(keyword);
        PageResult pageResult= PageResult.of(pageRequest.getPageNum(),pageRequest.getPageSize(),total);
        PageFinalResult<VideoInfo> pageFinalResult=new PageFinalResult<>(videoInfoList,pageResult);
        return pageFinalResult;

    }

    // 获取视频的信息通过类型查询获取按分页查询
    @Override
    public  PageFinalResult<VideoInfo> getVideoInfoByTypeWithPagination(PageRequestDTO pageRequest) {
        // 计算分页参数
        int offset = pageRequest.getOffset();
        int limit = pageRequest.getPageSize();
        String keyword = pageRequest.getKeyword();
        if (!StringUtils.hasText(keyword)) {
            throw new BusinessException("请填写类型的关键字后再查询");
        }


        List<VideoInfo> videoInfoList = pureUrlMapper.findVideoInfoByTypeWithPagination(offset, limit, keyword);

        // 获取视频总数
        int total = pureUrlMapper.countVideoInfoByType(keyword);
        PageResult pageResult = PageResult.of(pageRequest.getPageNum(), pageRequest.getPageSize(), total);
        PageFinalResult<VideoInfo> pageFinalResult = new PageFinalResult<>(videoInfoList, pageResult);
        return pageFinalResult;
    }

    @Override
    public List<VideoType> getAllVideoType(){
         List<VideoType> allVideoType=pureVideoTypeMapper.findAllVideoType();
         return allVideoType;
    }

    @Override
    @Async("fileTaskExecutor")
    public void generateSpriteAsync(String playUrl, Long videoId, String episodeNumber, String folderUuid) {
        try {
            log.info("开始异步生成雪碧图 - videoId: {}, episode: {}", videoId, episodeNumber);
            String spriteUrl = ffmpegService.generateAndUploadSprite(playUrl, videoId, episodeNumber, folderUuid);

            if (StringUtils.hasText(spriteUrl)) {
                // 异步更新数据库中的雪碧图URL
                int updatedRows = pureUploadEpisodesMapper.updateSpriteSheetUrl(videoId, episodeNumber, spriteUrl);
                if (updatedRows > 0) {
                    log.info("异步生成雪碧图成功并已更新数据库 - videoId: {}, episode: {}, URL: {}", videoId, episodeNumber, spriteUrl);
                } else {
                    log.warn("异步生成雪碧图成功但更新数据库失败 - videoId: {}, episode: {}, URL: {}", videoId, episodeNumber, spriteUrl);
                }
            } else {
                log.warn("异步生成雪碧图返回空URL - videoId: {}, episode: {}", videoId, episodeNumber);
            }
        } catch (Exception e) {
            // 如果雪碧图生成失败，仅记录错误，不影响主流程
            log.error("异步生成雪碧图失败 - videoId: {}, episode: {}, 错误: {}", videoId, episodeNumber, e.getMessage(), e);
        }
    }

    /**
     * 异步下载视频文件并上传到R2存储
     * <p>
     * 使用WebClient下载视频文件，然后上传到R2存储，最后更新episode的playUrl为R2的objectKey
     * </p>
     *
     * @param episode    视频分集DTO对象
     * @param videoId    视频ID
     * @param folderUuid 文件夹UUID，用于组织文件结构
     */
    @Override
    @Async("fileTaskExecutor")
    public void downloadAndUploadVideoAsync(VideoEpisodesDTO episode, Long videoId, String folderUuid) {
        try {
            log.info("开始异步下载并上传视频文件 - videoId: {}, episode: {}, url: {}",
                    videoId, episode.getNumber(), episode.getPlayUrl());

            // 1. 使用WebClient下载视频文件
            byte[] videoData = webClient.get()
                    .uri(episode.getPlayUrl())
                    .accept(MediaType.APPLICATION_OCTET_STREAM)
                    .retrieve()
                    .bodyToMono(byte[].class)
                    .block(); // 阻塞等待下载完成

            if (videoData == null || videoData.length == 0) {
                log.warn("下载的视频文件为空 - videoId: {}, episode: {}", videoId, episode.getNumber());
                return;
            }

            log.info("视频文件下载完成 - videoId: {}, episode: {}, 文件大小: {} bytes",
                    videoId, episode.getNumber(), videoData.length);

            // 2. 上传到R2存储
            String objectKey = r2Service.uploadVideoAssetFromBytes(videoData, folderUuid, episode.getNumber(), "video/mp4");

            log.info("视频文件上传到R2成功 - videoId: {}, episode: {}, objectKey: {}",
                    videoId, episode.getNumber(), objectKey);

            // 4. 异步更新数据库中的playUrl
            int updatedRows = pureUploadEpisodesMapper.updateEpisodePlayUrl(videoId, episode.getNumber(), objectKey);
            if (updatedRows > 0) {
                log.info("异步更新数据库playUrl成功 - videoId: {}, episode: {}, objectKey: {}",
                        videoId, episode.getNumber(), objectKey);
            } else {
                log.warn("异步更新数据库playUrl失败 - videoId: {}, episode: {}, objectKey: {}",
                        videoId, episode.getNumber(), objectKey);
            }

        } catch (IOException e) {
            log.error("上传视频文件到R2失败 - videoId: {}, episode: {}, 错误: {}",
                    videoId, episode.getNumber(), e.getMessage(), e);
        } catch (Exception e) {
            log.error("异步下载并上传视频文件失败 - videoId: {}, episode: {}, 错误: {}",
                    videoId, episode.getNumber(), e.getMessage(), e);
        }
    }
}

