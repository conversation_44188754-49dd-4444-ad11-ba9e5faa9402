package com.example.pure.service.impl;

import com.example.pure.constant.DeviceType;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.model.dto.DeviceInfo;
import com.example.pure.service.DeviceService;
import com.example.pure.util.HashingUtil;
import com.example.pure.util.IpUtil;
import com.example.pure.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 设备管理服务实现类
 * <p>
 * 使用Redis实现用户设备登录限制
 * 采用优化的存储结构：每个用户一个hash，token作为field，设备信息作为value
 * </p>
 */
@Slf4j
@Service
public class DeviceServiceImpl implements DeviceService {

    private final RedisUtil redisUtil;
    private final IpUtil ipUtil;
    private final HashingUtil hashUtil;

    /**
     * 设备列表在Redis中的过期时间（秒）
     * 默认30天
     */
    private static final long DEVICES_EXPIRATION = 30 * 24 * 60 * 60;

    @Autowired
    public DeviceServiceImpl(RedisUtil redisUtil, IpUtil ipUtil, HashingUtil hashUtil) {
        this.redisUtil = redisUtil;
        this.ipUtil = ipUtil;
        this.hashUtil = hashUtil;
    }

    /**
     * 获取用户 Access Token 设备列表的Redis key
     */
    private String getAccessTokenDevicesKey(String username) {
        return SecurityConstants.USER_DEVICES_KEY_PREFIX + "accessToken:" + username;
    }

    /**
     * 获取用户 Refresh Token 设备列表的Redis key
     */
    private String getRefreshTokenDevicesKey(String username) {
        return SecurityConstants.USER_DEVICES_KEY_PREFIX + "refreshToken:" + username;
    }


    /**
     * 创建设备信息对象
     *
     * @param request HTTP请求对象，用于获取IP和User-Agent
     * @param deviceId 前端提供的设备ID (可能为null或空)
     * @param token JWT令牌
     * @return 设备信息对象
     */
    @Override
    public DeviceInfo createDeviceInfo(HttpServletRequest request, String deviceId, String token) {
        // 获取设备信息
        String userAgent = request.getHeader("User-Agent");
        String ipAddress = ipUtil.getClientIp(request);
        DeviceType deviceType = DeviceType.fromUserAgent(userAgent);

        // 使用前端提供的设备ID，如果为空则生成新的UUID
        String finalDeviceId = StringUtils.hasText(deviceId) ? deviceId : UUID.randomUUID().toString();

        // 创建设备信息对象
        return DeviceInfo.builder()
                .deviceId(finalDeviceId)
                .deviceType(deviceType.name())
                .token(token)
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .loginTime(LocalDateTime.now())
                .lastActiveTime(LocalDateTime.now())
                .build();
    }

    @Override
    public String addAccessTokenDevice(String username, DeviceInfo deviceInfo) {
        return addDevice(getAccessTokenDevicesKey(username), deviceInfo);
    }

    @Override
    public String addRefreshTokenDevice(String username, DeviceInfo deviceInfo) {
        return addDevice(getRefreshTokenDevicesKey(username), deviceInfo);
    }

    /**
     * 通用的添加设备逻辑
     * @param userDevicesKey Redis Key
     * @param deviceInfo 设备信息
     * @return 被移除的设备token（如果有）
     */
    private String addDevice(String userDevicesKey, DeviceInfo deviceInfo) {
        String token = deviceInfo.getToken();
        String removedDeviceToken = null;

        // 设置设备登录时间和最后活跃时间
        if (deviceInfo.getLoginTime() == null) {
            deviceInfo.setLoginTime(LocalDateTime.now());
        }
        if (deviceInfo.getLastActiveTime() == null) {
            deviceInfo.setLastActiveTime(LocalDateTime.now());
        }

        try {
            List<DeviceInfo> userDevices = getDevices(userDevicesKey);

            // 检查是否已达到最大设备数量限制
            if (userDevices.size() >= MAX_DEVICES) {
                // 按登录时间排序，移除最早登录的设备
                DeviceInfo oldestDevice = userDevices.stream()
                        .min(Comparator.comparing(DeviceInfo::getLoginTime))
                        .orElse(null);

                if (oldestDevice != null) {
                    log.info("用户设备数量已达上限，移除最早登录的设备: Key - {}, DeviceId - {}", userDevicesKey, oldestDevice.getDeviceId());
                    redisUtil.deleteHash(userDevicesKey, oldestDevice.getToken());
                    removedDeviceToken = oldestDevice.getToken();
                }
            }

            redisUtil.setHash(userDevicesKey, token, deviceInfo);
            redisUtil.expire(userDevicesKey, DEVICES_EXPIRATION);

            log.info("添加设备成功: Key - {}, DeviceId - {}", userDevicesKey, deviceInfo.getDeviceId());
            return removedDeviceToken;
        } catch (Exception e) {
            log.error("添加用户设备失败: Key - {}", userDevicesKey, e);
            return null;
        }
    }

    @Override
    public boolean isAccessTokenValid(String username, String accessToken) {
        String userDevicesKey = getAccessTokenDevicesKey(username);
        String matchingToken = findMatchingHashedToken(userDevicesKey, accessToken);
        return matchingToken != null;
    }

    // 获取用户的所有登录设备
    @Override
    public List<DeviceInfo> getAccessTokenDevices(String username) {
        return getDevices(getAccessTokenDevicesKey(username));
    }

    /**
     * 通用的获取设备列表逻辑
     * @param userDevicesKey Redis Key
     * @return 设备列表
     */
    private List<DeviceInfo> getDevices(String userDevicesKey) {
        try {
            List<Object> values = redisUtil.getHashValues(userDevicesKey);
            return values.stream()
                    .filter(v -> v instanceof DeviceInfo)
                    .map(v -> (DeviceInfo) v)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户设备列表失败: Key - {}", userDevicesKey, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean removeDeviceByAccessToken(String username, String accessToken) {
        String userDevicesKey = getAccessTokenDevicesKey(username);
        String matchingToken = findMatchingHashedToken(userDevicesKey, accessToken);

        if (matchingToken != null) {
            try {
                redisUtil.deleteHash(userDevicesKey, matchingToken);
                log.info("通过accessToken移除设备成功: Key - {}", userDevicesKey);
                return true;
            } catch (Exception e) {
                log.error("通过accessToken移除用户设备失败", e);
                return false;
            }
        }
        log.warn("通过accessToken移除设备失败，未找到匹配的token: Key - {}", userDevicesKey);
        return false;
    }

    @Override
    public boolean removeDeviceByRefreshToken(String username, String refreshToken) {
        String userDevicesKey = getRefreshTokenDevicesKey(username);
        String matchingToken = findMatchingHashedToken(userDevicesKey, refreshToken);

        if (matchingToken != null) {
            try {
                redisUtil.deleteHash(userDevicesKey, matchingToken);
                log.info("通过refreshToken移除设备成功: Key - {}", userDevicesKey);
                return true;
            } catch (Exception e) {
                log.error("通过refreshToken移除用户设备失败", e);
                return false;
            }
        }
        log.warn("通过refreshToken移除设备失败，未找到匹配的token: Key - {}", userDevicesKey);
        return false;
    }

    /**
     * 在Redis中查找与原始令牌匹配的哈希令牌
     * @param userDevicesKey Redis Key
     * @param rawToken 未经加密的原始令牌
     * @return 匹配的哈希令牌，如果未找到则返回null
     */
    private String findMatchingHashedToken(String userDevicesKey, String rawToken) {
        try {
            Set<Object> storedHashedTokens = redisUtil.getHashKeys(userDevicesKey);
            for (Object storedTokenObj : storedHashedTokens) {
                String storedHashedToken = (String) storedTokenObj;
                if (hashUtil.passwordMatch(rawToken, storedHashedToken)) {
                    return storedHashedToken; // 找到匹配项
                }
            }
        } catch (Exception e) {
            log.error("在Redis中查找匹配的哈希令牌时出错: Key - {}", userDevicesKey, e);
        }
        return null; // 未找到匹配项
    }

    // 一次性注销用户的所有设备
    @Override
    public void removeAllUserDevices(String username) {
        String accessTokenKey = getAccessTokenDevicesKey(username);
        String refreshTokenKey = getRefreshTokenDevicesKey(username);
        try {
            redisUtil.delete(accessTokenKey, refreshTokenKey);
            log.info("移除用户 {} 所有设备成功", username);
        } catch (Exception e) {
            log.error("移除用户所有设备失败", e);
        }
    }
}
