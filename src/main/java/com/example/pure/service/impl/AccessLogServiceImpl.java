package com.example.pure.service.impl;

import com.example.pure.mapper.primary.AccessLogMapper;
import com.example.pure.model.entity.AccessLog;
import com.example.pure.service.AccessLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j



@Service
public class AccessLogServiceImpl implements AccessLogService {

    private final AccessLogMapper accessLogMapper;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public AccessLogServiceImpl(AccessLogMapper accessLogMapper) {
        this.accessLogMapper = accessLogMapper;
    }

    @Override
    @Transactional
    public void logAccess(Long userId, String accessType,String Ip) {
        LocalDate today = LocalDate.now();
        String todayStr = today.format(DATE_FORMATTER);

        //查询今天是否有记录，防止重复记录
        AccessLog existingLog = accessLogMapper.findByUserIdAndTypeAndDate(userId, accessType, todayStr);

        if (existingLog == null) {
            // 获取昨天的记录
            LocalDate yesterday = today.minusDays(1);
            String yesterdayStr = yesterday.format(DATE_FORMATTER);

            AccessLog yesterdayLog = accessLogMapper.findByUserIdAndTypeAndDate(userId, accessType, yesterdayStr);

            // 创建今天的新记录，如果有昨天的记录就在其基础上加1，否则从1开始
            AccessLog newLog = new AccessLog();
            newLog.setUserId(userId);
            newLog.setAccessType(accessType);
            newLog.setAccessDate(today);
            newLog.setAccessCount(yesterdayLog != null ? yesterdayLog.getAccessCount() + 1 : 1);
            newLog.setCreatedTime(LocalDateTime.now());
            newLog.setUpdatedTime(LocalDateTime.now());
            newLog.setIpAddress(Ip);

            accessLogMapper.insert(newLog);
            log.info("Created new access log for user: {}, type: {}, count: {}",
                    userId, accessType, newLog.getAccessCount());
        }
        // 如果今天已经有记录，不做任何操作，保持当天的访问次数不变
    }
}
