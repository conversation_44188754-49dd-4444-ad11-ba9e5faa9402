package com.example.pure.service.impl;

import com.example.pure.model.dto.FileMetadata;
import com.example.pure.service.FilePureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.core.io.ResourceLoader;

import java.io.*;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件服务实现类
 * <p>
 * 提供文件相关操作的实现，包括：
 * - 视频流式播放
 * - 文件下载
 * - 文件类型判断
 * - 文件名安全验证
 * </p>
 */
@Slf4j
@Service
public class FilePureServiceImpl implements FilePureService {

    /**
     * 文件存储根目录
     */
    @Value("${file.storage.location.upload}")
    private  String fileStorageLocationStr;

    private final ResourceLoader resourceLoader;

    /**
     * 允许下载的文件扩展名白名单 (安全检查)
     */
    private static final List<String> ALLOWED_EXTENSIONS =
            Arrays.asList("pdf", "txt", "docx", "xlsx", "pptx", "zip", "rar", "exe");

    /**
     * 线程池，用于异步处理文件传输
     * @deprecated 请使用Spring的TaskExecutor替代
     */
    @Deprecated
    private final ExecutorService executorService;



    /**
     * 构造函数，初始化文件存储根目录和线程池
     *
     *
     */
    public FilePureServiceImpl(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
        // 创建固定大小的线程池，大小根据系统预期并发量调整
        // 此线程池仅用于向后兼容，新代码应使用Spring的TaskExecutor
        this.executorService = Executors.newFixedThreadPool(10);


    }

    /**
     * 动态构建用户特定的文件路径
     *
     * @param fileName 文件名
     * @return 用户特定的文件路径
     * @throws IOException 如果无法获取用户名或构建路径时
     */
    private Path getUserSpecificFilePath(String fileName) throws IOException {




        // 存放位置按文件类型存放，所以先获取文件类型再合并文件名
        String fileType=fileName.substring( fileName.lastIndexOf(".")+1);

        // 创建存放文件路径
        Path filePath =Paths.get(fileStorageLocationStr,fileType).toAbsolutePath().normalize();
        Path fullFilePath=filePath.resolve(fileName);


        // 确保用户目录存在，如果不存在则创建
        try {
            Files.createDirectories(filePath);
        } catch (IOException e) {
            log.error("无法创建用户目录: {}", filePath, e);
            throw new IOException("无法创建用户目录: " + filePath, e);
        }

        return fullFilePath;
    }

    /**
     * 获取视频流
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含视频流的ResponseEntity
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> streamVideo(String fileName, HttpHeaders headers) throws IOException {
        // 安全性检查：防止目录遍历攻击
        if (!isValidFileName(fileName)) {
            throw new IOException("无效的文件名: " + fileName + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        try {
            // 动态构建并验证用户特定的文件路径
            Path filePath = getUserSpecificFilePath(fileName);

            // 使用 ResourceLoader 从文件系统加载视频文件。这比直接使用UrlResource更灵活。
            Resource video = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在且可读
            if (!video.exists()) {
                log.warn("请求的文件不存在: {}", filePath);
                throw new IOException("文件不存在: " + fileName);
            }

            if (!video.isReadable()) {
                log.warn("请求的文件不可读: {}", filePath);
                throw new IOException("无法读取文件: " + fileName + "，请检查文件权限");
            }

            // 获取文件类型
            String contentType = determineContentType(filePath);

            // 实现范围请求 (Range Requests)
            long rangeStart = 0; //视频开始字节
            long rangeEnd; //视频结束字节
            long fileSize = video.contentLength();

            // 从请求头中获取Range信息
            String range = headers.getFirst(HttpHeaders.RANGE);
            // 检查range开头是否"bytes="开头这是标准的格式
            if (range != null && range.startsWith("bytes=")) {
                // 解析 Range 头部的起始和结束位置，提取除了"bytes="以外的子字符串并按子字符串里的-分开成字符串数组
                String[] ranges = range.substring("bytes=".length()).split("-");
                // 把字符串数组的第一个元素转成long类型，HTTP 协议和 Java 的 InputStream 都使用 long 类型来表示文件大小和偏移量，因为文件的大小（字节数）很容易超过 int 类型能够表示的最大值
                rangeStart = Long.parseLong(ranges[0]);
                // 检查数组是否有第二个元素（结束位置），如果有把第二个元素转long类型作为rangeEnd，如果没有第二个表示文件结尾设置fileSize-1(文件最后一个字节索引)
                rangeEnd = ranges.length > 1 ? Long.parseLong(ranges[1]) : fileSize - 1;

                // 检查范围有效性
                if (rangeStart >= fileSize) {
                    log.warn("请求范围无效，起始点超出文件大小: Range={}, fileSize={}, filePath={}", range, fileSize, filePath);
                    return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                            .header(HttpHeaders.CONTENT_RANGE, "bytes */" + fileSize)
                            .build();
                }
                if (rangeEnd >= fileSize) {
                    rangeEnd = fileSize - 1;
                }
                if (rangeStart > rangeEnd) {
                    log.warn("请求范围无效，起始点大于结束点: Range={}, filePath={}", range, filePath);
                     return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                            .header(HttpHeaders.CONTENT_RANGE, "bytes */" + fileSize)
                            .build();
                }

            } else {
                // 如果没有 Range 头部，则默认返回整个文件
                rangeEnd = fileSize - 1;
            }

            // 计算要返回的内容长度，我们要计算的是从第101个字节到第 201 个字节之间有多少个字节。如果我们只做 rangeEnd - rangeStart，结果是100，但这实际上少了 1 个字节（因为我们没有包含第 201 个字节本身）。所以我们需要加 1，得到正确的结果101。
            long contentLength = rangeEnd - rangeStart + 1;

            // 使用健壮的 RangeResource 来包装原始视频资源和请求的范围。
            // RangeResource 确保了即使流被多次读取（例如，被框架检查元数据后再次发送），
            // 每次都能提供一个全新的、正确的范围流，避免了因 InputStream 只能读取一次而导致的问题。
            // 设置响应头
            return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                    .header(HttpHeaders.CONTENT_TYPE, contentType) // 设置文件类型
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength)) // 设置内容长度
                    // 设置 Content-Range 头部，指示返回的内容范围，设置 Content-Range 头部，告诉客户端返回的内容范围。格式为 "bytes 起始位置-结束位置/文件总大小"
                    .header(HttpHeaders.CONTENT_RANGE, "bytes " + rangeStart + "-" + rangeEnd + "/" + fileSize)
                    .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                    .body(new RangeResource(video, rangeStart, contentLength));

        } catch (MalformedURLException e) {
            log.error("文件路径构建URL时出错: {}", fileName, e);
            throw new IOException("无效的文件路径: " + fileName, e);
        } catch (NumberFormatException e) {
            log.warn("解析Range头失败: {}, fileName={}", headers.getFirst(HttpHeaders.RANGE), fileName, e);
             throw new IOException("无效的Range请求头", e);
        }
    }

    /**
     * 下载文件
     *
     * @param filename 文件名
     * @param headers  请求头
     * @return 包含文件内容的ResponseEntity
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> downloadFile(String filename, HttpHeaders headers) throws IOException {
        // 安全检查: 防止路径遍历攻击
        if (!isValidFileName(filename)) {
            log.warn("请求下载的文件名无效: {}", filename);
            throw new IOException("无效的文件名: " + filename + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

         // 安全检查: 检查文件扩展名是否在白名单中
        String fileExtension = getFileExtension(filename);
        if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
            log.warn("请求下载的文件类型不被允许: {}, 扩展名: {}", filename, fileExtension);
            throw new IOException("不支持的文件类型: " + fileExtension + "。允许的文件类型: " + String.join(", ", ALLOWED_EXTENSIONS));
        }

        try {
            // 动态构建并验证用户特定的文件路径
            Path filePath = getUserSpecificFilePath(filename);
            Resource resource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在
            if (!resource.exists() || !resource.isReadable()) {
                 log.warn("请求下载的文件不存在或不是文件: {}", filePath);
                throw new IOException("文件不存在: " + filename);
            }

            // 获取文件MIME类型
            String contentType = determineContentType(filePath);

            // 设置HTTP头
            HttpHeaders responseHeaders = new HttpHeaders();
            // 使用Spring的ContentDisposition构建器来设置头，更健壮
            ContentDisposition contentDisposition = ContentDisposition.attachment()
                    .filename(resource.getFilename())
                    .build();
            responseHeaders.setContentDisposition(contentDisposition);
            responseHeaders.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            responseHeaders.add(HttpHeaders.PRAGMA, "no-cache");
            responseHeaders.add(HttpHeaders.EXPIRES, "0");
            responseHeaders.setContentType(MediaType.parseMediaType(contentType));

            // 构建响应 - 注意：对于下载，通常不处理Range请求，直接返回整个文件
            return ResponseEntity.ok()
                    .headers(responseHeaders)
                    .contentLength(resource.contentLength())
                    // 返回ResponseEntity.body（Resource）时自动从资源流获取输入流和设置缓冲区和输出流，最后返回数据
                    .body(resource);

        } catch (MalformedURLException e) {
             log.error("文件路径构建URL时出错: {}", filename, e);
            throw new IOException("无效的文件路径: " + filename, e);
        }
    }

    /**
     * 确定文件的MIME类型
     * 获取文件MIME类型，MIME类型（让浏览器识别这个类型之后，根据这个类型调用相应的事，比如说视频或图片在线观看或者下载）
     *
     * @param filePath 文件路径 (应该是已经验证过的绝对路径)
     * @return 文件的MIME类型
     * @throws IOException 如果文件访问出错
     */
    @Override
    public String determineContentType(Path filePath) throws IOException {
        // 这里的filePath应该是调用此方法前已经通过 getUserSpecificFilePath 获取并验证过的
        String contentType = Files.probeContentType(filePath);
        if (contentType == null) {
            // 如果 probeContentType 无法确定，则根据文件扩展名手动设置
            String fileName = filePath.getFileName().toString();
            String extension = getFileExtension(fileName); // 使用已有方法获取小写扩展名
            switch (extension) {
                case "mp4": contentType = "video/mp4"; break;
                case "webm": contentType = "video/webm"; break;
                case "ogg": contentType = "video/ogg"; break; // 通常是 .ogv 或 .oga
                case "ogv": contentType = "video/ogg"; break;
                case "mp3": contentType = "audio/mpeg"; break;
                case "wav": contentType = "audio/wav"; break;
                case "oga": contentType = "audio/ogg"; break;
                case "pdf": contentType = "application/pdf"; break;
                case "txt": contentType = "text/plain"; break;
                case "html": case "htm": contentType = "text/html"; break;
                case "css": contentType = "text/css"; break;
                case "js": contentType = "application/javascript"; break;
                case "json": contentType = "application/json"; break;
                case "xml": contentType = "application/xml"; break;
                case "doc": contentType = "application/msword"; break;
                case "docx": contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"; break;
                case "xls": contentType = "application/vnd.ms-excel"; break;
                case "xlsx": contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; break;
                case "ppt": contentType = "application/vnd.ms-powerpoint"; break;
                case "pptx": contentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation"; break;
                case "zip": contentType = "application/zip"; break;
                case "rar": contentType = "application/vnd.rar"; break; // 或者 application/x-rar-compressed
                case "exe": contentType = "application/vnd.microsoft.portable-executable"; break; // 或者 application/x-msdownload
                case "jpg": case "jpeg": contentType = "image/jpeg"; break;
                case "png": contentType = "image/png"; break;
                case "gif": contentType = "image/gif"; break;
                case "bmp": contentType = "image/bmp"; break;
                case "webp": contentType = "image/webp"; break;
                case "svg": contentType = "image/svg+xml"; break;
                default: contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE; // 通用二进制流
            }
            log.debug("无法自动探测文件 '{}' 的类型，根据扩展名 '{}' 设置为 '{}'", filePath.getFileName(), extension, contentType);
        } else {
             log.debug("探测到文件 '{}' 的类型为 '{}'", filePath.getFileName(), contentType);
        }
        return contentType;
    }

    /**
     * 验证文件名是否合法，防止目录遍历攻击
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */
    @Override
    public boolean isValidFileName(String fileName) {
        // 定义一个正则表达式，只允许字母、数字、下划线、连字符和点
        Pattern pattern = Pattern.compile("^[a-zA-Z0-9_\\-.]+$");
        Matcher matcher = pattern.matcher(fileName);
        // 检查文件名是否匹配正则表达式，并且不包含 ".." 序列
        return matcher.matches() && !fileName.contains("..");
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名
     */
    @Override
    public String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 流式传输文件（通用方法，适用于任何文件类型）
     * <p>
     * 注意：此通用方法也应该使用用户特定的路径。
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含文件流的Resource对象
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> streamFile(String fileName, HttpHeaders headers) throws IOException {
        // 安全性检查：文件名本身
        if (!isValidFileName(fileName)) {
            throw new IOException("无效的文件名: " + fileName + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        try {
            // 动态构建并验证用户特定的文件路径
            Path filePath = getUserSpecificFilePath(fileName);

            // 使用 ResourceLoader 从文件系统加载文件
            Resource file = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在且可读
            if (!file.exists()) {
                log.warn("请求的文件不存在: {}", filePath);
                throw new IOException("文件不存在: " + fileName);
            }

            if (!file.isReadable()) {
                 log.warn("请求的文件不可读: {}", filePath);
                throw new IOException("无法读取文件: " + fileName + "，请检查文件权限");
            }

            // 获取文件类型
            String contentType = determineContentType(filePath);

            // 实现范围请求 (Range Requests)
            long rangeStart = 0; // 文件开始字节
            long rangeEnd; // 文件结束字节
            long fileSize = file.contentLength();

            // 从请求头中获取Range信息
            String range = headers.getFirst(HttpHeaders.RANGE);
            if (range != null && range.startsWith("bytes=")) {
                // 清除range开头的"bytes="
                String rangeValue = range.substring("bytes=".length());

                // 解析范围值
                String[] ranges = rangeValue.split("-");
                try {
                    rangeStart = Long.parseLong(ranges[0]); // 起始字节
                } catch (NumberFormatException e) {
                     log.warn("无效的Range起始值: {}, fileName={}", ranges[0], fileName);
                    throw new IOException("无效的Range请求头 - 起始值错误", e);
                }


                // 如果指定了结束字节，则使用指定值；否则使用文件末尾
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                     try {
                        rangeEnd = Long.parseLong(ranges[1]);
                    } catch (NumberFormatException e) {
                        log.warn("无效的Range结束值: {}, fileName={}", ranges[1], fileName);
                        throw new IOException("无效的Range请求头 - 结束值错误", e);
                    }
                } else {
                    rangeEnd = fileSize - 1; // 到文件末尾
                }

                // 校验和调整范围
                 if (rangeStart >= fileSize) {
                    log.warn("请求范围无效，起始点超出文件大小: Range={}, fileSize={}, filePath={}", range, fileSize, filePath);
                    return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                            .header(HttpHeaders.CONTENT_RANGE, "bytes */" + fileSize)
                            .body(null); // Body为null
                }
                if (rangeEnd >= fileSize) {
                    rangeEnd = fileSize - 1; // 调整结束点到文件末尾
                }
                 if (rangeStart < 0) { // 起始点不能小于0
                    rangeStart = 0;
                }
                if (rangeStart > rangeEnd) { // 起始点不能大于结束点
                     log.warn("请求范围无效，起始点大于结束点: Range={}, filePath={}", range, filePath);
                     return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                            .header(HttpHeaders.CONTENT_RANGE, "bytes */" + fileSize)
                            .body(null); // Body为null
                }


                // 计算最终的内容长度
                long contentLength = rangeEnd - rangeStart + 1;

                // 使用健壮的 RangeResource 来包装原始文件资源和请求的范围。
                // 这比手动操作 InputStream 更安全，因为它能保证流的可重复读。
                //
                Resource rangeResource = new RangeResource(file, rangeStart, contentLength);

                // 设置Content-Range头和状态码
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                        .header(HttpHeaders.CONTENT_RANGE, "bytes " + rangeStart + "-" + rangeEnd + "/" + fileSize)
                        .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(contentLength)
                        // 返回ResponseEntity.body（Resource）时自动从资源流获取输入流和设置缓冲区和输出流，最后返回数据
                        .body(rangeResource);
            } else {
                // 不是范围请求，返回整个文件
                return ResponseEntity.ok()
                        .header(HttpHeaders.ACCEPT_RANGES, "bytes") // 仍然告知客户端支持范围请求
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(fileSize)
                        .body(file);
            }
        } catch (MalformedURLException e) {
            log.error("文件路径构建URL时出错: {}", fileName, e);
            throw new IOException("文件路径无效: " + fileName, e);
        } catch (IOException e) {
            // 捕获 getUserSpecificFilePath 可能抛出的IO异常或其他IO异常
            log.error("处理流式文件传输时发生IO错误: {}", fileName, e);
            throw e; // 重新抛出，让上层处理或由全局异常处理器处理
        }
    }

    /**
     * 最佳实践如果需要拿到异步数据的话，必须让异步获得的数据返回主线程，再从主线程返回客户端
     * 异步流式传输文件（通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers) {
         // 安全性检查：文件名本身 - 在异步方法开始时执行
        if (!isValidFileName(fileName)) {
            log.warn("异步流式传输：无效的文件名: {}", fileName);
            CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
            future.completeExceptionally(new IOException("无效的文件名: " + fileName));
            return future;
        }

        // CompletableFuture 用于包装最终结果或异常
        CompletableFuture<ResponseBodyEmitter> resultFuture = new CompletableFuture<>();

        try {
            log.debug("开始异步处理文件流(fileTaskExecutor): {}", fileName);

            // 动态构建并验证用户特定的文件路径
            // 注意：这里获取用户名和路径是在调用streamFileAsync的主线程中执行的
            final Path filePath = getUserSpecificFilePath(fileName);
            final Resource fileResource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

             // 检查文件是否存在且可读 - 在主线程中检查，避免异步任务中做无效操作
            if (!fileResource.exists() || !fileResource.isReadable()) {
                log.warn("异步流式传输：请求的文件不存在或不是文件: {}", filePath);
                 throw new IOException("文件不存在: " + fileName);
            }
             if (!fileResource.isReadable()) {
                log.warn("异步流式传输：请求的文件不可读: {}", filePath);
                 throw new IOException("无法读取文件: " + fileName);
            }

            // 获取文件元数据 - 只获取大小，类型等可以省略或在异步任务需要时获取
            final long fileSize = fileResource.contentLength();

            // 解析Range头信息
            String range = headers.getFirst(HttpHeaders.RANGE);
            long rangeStart = 0;
            long rangeEnd = fileSize - 1;

            if (range != null && range.startsWith("bytes=")) {
                String[] ranges = range.substring("bytes=".length()).split("-");
                try {
                    rangeStart = Long.parseLong(ranges[0]);
                } catch (NumberFormatException e) {
                    log.warn("异步流式传输：无效的Range起始值: {}, fileName={}", ranges[0], fileName);
                    throw new IOException("无效的Range请求头 - 起始值错误", e);
                }
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                     try {
                        rangeEnd = Long.parseLong(ranges[1]);
                    } catch (NumberFormatException e) {
                         log.warn("异步流式传输：无效的Range结束值: {}, fileName={}", ranges[1], fileName);
                        throw new IOException("无效的Range请求头 - 结束值错误", e);
                    }
                }
            }

            // 确保范围有效
             if (rangeStart < 0) {rangeStart = 0;}
             if (rangeEnd >= fileSize){ rangeEnd = fileSize - 1;}

             // 如果范围无效（例如 start > end），提前处理
             if (rangeStart > rangeEnd) {
                log.warn("异步流式传输：请求范围无效，起始点大于结束点: Range={}, filePath={}", range, filePath);
                 // 对于异步场景，直接完成异常可能更合适
                 throw new IOException("无效的Range请求，起始位置大于结束位置");
             }


            // 计算要返回的内容长度
            final long contentLength = rangeEnd - rangeStart + 1;
            final long finalRangeStart = rangeStart; // 用于lambda表达式

            // 创建响应发射器，设置超时时间（可选，例如120秒）
            ResponseBodyEmitter emitter = new ResponseBodyEmitter(120_000L);

              /* @async的子线程返回未处理数据的存储二进制流类给主线程return CompletableFuture.completedFuture(emitter)，
                让主线程提交数据这个类给客户端形成握手并连接，后续通过runAsync调用线程池启用的子线程持续发送数据到客户端
                ()->{}括号内为传参，{}为运行的方法，debug只能单独在那个线程的代码里添加点监控，也就是正在监控主线程不能跳到子线程监控 */

            // 使用 runAsync 在 fileTaskExecutor 线程池中执行IO密集型任务
            CompletableFuture.runAsync(() -> {
                // try-with-resources 确保 InputStream 被关闭
                try (InputStream inputStream = fileResource.getInputStream()) { // 使用 final 的 file 对象
                    // 跳过开始部分
                    long bytesSkipped = inputStream.skip(finalRangeStart);
                    if (bytesSkipped < finalRangeStart) {
                         log.error("异步流式传输：无法跳过足够的字节: 需要 {}, 实际 {}, filePath={}", finalRangeStart, bytesSkipped, filePath);
                        throw new IOException("无法跳过指定的字节数: " + finalRangeStart);
                    }

                    // 发送逻辑
                    byte[] buffer = new byte[8192]; // 8KB 缓冲区
                    int bytesRead;
                    long bytesRemaining = contentLength;
                    long totalBytesSent = 0;

                    // 分块发送数据，读取数据到缓存区，inputStream.read()读取输入流字节0开始，读取bytesRemaining大小数据到buffer字节数组里
                    while (bytesRemaining > 0 && (bytesRead = inputStream.read(buffer, 0, (int) Math.min(buffer.length, bytesRemaining))) != -1) {

                        // 需要发送一个完整的对象，通常是 byte[]
                        if (bytesRead == buffer.length) {
                            // 如果读取了整个缓冲区，可以直接发送 buffer (需要确保emitter.send能处理共享buffer的并发问题，或者拷贝一份)
                            // 为安全起见，即使是满缓冲区，也拷贝一份
                            emitter.send(Arrays.copyOf(buffer, bytesRead));
                        } else {
                            // 如果读取的字节数小于缓冲区大小，创建一个精确大小的数组发送
                            byte[] chunkToSend = Arrays.copyOf(buffer, bytesRead);
                            emitter.send(chunkToSend);
                        }

                        totalBytesSent += bytesRead;
                        bytesRemaining -= bytesRead;

                        // （可选）控制发送速率，如果需要的话
                        // try {
                        //     Thread.sleep(5); // 例如，休眠5毫秒
                        // } catch (InterruptedException e) {
                        //     Thread.currentThread().interrupt();
                        //     log.warn("异步流式传输发送线程被中断: {}", filePath);
                        //     throw new IOException("发送线程被中断", e);
                        // }
                    }

                     if (bytesRemaining > 0) {
                        log.warn("异步流式传输：未发送完所有请求的字节. 剩余: {}, filePath={}", bytesRemaining, filePath);
                    }

                    // 成功完成数据发送
                    emitter.complete();
                    log.debug("异步文件流传输完成: {}, 总字节数: {}, 请求范围: bytes={}-{}", filePath.getFileName(), totalBytesSent, finalRangeStart, finalRangeStart + contentLength -1);

                } catch (IOException e) {
                     log.error("异步文件流传输过程中发生IO错误: {}", filePath, e);
                    emitter.completeWithError(e);
                } catch (Exception e) {
                     log.error("异步文件流传输过程中发生意外错误: {}", filePath, e);
                     emitter.completeWithError(e);
                 }
            }, executorService); //明确指定线程池（如果@Async配置的默认线程池不是这个）或者依赖@Async配置

             // 注册完成和超时的回调（可选，但推荐）
            emitter.onCompletion(() -> log.info("Emitter completed for: {}", filePath.getFileName()));
            emitter.onTimeout(() -> {
                log.warn("Emitter timed out for: {}", filePath.getFileName());
                emitter.completeWithError(new IOException("流式传输超时 for " + filePath.getFileName()));
            });
             emitter.onError((throwable) -> log.error("Emitter error for: {}", filePath.getFileName(), throwable));


            // 异步任务已启动，立即完成外部的Future，值为emitter
             resultFuture.complete(emitter);


        } catch (IOException e) {
            // 处理在准备阶段（获取路径、检查文件、解析Range等）发生的IO异常
            log.error("准备异步文件流时出错: {}", fileName, e);
            resultFuture.completeExceptionally(e); // 将异常设置给外部Future
        } catch (Exception e) {
             // 处理其他准备阶段的意外异常
            log.error("准备异步文件流时发生意外错误: {}", fileName, e);
            resultFuture.completeExceptionally(e);
        }

        return resultFuture; // 返回包含emitter或异常的Future
    }


    /**
     * 异步下载文件（带选项）
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行文件下载，避免阻塞请求处理线程
     * 支持自定义选项，如缓冲区大小等
     * </p>
     *
     * @param filename 文件名
     * @param headers  请求头，用于处理Range请求和其他HTTP头信息
     * @param options  下载选项，可包含缓冲区大小等参数
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> downloadFileAsync(String filename, HttpHeaders headers, java.util.Map<String, Object> options) {
         // 安全性检查：文件名本身
        if (!isValidFileName(filename)) {
             log.warn("异步下载：无效的文件名: {}", filename);
            CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
            future.completeExceptionally(new IOException("无效的文件名: " + filename));
            return future;
        }

        // 安全检查: 检查文件扩展名是否在白名单中
        String fileExtension = getFileExtension(filename);
        if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
             log.warn("异步下载：请求的文件类型不被允许: {}, 扩展名: {}", filename, fileExtension);
            CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
             future.completeExceptionally(new IOException("不支持的文件类型: " + fileExtension));
             return future;
        }


         CompletableFuture<ResponseBodyEmitter> resultFuture = new CompletableFuture<>();

        try {
            log.debug("开始异步处理文件下载(带选项，fileTaskExecutor): {}", filename);

            // 动态构建并验证用户特定的文件路径
            final Path filePath = getUserSpecificFilePath(filename);
            final Resource fileResource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在且可读
            if (!fileResource.exists() || !fileResource.isReadable()) {
                log.warn("异步下载：请求的文件不存在或不是文件: {}", filePath);
                 throw new IOException("文件不存在: " + filename);
            }
            if (!fileResource.isReadable()) {
                 log.warn("异步下载：请求的文件不可读: {}", filePath);
                throw new IOException("无法读取文件: " + filename);
            }


            // 解析Range头信息 (下载通常忽略Range，但如果需要支持断点续传则保留)
            final long fileSize = fileResource.contentLength();
            String range = headers.getFirst(HttpHeaders.RANGE);
            long rangeStart = 0;
            long rangeEnd = fileSize - 1;

             // 注意：标准的下载通常不处理Range，这里假设如果提供了Range，则进行部分下载
            boolean isRangeRequest = false;
            if (range != null && range.startsWith("bytes=")) {
                 isRangeRequest = true; // 标记为范围请求
                String[] ranges = range.substring("bytes=".length()).split("-");
                 try {
                    rangeStart = Long.parseLong(ranges[0]);
                } catch (NumberFormatException e) {
                     log.warn("异步下载：无效的Range起始值: {}, fileName={}", ranges[0], filename);
                    throw new IOException("无效的Range请求头 - 起始值错误", e);
                }
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                     try {
                        rangeEnd = Long.parseLong(ranges[1]);
                    } catch (NumberFormatException e) {
                         log.warn("异步下载：无效的Range结束值: {}, fileName={}", ranges[1], filename);
                        throw new IOException("无效的Range请求头 - 结束值错误", e);
                    }
                }
             }

             // 调整和校验范围 (仅当是Range请求时)
            if (isRangeRequest) {
                 if (rangeStart < 0) {rangeStart = 0;}
                 if (rangeEnd >= fileSize) {rangeEnd = fileSize - 1;}
                 if (rangeStart > rangeEnd) {
                     log.warn("异步下载：请求范围无效，起始点大于结束点: Range={}, filePath={}", range, filePath);
                     throw new IOException("无效的Range请求，起始位置大于结束位置");
                 }
            } else {
                // 如果不是Range请求，确保rangeStart和rangeEnd覆盖整个文件
                rangeStart = 0;
                rangeEnd = fileSize - 1;
            }


            // 计算要返回的内容长度
            final long contentLength = rangeEnd - rangeStart + 1;
            final long finalRangeStart = rangeStart;

            // 创建响应发射器
            ResponseBodyEmitter emitter = new ResponseBodyEmitter(120_000L); // 120秒超时

            // 从options中获取缓冲区大小，如果没有指定则使用默认值
            final int bufferSize = options != null && options.containsKey("bufferSize") ?
                    (int) options.get("bufferSize") : 8192 * 2; // 默认16KB

            // 从options中获取线程休眠时间，控制发送速度，如果没有指定则不休眠
            final long sleepTime = options != null && options.containsKey("sleepTime") ?
                    (long) options.get("sleepTime") : 0; // 默认0，不休眠

            log.debug("文件下载使用的缓冲区大小: {} 字节, 发送间隔: {} ms", bufferSize, sleepTime);

            //  主线程调用异步后运行return CompletableFuture.completedFuture(emitter),让子线程来执行异步任务
            CompletableFuture.runAsync(() -> {
                try (InputStream inputStream = fileResource.getInputStream()) {
                    // 跳过这个数量的字节
                    long bytesSkipped = inputStream.skip(finalRangeStart);
                    if (bytesSkipped < finalRangeStart) {
                        log.error("异步下载：无法跳过足够的字节: 需要 {}, 实际 {}, filePath={}", finalRangeStart, bytesSkipped, filePath);
                        throw new IOException("无法跳过指定的字节数: " + finalRangeStart);
                    }

                    // 使用指定的缓冲区大小
                    byte[] buffer = new byte[bufferSize];
                    int bytesRead;
                    long bytesRemaining = contentLength;
                    long totalBytesSent = 0;
                    // 读取数据到缓存区，inputStream.read()读取输入流字节0开始，读取bytesRemaining大小数据到buffer字节数组里
                    while (bytesRemaining > 0 && (bytesRead = inputStream.read(buffer, 0, (int) Math.min(buffer.length, bytesRemaining))) != -1) {

                        // 需要发送一个完整的对象，通常是 byte[]
                        if (bytesRead == buffer.length) {
                            // 如果读取了整个缓冲区，可以直接发送 buffer (需要确保emitter.send能处理共享buffer的并发问题，或者拷贝一份)
                            // 为安全起见，即使是满缓冲区，也拷贝一份
                            emitter.send(Arrays.copyOf(buffer, bytesRead));
                        } else {
                            // 如果读取的字节数小于缓冲区大小，创建一个精确大小的数组发送
                            byte[] chunkToSend = Arrays.copyOf(buffer, bytesRead);
                            emitter.send(chunkToSend);
                        }

                        totalBytesSent += bytesRead;
                        bytesRemaining -= bytesRead;

                        // 如果设置了休眠时间
                        if (sleepTime > 0) {
                            try {
                                Thread.sleep(sleepTime);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                log.warn("异步下载发送线程被中断: {}", filePath);
                                throw new IOException("发送线程被中断", e);
                            }
                        }
                    }

                     if (bytesRemaining > 0) {
                        log.warn("异步下载：未发送完所有请求的字节. 剩余: {}, filePath={}", bytesRemaining, filePath);
                    }

                    // 成功完成数据发送
                    emitter.complete();
                    log.debug("异步文件下载完成: {}, 总字节数: {}", filePath.getFileName(), totalBytesSent);

                } catch (IOException e) {
                     log.error("异步文件下载过程中发生IO错误: {}", filePath, e);
                    emitter.completeWithError(e);
                } catch (Exception e) {
                     log.error("异步文件下载过程中发生意外错误: {}", filePath, e);
                     emitter.completeWithError(e);
                 }
            }, executorService); // 指定线程池

             // 注册回调
             emitter.onCompletion(() -> log.info("Emitter completed for download: {}", filePath.getFileName()));
             emitter.onTimeout(() -> {
                 log.warn("Emitter timed out for download: {}", filePath.getFileName());
                 emitter.completeWithError(new IOException("下载传输超时 for " + filePath.getFileName()));
             });
             emitter.onError((throwable) -> log.error("Emitter error for download: {}", filePath.getFileName(), throwable));

            // 异步任务已启动，立即完成外部的Future
            resultFuture.complete(emitter);

        } catch (IOException e) {
            log.error("准备异步文件下载时出错: {}", filename, e);
            resultFuture.completeExceptionally(e);
        } catch (Exception e) {
             log.error("准备异步文件下载时发生意外错误: {}", filename, e);
            resultFuture.completeExceptionally(e);
        }
         return resultFuture;
    }

    /**
     * 获取文件元数据信息
     *
     * @param filename 文件名
     * @return 文件元数据对象
     * @throws IOException 如果文件访问出错或文件不存在
     */
    @Override
    public FileMetadata getFileMetadata(String filename) throws IOException {
        // 安全检查: 防止路径遍历攻击
        if (!isValidFileName(filename)) {
            throw new IOException("无效的文件名: " + filename + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        // 动态构建并验证用户特定的文件路径
        Path filePath = getUserSpecificFilePath(filename);
        Resource resource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());
        File file = filePath.toFile();

        // 初始化元数据
        FileMetadata metadata = new FileMetadata();
        metadata.setFileName(filename); // 使用原始传入的文件名

        // 检查文件是否存在且可读
        boolean exists = resource.exists();
        boolean readable = exists && resource.isReadable(); // 只有存在的文件才能判断是否可读

        metadata.setExists(exists);
        metadata.setReadable(readable);

        if (exists) { // 只有文件存在时才获取大小和类型
            metadata.setFileSize(resource.contentLength());
            if (readable) { // 只有可读时尝试获取类型
                 try {
                    String contentType = determineContentType(filePath);
                    metadata.setContentType(contentType);
                 } catch (IOException e) {
                     log.warn("获取文件 {} 的 ContentType 时出错: {}", filePath, e.getMessage());
                     metadata.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 出错时给默认值
                 }
            } else {
                 log.warn("文件 {} 不可读，无法确定 ContentType", filePath);
                 metadata.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 不可读时给默认值
            }
        } else {
            // 文件不存在
            log.warn("请求获取元数据的文件不存在: {}", filePath);
            metadata.setFileSize(0);
            metadata.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 不存在时给默认值
             // 可以考虑直接抛出FileNotFoundException，取决于业务需求
             // throw new FileNotFoundException("文件不存在: " + filename);
        }

        return metadata;
    }

    /**
     * 异步流式传输视频
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行视频流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamVideoAsync(String fileName, HttpHeaders headers) {
        // 直接调用通用的文件流方法
        log.debug("调用视频流异步处理方法，将委托给通用文件流处理方法: {}", fileName);
        // 注意：通用方法 streamFileAsync 内部已经处理了文件名校验和路径构建
        return streamFileAsync(fileName, headers);
    }

    /**
     * 异步下载文件（无选项版本，委托给带选项版本）
     *
     * @param filename 文件名
     * @param headers 请求头
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> downloadFileAsync(String filename, HttpHeaders headers) {
        // 调用带选项的方法，传递默认选项 (null)
        log.debug("调用文件下载异步处理方法，将委托给带选项的文件下载处理方法: {}", filename);
        // 注意：通用方法 downloadFileAsync(带选项) 内部已经处理了文件名和扩展名校验以及路径构建
        return downloadFileAsync(filename, headers, null);
    }

    /**
     * 代表底层资源的特定字节范围的资源实现。
     * <p>
     * 这个类专门用于处理HTTP范围请求（HTTP Range requests），例如视频拖动播放或文件断点续传。
     * 它通过每次调用 {@link #getInputStream()} 时都创建一个新的输入流，来确保资源的可重复读取性。
     * 这解决了直接使用 {@link InputStreamResource} 可能导致流只能被消费一次的问题，
     * 使其在Spring MVC环境中作为 {@link ResponseEntity} 的body部分时更加健壮。
     * </p>
     */
    public static class RangeResource extends org.springframework.core.io.AbstractResource {
        private final Resource resource;
        private final long start;
        private final long length;

        /**
         * 构造一个新的 {@code RangeResource}.
         * @param resource 底层的原始资源 (例如一个 {@link UrlResource} 指向完整文件)
         * @param start 范围的起始字节位置（包含）
         * @param length 范围的长度（字节数）
         */
        public RangeResource(Resource resource, long start, long length) {
            if (start < 0) {
                throw new IllegalArgumentException("Start position cannot be negative");
            }
            if (length < 0) {
                throw new IllegalArgumentException("Length cannot be negative");
            }
            this.resource = resource;
            this.start = start;
            this.length = length;
        }

        /**
         * {@inheritDoc}
         * <p>每次调用此方法时，都会从底层资源创建一个新的 {@link InputStream}，
         * 跳到指定的起始位置，并返回一个被 {@link LimitedInputStream} 包装的流，
         * 以确保只返回请求范围内的数据。</p>
         */
        @Override
        public InputStream getInputStream() throws IOException {
            InputStream is = this.resource.getInputStream();
            if (is.skip(this.start) < this.start) {
                try {
                    is.close();
                } catch (IOException ignored) {
                }
                throw new IOException("Failed to skip to start of range for resource: " + this.resource.getDescription());
            }
            return new LimitedInputStream(is, this.length);
        }

        @Override
        public long contentLength() {
            return this.length;
        }

        @Override
        public String getDescription() {
            return this.resource.getDescription() + " (range: " + this.start + " to " + (this.start + this.length - 1) + ")";
        }

        @Override
        public boolean isReadable() {
            return this.resource.isReadable();
        }
    }


    /**
     * 一个内部辅助类，它包装了一个现有的 {@link InputStream}，并限制从其读取的字节总数。
     * <p>
     * 当到达指定的字节限制后，该流的表现如同到达了末尾（read() 返回 -1）。
     * 这对于精确控制从流中读取的数据量非常有用，尤其是在处理文件范围时。
     * </p>
     */
    private static class LimitedInputStream extends FilterInputStream {
        private long remaining;

        LimitedInputStream(InputStream in, long length) {
            super(in);
            this.remaining = length;
        }

        @Override
        public int read() throws IOException {
            if (remaining <= 0) {
                return -1; // 没有更多字节可读
            }
            int result = super.read();
            if (result != -1) {
                remaining--;
            }
            return result;
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            if (remaining <= 0) {
                return -1; // 没有更多字节可读
            }
            // 调整读取长度，不超过剩余字节数
            int actualLen = (int) Math.min(len, remaining);
            int result = super.read(b, off, actualLen);
            if (result != -1) {
                remaining -= result;
            }
            return result;
        }

        @Override
        public long skip(long n) throws IOException {
             if (n <= 0) {
                return 0;
            }
            // 限制跳过的字节数不超过剩余字节数
            long actualSkip = Math.min(n, remaining);
            long skipped = super.skip(actualSkip);
            remaining -= skipped;
            return skipped;
        }

        @Override
        public int available() throws IOException {
            // 可用字节数是底层流的可用字节数和剩余字节数中的较小值
            return (int) Math.min(super.available(), remaining);
        }
    }
}
