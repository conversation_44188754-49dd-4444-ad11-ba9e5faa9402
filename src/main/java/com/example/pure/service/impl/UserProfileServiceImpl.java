package com.example.pure.service.impl;


import com.example.pure.exception.BusinessException;

import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.mapper.primary.UserProfileMapper;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.service.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class UserProfileServiceImpl implements UserProfileService {

    private final UserProfileMapper userProfileMapper;
    private final UserMapper userMapper; // 用于验证用户是否存在

    @Autowired
    public UserProfileServiceImpl(UserProfileMapper userProfileMapper, UserMapper userMapper) {
        this.userProfileMapper = userProfileMapper;
        this.userMapper = userMapper;
    }

    @Override
    public UserProfile findUserProfileByUserId(Long userId) {
        log.debug("根据用户 ID 查找用户详情: {}", userId);
        return userProfileMapper.findUserProfileByUserId(userId);
    }

    @Override
    public UserProfile findUserProfileByUsername(String username) {
        log.debug("根据用户名查找用户详情: {}", username);
        return userProfileMapper.findUserProfileByUsername(username);
    }

    @Override
    @Transactional // 事务管理
    public int createNewUserProfile(UserProfile userProfile) {
        if (userProfile == null || userProfile.getId() == null) {
            throw new BusinessException("创建用户详情失败：缺少用户信息或用户ID");
        }

        // 检查是否已存在该用户的详情
        if (userProfileMapper.findUserProfileByUserId(userProfile.getId()) != null) {
            log.warn("尝试为用户ID {} 创建详情，但详情已存在。", userProfile.getId());
             // 根据业务决定是更新还是抛异常，此处选择抛异常，因为这是创建操作
             throw new BusinessException(String.format("创建用户详情失败：用户ID %d 已存在详情记录", userProfile.getId()));
        }

        int infoRowsAffected = userProfileMapper.createNewUserProfile(userProfile);
        log.debug("用户详情创建成功 (user_info table). user ID: {}", userProfile.getId());
        return infoRowsAffected;
    }

    @Override
    @Transactional // 事务管理
    public void updateUserProfileByUserId(UserProfile userProfile) {
        if (userProfile == null || userProfile.getId() == null) {
            throw new BusinessException("更新用户详情失败：缺少用户信息或用户ID");
        }



        int rowsAffected = userProfileMapper.updateUserProfileByUserId(userProfile);
        if (rowsAffected == 0) {
            // 检查详情记录是否存在，如果不存在则提示更明确
            if (userProfileMapper.findUserProfileByUserId(userProfile.getId()) == null) {
                 log.warn("尝试更新用户详情失败，因为用户ID {} 不存在对应的详情记录。", userProfile.getId());
                 throw new BusinessException("更新用户详情失败，未找到对应详情记录");
            } else {
                log.warn("尝试更新用户详情，但未发生更改 (可能数据未变). user ID: {}", userProfile.getId());
            }
        } else {
            log.debug("用户详情更新成功 (user_info table). user ID: {}", userProfile.getId());
        }
    }

    @Override
    @Transactional
    public void updateUserProfileByUsername(UserProfile userProfile) {
         if (userProfile == null || userProfile.getUsername() == null || userProfile.getUsername().trim().isEmpty()) {
            throw new BusinessException("更新用户详情失败：缺少用户信息或用户名");
        }

        int rowsAffected = userProfileMapper.updateUserProfileByUserUsername(userProfile); // 使用 Mapper 中已存在的方法
        if (rowsAffected == 0) {
             // 检查详情记录是否存在
            if (userProfileMapper.findUserProfileByUsername(userProfile.getUsername()) == null) {
                 log.warn("尝试更新用户详情失败，因为用户名 '{}' 不存在对应的详情记录。", userProfile.getUsername());
                 throw new BusinessException("更新用户详情失败，未找到对应详情记录");
            } else {
                log.warn("尝试更新用户详情，但未发生更改 (可能数据未变). Username: {}", userProfile.getUsername());
            }
        } else {
            log.debug("用户详情更新成功 (User_info table). Username: {}", userProfile.getUsername());
        }
    }

     @Override
     @Transactional
     public void deleteUserProfileByUserId(Long UserId) {
         if (UserId == null) {
             throw new BusinessException("删除用户详情失败：用户ID不能为空");
         }
         log.debug("尝试删除用户详情 for User ID: {}", UserId);
         int detailsDeleted = userProfileMapper.deleteUserProfileByUserId(UserId);
         if (detailsDeleted == 0) {
             // 这不一定是错误，可能用户就没有详情记录
             log.warn("未找到或删除用户详情记录 for User ID: {}. 可能不存在.", UserId);
         } else {
             log.debug("用户详情记录已删除 for User ID: {}", UserId);
         }
     }

     @Override
     public Long findUserIdByEmail(String email) {
         if (email == null || email.trim().isEmpty()) {
             return null;
         }
         log.debug("根据邮箱查找用户ID: {}", email);
         return userProfileMapper.findUserIdByEmail(email);
     }
}
