package com.example.pure.service.impl;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.PageResult;
import com.example.pure.event.CommentLikedEvent;
import com.example.pure.event.CommentReplyEvent;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.PureVideoInteractionMapper;
import com.example.pure.mapper.primary.VideoCommentInteractionMapper;
import com.example.pure.model.dto.*;
import com.example.pure.model.entity.VideoCommentLikes;
import com.example.pure.service.VideoCommentInteractionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 视频评论互动服务实现类。
 * <p>
 * 实现了 {@link VideoCommentInteractionService} 接口，
 * 负责处理所有与视频评论相关的业务逻辑，包括：
 * <ul>
 *     <li>评论的创建、查询和删除</li>
 *     <li>对评论的点赞和踩</li>
 *     <li>发布评论相关的应用事件（如回复、点赞）</li>
 * </ul>
 * </p>
 */
@Service
@Slf4j
public class VideoCommentInteractionServiceImpl implements VideoCommentInteractionService {


    private final VideoCommentInteractionMapper videoCommentInteractionMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public VideoCommentInteractionServiceImpl(

            VideoCommentInteractionMapper videoCommentInteractionMapper,
            ApplicationEventPublisher eventPublisher) {
        this.videoCommentInteractionMapper = videoCommentInteractionMapper;
        this.eventPublisher = eventPublisher;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createComment(VideoCommentRequest videoCommentRequest) {
        int rowsAffected = videoCommentInteractionMapper.insertVideoComments(videoCommentRequest);
        if (rowsAffected == 0) {
            throw new RuntimeException("创建评论失败");
        }
        log.info("视频 {} 下，用户 {} 发表评论成功", videoCommentRequest.getVideoEpisodesId(), videoCommentRequest.getUserId());

        if (videoCommentRequest.getParentCommentId() != null) {
            Long parentCommentAuthorId =videoCommentInteractionMapper.getUserIdByCommentId(videoCommentRequest.getParentCommentId());
            if (parentCommentAuthorId != null && !parentCommentAuthorId.equals(videoCommentRequest.getUserId())) {
                CommentReplyEvent event = new CommentReplyEvent(
                        this,
                        videoCommentRequest.getUserId(),
                        videoCommentRequest.getParentCommentId(),
                        videoCommentRequest.getContent(),
                        videoCommentRequest.getVideoEpisodesId()
                );
                eventPublisher.publishEvent(event);
                log.info("已发布评论回复事件：父评论ID {}，回复者ID {}，父评论作者ID {}",
                        videoCommentRequest.getParentCommentId(), videoCommentRequest.getUserId(), parentCommentAuthorId);
            } else if (parentCommentAuthorId == null) {
                log.warn("未找到父评论作者：父评论ID {}。未发布事件。", videoCommentRequest.getParentCommentId());
            } else {
                log.info("用户 {} 回复了自己的评论 (父评论ID: {})。未发布事件。", videoCommentRequest.getUserId(), videoCommentRequest.getParentCommentId());
            }
        }
    }

    /**
     * {@inheritDoc}
     * @implNote 当前实现的分页总数（total）获取方式不够精确，它仅返回了当前页的记录数。
     * 在生产环境中，应改为执行一次额外的 `COUNT(*)` 查询来获取总记录数，以确保分页信息的准确性。
     */
    @Override
    @Transactional(readOnly = true)
    public PageFinalResult<VideoCommentsDTO> getComments(VideoComentsPageRequest videoComentsPageRequest,Long userId) {
        int offset = videoComentsPageRequest.getOffset();
        int limit = videoComentsPageRequest.getPageSize();
        Long videoEpisodesId = videoComentsPageRequest.getVideoEpisodesId();

        // 1.获取基础数据
        List<VideoCommentsDTO> videoCommentsList = videoCommentInteractionMapper.getBasicComments(offset, limit, videoEpisodesId);

        // 2.提取评论ID
        List<Long> commentIds = videoCommentsList.stream()
                .map(VideoCommentsDTO::getId)
                .collect(Collectors.toList());

        // 3. 批量获取点赞数据
        List<CommentLikeData> likeDataList = videoCommentInteractionMapper.getCommentsLikeData(commentIds, userId);

        // 4. 转换为Map便于查找（纯内存操作，不涉及数据库）
        Map<Long, CommentLikeData> likeDataMap = likeDataList.stream()
                .collect(Collectors.toMap(CommentLikeData::getCommentId, Function.identity()));
        // 5. 合并数据
        videoCommentsList.forEach(comment -> {
            CommentLikeData likeData = likeDataMap.get(comment.getId());
            if (likeData != null) {
                comment.setLikesCount(likeData.getLikesCount());
                comment.setDislikesCount(likeData.getDislikesCount());
                comment.setUserLikeType(likeData.getUserLikeType());
                comment.setLikeStatus("like".equals(likeData.getUserLikeType()));
            } else {
                // 设置默认值
                comment.setLikesCount(0L);
                comment.setDislikesCount(0L);
                comment.setLikeStatus(false);
            }
        });

        int total = videoCommentsList.size(); // This should be a COUNT(*) query for accuracy
        PageResult pageResult = PageResult.of(videoComentsPageRequest.getPageNum(), videoComentsPageRequest.getPageSize(), total);
        return new PageFinalResult<>(videoCommentsList, pageResult);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteComment(Long commentId) {
        int rowsAffected = videoCommentInteractionMapper.deleteComment(commentId);
        if (rowsAffected == 0) {
            throw new RuntimeException("删除评论失败");
        }
    }

    /**
     * {@inheritDoc}
     * <p>
     * 核心逻辑：
     * <ol>
     *     <li>检查目标评论是否存在，防止对空目标进行操作。</li>
     *     <li>查询用户是否已有对此评论的操作记录（点赞或踩）。</li>
     *     <li><b>首次操作</b>：创建新的点赞/踩记录。如果是首次点赞，则发布一个 {@link CommentLikedEvent} 事件。</li>
     *     <li><b>已有记录</b>：
     *         <ul>
     *             <li>如果操作类型与记录相同（如重复点赞），则翻转状态（点赞 -> 取消点赞）。</li>
     *             <li>如果操作类型与记录不同（如从踩切换到赞），则更新类型，并设置状态为激活(true)。</li>
     *         </ul>
     *     </li>
     *     <li>更新记录的最后修改时间。</li>
     * </ol>
     * </p>
     */
    @Override
    @Transactional
    public void handleCommentLike(Long userId, String username, CommentLikeRequest commentLikeRequest) {
        Long commentId = commentLikeRequest.getCommentId();
        Long commentAuthorId = videoCommentInteractionMapper.getUserIdByCommentId(commentId);
        if (commentAuthorId == null) {
            throw new BusinessException("评论不存在");
        }

        VideoCommentLikes existingLike = videoCommentInteractionMapper.findByCommentIdAndUserId(commentId, userId);
        String type = commentLikeRequest.getType();

        if (ObjectUtils.isEmpty(existingLike)) {
            VideoCommentLikes newLike = new VideoCommentLikes();
            newLike.setCommentId(commentId);
            newLike.setUserId(userId);
            newLike.setType(type);
            newLike.setStatus(true);
            newLike.setCreatedTime(Instant.now());
            newLike.setUpdatedTime(Instant.now());
            videoCommentInteractionMapper.insert(newLike);
            log.info("用户 {} 首次对评论 {} 进行操作: {}", userId, commentId, type);

            if ("like".equals(type)) {
                eventPublisher.publishEvent(new CommentLikedEvent(this, userId, username, commentAuthorId, commentId));
                log.info("已为点赞操作发布 CommentLikedEvent");
            }
        } else {
            if (existingLike.getType().equals(type)) {
                existingLike.setStatus(!existingLike.getStatus());
            } else {
                existingLike.setType(type);
                existingLike.setStatus(true);
            }
            existingLike.setUpdatedTime(Instant.now());
            videoCommentInteractionMapper.update(existingLike);
            log.info("用户 {} 更新了对评论 {} 的操作状态: type={}, status={}", userId, commentId, existingLike.getType(), existingLike.getStatus());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommentLikeInfoDTO getCommentLikeInfo(Long commentId, Long userId) {
        long likeCount = videoCommentInteractionMapper.countByCommentIdAndTypeAndStatus(commentId, "like", true);
        long dislikeCount = videoCommentInteractionMapper.countByCommentIdAndTypeAndStatus(commentId, "dislike", true);

        Boolean userLikeStatus = null;
        String userLikeType = null;
        if (userId != null) {
            // 判断用户对于这条评论的点赞状态
            VideoCommentLikes userLike = videoCommentInteractionMapper.findByCommentIdAndUserId(commentId, userId);
            if (!ObjectUtils.isEmpty(userLike) && userLike.getStatus()) {
                userLikeStatus = userLike.getStatus();
                userLikeType = userLike.getType();
            }
        }

        return CommentLikeInfoDTO.builder()
                .likeCount(likeCount)
                .dislikeCount(dislikeCount)
                .userLikeType(userLikeType)
                .LikeStatus(userLikeStatus)
                .build();
    }
}
