package com.example.pure.service.impl;

import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.RecaptchaResponse;
import com.example.pure.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import reactor.core.publisher.Mono;
import java.time.Duration;
import java.util.function.Function;

/**
 * reCAPTCHA 人机验证服务的实现类。
 */
@Service
@Slf4j
public class CaptchaServiceImpl implements CaptchaService {

    private final WebClient directWebClient;
    private final WebClient proxyWebClient;
    private final String recaptchaSecretKey;
    private static final String GOOGLE_VERIFY_URL = "https://www.recaptcha.net/recaptcha/api/siteverify";

    /**
     * 构造函数，通过依赖注入初始化所需的 WebClient 实例和配置。
     * @param directWebClient 用于直接连接的 WebClient
     * @param proxyWebClient 用于通过代理连接的 WebClient
     * @param recaptchaSecretKey 从配置文件中注入的 reCAPTCHA 密钥
     */
    public CaptchaServiceImpl(@Qualifier("directWebClient") WebClient directWebClient,
                              @Qualifier("proxyWebClient") WebClient proxyWebClient,
                              @Value("${recaptcha.secret-key}") String recaptchaSecretKey) {
        this.directWebClient = directWebClient;
        this.proxyWebClient = proxyWebClient;
        this.recaptchaSecretKey = recaptchaSecretKey;
    }

    /**
     * {@inheritDoc}
     * <p>
     * 此实现的核心逻辑包括：
     * 1. 构建发送到 Google reCAPTCHA 服务器的验证请求。
     * 2. 首先尝试通过直接网络连接发送请求，并设置超时。
     * 3. 如果直接连接失败（如超时或连接异常），则自动切换到使用代理的 WebClient 进行重试。
     * 4. 解析 Google 的响应。如果响应表明验证失败，则记录日志并抛出 {@link BusinessException}。
     * 5. 所有操作都是通过响应式编程（WebClient）执行，但最终通过 {@code .block()} 方法转为同步阻塞调用，以便无缝集成到传统的同步业务流程中。
     * </p>
     */
    @Override
    public void captchaVerify(String token) {
        log.info("【reCAPTCHA服务】收到 token，准备开始验证...");

        MultiValueMap<String, String> requestMap = new LinkedMultiValueMap<>();
        requestMap.add("secret", recaptchaSecretKey);
        requestMap.add("response", token);

        // 定义一个可重用的函数接口接收T类型参数返回R类型的结果的函数，用于执行 reCAPTCHA 验证的网络请求。
        // 这样做可以避免在直连和代理逻辑中重复代码。
        Function<WebClient, Mono<RecaptchaResponse>> verifier =
                webClient -> webClient.post()
                        .uri(GOOGLE_VERIFY_URL)
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .body(BodyInserters.fromFormData(requestMap))
                        .retrieve()
                        .bodyToMono(RecaptchaResponse.class);

        // 上面定义函数接口的方法，用 verifier.apply(T的对象)来启动配配置http请求参数
        // Mono定义一个处理数据流的管道，这个管道未来可能会流过 0 或 1 个数据
        // 执行验证流程：首先尝试直接连接，如果遇到特定的网络错误，则回退到代理。
        Mono<RecaptchaResponse> verificationMono = verifier.apply(directWebClient)
                .timeout(Duration.ofSeconds(5))
                .doOnSuccess(response -> log.info("【Direct日志】Google 响应: success={}", response.isSuccess()))
                .onErrorResume(e -> {
                    // 仅在连接/读取超时等可恢复的网络问题时才尝试代理
                    if (e instanceof WebClientRequestException || e instanceof java.util.concurrent.TimeoutException) {
                        log.warn("直接连接 reCAPTCHA 失败 ({}). 正在尝试使用代理...", e.getMessage());
                        // 失败后，使用代理客户端重新发起请求
                        return verifier.apply(proxyWebClient)
                                .doOnSuccess(response -> log.info("【Proxy日志】Google 响应: success={}", response.isSuccess()));
                    }
                    // 对于其他无法恢复的错误（如JSON解析错误），直接包装为业务异常并向上抛出
                    log.error("验证过程中出现无法恢复的错误", e);
                    return Mono.error(new BusinessException("验证时出现内部错误"));
                });
        // 上面的http参数配置完后使用mono<T>的.subscribe异步或者.block同步阻塞来发送http请求
        RecaptchaResponse response = verificationMono.block();

        // 检查最终的验证结果
        if (response == null || !response.isSuccess()) {
            log.warn("人机验证失败，原因: {}", response != null ? response.getErrorCodes() : "响应为空");
            throw new BusinessException("人机验证失败");
        }

        log.info("人机验证成功");
    }
}
