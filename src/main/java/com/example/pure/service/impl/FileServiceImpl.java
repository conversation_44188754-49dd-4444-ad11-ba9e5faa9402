package com.example.pure.service.impl;

import com.example.pure.model.dto.FileMetadata;
import com.example.pure.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.*;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件服务实现类
 * <p>
 * 提供文件相关操作的实现，包括：
 * - 视频流式播放
 * - 文件下载
 * - 文件类型判断
 * - 文件名安全验证
 * </p>
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    /**
     * 文件存储位置
     */
    private final Path fileStorageLocation;

    /**
     * 允许下载的文件扩展名白名单 (安全检查)
     */
    private static final List<String> ALLOWED_EXTENSIONS =
            Arrays.asList("pdf", "txt", "docx", "xlsx", "pptx", "zip", "rar", "exe");

    /**
     * 线程池，用于异步处理文件传输
     * @deprecated 请使用Spring的TaskExecutor替代
     */
    @Deprecated
    private final ExecutorService executorService;

    /**
     * 构造函数，初始化文件存储位置和线程池
     *
     * @param fileStorageLocationStr 文件存储根目录路径
     */
    public FileServiceImpl(@Value("${file.storage.location.download}") String fileStorageLocationStr) {
        this.fileStorageLocation = Paths.get(fileStorageLocationStr).toAbsolutePath().normalize();
        // 创建固定大小的线程池，大小根据系统预期并发量调整
        // 此线程池仅用于向后兼容，新代码应使用Spring的TaskExecutor
        this.executorService = Executors.newFixedThreadPool(10);

        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (IOException e) {
            throw new RuntimeException("无法创建文件存储目录", e);
        }
    }

    /**
     * 获取视频流
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含视频流的ResponseEntity
     * @throws IOException 如果文件访问出错，抛出到访问这个方法的那个类
     */
    @Override
    public ResponseEntity<Resource> streamVideo(String fileName, HttpHeaders headers) throws IOException {
        // 安全性检查：防止目录遍历攻击
        if (!isValidFileName(fileName)) {
            throw new IOException("无效的文件名: " + fileName + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        try {
            // 构建文件路径
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();

            // 检查文件路径是否在允许的目录下
            if (!filePath.startsWith(this.fileStorageLocation)) {
                throw new IOException("访问被拒绝: 文件路径不在允许的目录范围内 - " + filePath);
            }

            // 使用 UrlResource 从文件系统加载视频文件。
            Resource video = new UrlResource(filePath.toUri());

            // 检查文件是否存在且可读
            if (!video.exists()) {
                throw new IOException("文件不存在: " + fileName);
            }

            if (!video.isReadable()) {
                throw new IOException("无法读取文件: " + fileName + "，请检查文件权限");
            }

            // 获取文件类型
            String contentType = determineContentType(filePath);

            // 实现范围请求 (Range Requests)
            long rangeStart = 0; //视频开始字节
            long rangeEnd; //视频结束字节
            long fileSize = video.contentLength();

            // 从请求头中获取Range信息
            String range = headers.getFirst(HttpHeaders.RANGE);
            // 检查range开头是否"bytes="开头这是标准的格式
            if (range != null && range.startsWith("bytes=")) {
                // 解析 Range 头部的起始和结束位置，提取除了"bytes="以外的子字符串并按子字符串里的-分开成字符串数组
                String[] ranges = range.substring("bytes=".length()).split("-");
                // 把字符串数组的第一个元素转成long类型，HTTP 协议和 Java 的 InputStream 都使用 long 类型来表示文件大小和偏移量，因为文件的大小（字节数）很容易超过 int 类型能够表示的最大值
                rangeStart = Long.parseLong(ranges[0]);
                // 检查数组是否有第二个元素（结束位置），如果有把第二个元素转long类型作为rangeEnd，如果没有第二个表示文件结尾设置fileSize-1(文件最后一个字节索引)
                rangeEnd = ranges.length > 1 ? Long.parseLong(ranges[1]) : fileSize - 1;
            } else {
                // 如果没有 Range 头部，则默认返回整个文件
                rangeEnd = fileSize - 1;
            }

            // 计算要返回的内容长度，我们要计算的是从第101个字节到第 201 个字节之间有多少个字节。如果我们只做 rangeEnd - rangeStart，结果是100，但这实际上少了 1 个字节（因为我们没有包含第 201 个字节本身）。所以我们需要加 1，得到正确的结果101。
            long contentLength = rangeEnd - rangeStart + 1;

            // 设置响应头
            return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                    .header(HttpHeaders.CONTENT_TYPE, contentType) // 设置文件类型
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength)) // 设置内容长度
                    // 设置 Content-Range 头部，指示返回的内容范围，设置 Content-Range 头部，告诉客户端返回的内容范围。格式为 "bytes 起始位置-结束位置/文件总大小"
                    .header(HttpHeaders.CONTENT_RANGE, "bytes " + rangeStart + "-" + rangeEnd + "/" + fileSize)
                    .header(HttpHeaders.ACCEPT_RANGES, "bytes") // 告诉客户端服务器支持范围请求
                    // 这是实现范围请求的关键，它会根据 rangeStart 和 contentLength 来读取和返回部分内容
                    .body(new CustomInputStreamResource(video.getInputStream(), rangeStart, contentLength)); // 创建一个 CustomInputStreamResource 对象，并将它作为响应体返回。

        } catch (MalformedURLException e) {
            throw new IOException("无效的文件路径: " + fileName, e);
        }
    }

    /**
     * 下载文件
     *
     * @param filename 文件名
     * @param headers  请求头
     * @return 包含文件内容的ResponseEntity
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> downloadFile(String filename, HttpHeaders headers) throws IOException {
        // 安全检查: 防止路径遍历攻击
        if (!isValidFileName(filename)) {
            log.info("请求下载的文件类型错误,{}", filename);
            throw new IOException("无效的文件名: " + filename + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        // 构建文件路径,路径拼接字符串为新路径
        Path filePath = this.fileStorageLocation.resolve(filename).normalize();

        // 检查文件路径是否在允许的目录下
        if (!filePath.startsWith(this.fileStorageLocation)) {
            log.info("请求下载的文件路径错误,{}", filePath);
            throw new IOException("访问被拒绝: 文件路径不在允许的目录范围内 - " + filePath);
        }

        File file = filePath.toFile();

        // 检查文件是否存在
        if (!file.exists() || !file.isFile()) {
            throw new IOException("文件不存在: " + filename);
        }

        // 安全检查: 检查文件扩展名是否在白名单中
        String fileExtension = getFileExtension(filename);
        if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
            throw new IOException("不支持的文件类型: " + fileExtension + "。允许的文件类型: " + String.join(", ", ALLOWED_EXTENSIONS));
        }

        // 获取文件MIME类型
        String contentType = determineContentType(filePath);

        // 准备流式响应,读取流二进制文件
        InputStream inputStream = new FileInputStream(file);
        InputStreamResource inputStreamResource = new InputStreamResource(inputStream);

        // 设置HTTP头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\""); // 设置附件下载，下载的默认文件名
        responseHeaders.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        responseHeaders.add(HttpHeaders.PRAGMA, "no-cache");
        responseHeaders.add(HttpHeaders.EXPIRES, "0");
        responseHeaders.setContentType(MediaType.parseMediaType(contentType)); //设置MIME类型才能让浏览器和客户端解析并处理

        // 构建响应
        return ResponseEntity.ok()
                .headers(responseHeaders)
                .contentLength(file.length()) // 设置文件大小,验证数据传输的完整性,客户端显示下载进度
                .body(inputStreamResource);
    }

    /**
     * 确定文件的MIME类型
     * 获取文件MIME类型，MIME类型（让浏览器识别这个类型之后，根据这个类型调用相应的事，比如说视频或图片在线观看或者下载）
     *
     * @param filePath 文件路径
     * @return 文件的MIME类型
     * @throws IOException 如果文件访问出错
     */
    @Override
    public String determineContentType(Path filePath) throws IOException {
        // 尝试使用 probeContentType 方法根据文件内容确定 MIME 类型
        String contentType = Files.probeContentType(filePath);
        if (contentType == null) {
            // 如果 probeContentType 无法确定，则根据文件扩展名手动设置
            String fileName = filePath.getFileName().toString();
            if (fileName.endsWith(".mp4")) {
                contentType = "video/mp4";
            } else if (fileName.endsWith(".webm")) {
                contentType = "video/webm";
            } else if (fileName.endsWith(".ogg")) {
                contentType = "video/ogg";
            } else if (fileName.endsWith(".mp3")) {
                contentType = "audio/mpeg";
            } else if (fileName.endsWith(".wav")) {
                contentType = "audio/wav";
            } else if (fileName.endsWith(".pdf")) {
                contentType = "application/pdf";
            } else if (fileName.endsWith(".txt")) {
                contentType = "text/plain";
            } else if (fileName.endsWith(".html") || fileName.endsWith(".htm")) {
                contentType = "text/html";
            } else if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
                contentType = "application/msword";
            } else if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx")) {
                contentType = "application/vnd.ms-excel";
            } else if (fileName.endsWith(".ppt") || fileName.endsWith(".pptx")) {
                contentType = "application/vnd.ms-powerpoint";
            } else {
                contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE; // 通用内容类型
            }
        }
        return contentType;
    }

    /**
     * 验证文件名是否合法，防止目录遍历攻击
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */
    @Override
    public boolean isValidFileName(String fileName) {
        // 定义一个正则表达式，只允许字母、数字、下划线、连字符和点
        Pattern pattern = Pattern.compile("^[a-zA-Z0-9_\\-.]+$");
        Matcher matcher = pattern.matcher(fileName);
        // 检查文件名是否匹配正则表达式，并且不包含 ".." 序列
        return matcher.matches() && !fileName.contains("..");
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }

    /*
    为什么要重写类（CustomInputStreamResource 和 LimitedInputStream）？用默认的类和重写类有什么区别？
    默认的 InputStreamResource 的行为：
    contentLength(): 默认返回底层 InputStream 的总长度（如果 InputStream 支持获取长度）。
    getInputStream(): 默认返回底层 InputStream，从 InputStream 的当前位置开始读取，直到流结束。
    重写类的目的：
    支持范围请求： 默认的 InputStreamResource 不会处理 Range 头部，它会简单地发送整个文件。重写类的目的是为了实现对 Range 请求头的处理，只发送客户端请求的那部分内容。
    精确控制： 重写类允许我们精确控制要发送的数据的起始位置和长度。
    CustomInputStreamResource 重写了：
    contentLength(): 返回实际要发送的内容长度（length），而不是底层 InputStream 的总长度。
    getInputStream():
    将底层 InputStream 跳过 start 个字节。
    创建一个 LimitedInputStream 来限制读取的字节数。
    LimitedInputStream 重写了：
    read() (单字节读取) 和 read(byte[] b, int off, int len) (多字节读取): 限制从底层 InputStream 中读取的总字节数不超过构造函数中指定的 length。
     */

    /**
     * 自定义InputStreamResource类，支持范围请求
     */
    public static class CustomInputStreamResource extends InputStreamResource {
        private final long start;
        private final long length;

        public CustomInputStreamResource(InputStream inputStream, long start, long length) {
            super(inputStream);
            this.start = start;
            this.length = length;
        }

        @Override
        //重写 contentLength() 方法，返回 length（要返回的内容长度），而不是底层 InputStream 的总长度
        public long contentLength() {
            return length;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            InputStream originalInputStream = super.getInputStream();
            // 将 InputStream 的读取位置跳过 rangeStart 个字节，定位到请求范围的起始位置
            originalInputStream.skip(start);
            // 将 originalInputStream和length传递给LimitedInputStream会限制从originalInputStream中读取的字节数，确保只读取请求范围内的内容。
            return new LimitedInputStream(originalInputStream, length);
        }

        /**
         * 限制InputStream读取长度的内部类
         */
        private static class LimitedInputStream extends FilterInputStream {
            private long remaining;// 存储还可以读取的字节数

            //将从originalInputStream的当前位置跳过的字节例如（总字节是1000B的视频，跳到第100个字节）开始播放视频，length是本次请求最多加载字节比如说200，100+200=RangeEnd等于本次加载结束为300字节
            LimitedInputStream(InputStream in, long length) {
                super(in);
                this.remaining = length;
            }

            @Override//重写 read() 方法（读取单个字节）
            public int read() throws IOException {
                if (remaining == 0) { // 如果 remaining 为 0（表示已经读取了足够多的字节），则返回 -1（表示流结束）。
                    return -1;
                }
                // 调用父类（FilterInputStream）的 read() 方法，从底层 InputStream 中读取一个字节。
                int result = super.read();
                // 如果读取成功（result 不是 -1），则将 remaining 减 1。
                if (result != -1) {
                    remaining--;
                }
                return result;
            }

            // 重写read方法，读取多个字节到数组b
            @Override
            public int read(byte[] b, int off, int len) throws IOException {
                if (remaining == 0) { // 如果 remaining 为 0（表示已经读取了足够多的字节），则返回 -1（表示流结束）。
                    return -1;
                }
                // 读取长度len不能超过remaining
                len = (int) Math.min(len, remaining);
                // 调用父类（FilterInputStream）的 read() 方法，从底层 InputStream 中读取多个字节
                int result = super.read(b, off, len);
                if (result != -1) { // 如果读取成功,则将 remaining 减少result
                    remaining -= result;
                }
                return result;
            }
        }
    }

    /**
     * 流式传输文件（通用方法，适用于任何文件类型）
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含文件流的Resource对象
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> streamFile(String fileName, HttpHeaders headers) throws IOException {
        // 安全性检查：防止目录遍历攻击
        if (!isValidFileName(fileName)) {
            throw new IOException("无效的文件名: " + fileName + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        try {
            // 构建文件路径
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();

            // 检查文件路径是否在允许的目录下
            if (!filePath.startsWith(this.fileStorageLocation)) {
                throw new IOException("访问被拒绝: 文件路径不在允许的目录范围内 - " + filePath);
            }

            // 使用 UrlResource 从文件系统加载文件
            Resource file = new UrlResource(filePath.toUri());

            // 检查文件是否存在且可读
            if (!file.exists()) {
                throw new IOException("文件不存在: " + fileName);
            }

            if (!file.isReadable()) {
                throw new IOException("无法读取文件: " + fileName + "，请检查文件权限");
            }

            // 获取文件类型
            String contentType = determineContentType(filePath);

            // 实现范围请求 (Range Requests)
            long rangeStart = 0; // 文件开始字节
            long rangeEnd; // 文件结束字节
            long fileSize = file.contentLength();

            // 从请求头中获取Range信息
            String range = headers.getFirst(HttpHeaders.RANGE);
            if (range != null && range.startsWith("bytes=")) {
                // 清除range开头的"bytes="
                String rangeValue = range.substring("bytes=".length());

                // 解析范围值
                String[] ranges = rangeValue.split("-");
                rangeStart = Long.parseLong(ranges[0]); // 起始字节

                // 如果指定了结束字节，则使用指定值；否则使用文件末尾
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]);
                } else {
                    rangeEnd = fileSize - 1; // 到文件末尾
                }

                // 如果范围超出了文件大小，调整为文件大小
                if (rangeEnd >= fileSize) {
                    rangeEnd = fileSize - 1;
                }

                if (rangeStart >= fileSize) {
                    // 如果起始位置超出了文件大小，返回416的状态码（请求范围不满足）
                    return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                            .header(HttpHeaders.CONTENT_RANGE, "bytes */" + fileSize)
                            .body(null);
                }

                // 创建包含指定范围的输入流
                InputStream inputStream = file.getInputStream();
                // 跳过起始字节前的内容
                inputStream.skip(rangeStart);
                // 创建范围输入流，只读取到结束字节
                CustomInputStreamResource.LimitedInputStream limitedInputStream =
                    new CustomInputStreamResource.LimitedInputStream(inputStream, rangeEnd - rangeStart + 1);
                // 使用InputStreamResource包装范围输入流
                Resource rangeResource = new InputStreamResource(limitedInputStream);

                // 设置Content-Range头和状态码
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                        .header(HttpHeaders.CONTENT_RANGE, "bytes " + rangeStart + "-" + rangeEnd + "/" + fileSize)
                        .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(rangeEnd - rangeStart + 1)
                        .body(rangeResource);
            } else {
                // 不是范围请求，返回整个文件
                return ResponseEntity.ok()
                        .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                        .contentType(MediaType.parseMediaType(contentType))
                        .contentLength(fileSize)
                        .body(file);
            }
        } catch (MalformedURLException e) {
            throw new IOException("文件路径无效: " + fileName, e);
        }
    }

    /**
     * 最佳实践如果需要拿到异步数据的话，必须让异步获得的数据返回主线程，再从主线程返回客户端
     * 异步流式传输文件（通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     *
     * Async注解启用异步调用一个子线程运行方法里的任务，CompletableFuture.runAsync启用线程池调用异步多个子线程运行任务
     * 如果没 Async注解就是主线程运行方法的任务直到CompletableFuture.runAsync从线程池调用异步任务，然后主线程跳过异步任务运行这个页面方法内的其他代码
     * 这写代码是主线程先返回空响应求体数据给客户端形成握手，后续多线程异步再通过emitter.send发送数据到响应体就能持续发送数据
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers) {
        try {
            log.debug("开始异步处理文件流(fileTaskExecutor): {}", fileName);

            // 获取文件元数据
            FileMetadata metadata = getFileMetadata(fileName);

            if (!metadata.isExists()) {
                throw new IOException("文件不存在: " + fileName);
            }

            if (!metadata.isReadable()) {
                throw new IOException("无法读取文件: " + fileName + "，请检查文件权限");
            }

            // 构建文件路径并创建资源
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            Resource file = new UrlResource(filePath.toUri());

            // 解析Range头信息
            long fileSize = metadata.getFileSize();
            String range = headers.getFirst(HttpHeaders.RANGE);
            long rangeStart = 0;
            long rangeEnd = fileSize - 1;

            if (range != null && range.startsWith("bytes=")) {
                String[] ranges = range.substring("bytes=".length()).split("-");
                rangeStart = Long.parseLong(ranges[0]);
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]);
                }
            }

            // 确保范围有效
            if (rangeStart < 0) {
                rangeStart = 0;
            }

            if (rangeEnd >= fileSize) {
                rangeEnd = fileSize - 1;
            }

            // 计算要返回的内容长度
            final long contentLength = rangeEnd - rangeStart + 1;
            final long finalRangeStart = rangeStart;

            // 创建响应发射器
            ResponseBodyEmitter emitter = new ResponseBodyEmitter();

              /* @async的子线程返回未处理数据的存储二进制流类给主线程return CompletableFuture.completedFuture(emitter)，
                让主线程提交数据这个类给客户端形成握手并连接，后续通过runAsync调用线程池启用的子线程持续发送数据到客户端
                ()->{}括号内为传参，{}为运行的方法，debug只能单独在那个线程的代码里添加点监控，也就是正在监控主线程不能跳到子线程监控 */
            CompletableFuture.runAsync(() -> {

                // try () try-with-resources 括号内初始化初始化的对象try语句结束后自动关闭初始化的对象，不需要finally内使用AutoCloseable接口关闭
                try (InputStream inputStream = file.getInputStream()) {
                    // 跳过开始部分，也就是这个数量的字节
                    long bytesSkipped = inputStream.skip(finalRangeStart); // 跳到开始部分的字节
                    if (bytesSkipped < finalRangeStart) {
                        throw new IOException("无法跳过指定的字节数: " + finalRangeStart);
                    }

                    // 发送逻辑:用inputStream把文件数据写入到字节数组里去，然后通过emitter.send（data）发送
                    // 设置缓冲区大小，缓冲区: 创建一个大小为 8192 字节（8KB）的字节数组 buffer。这个缓冲区将用于分块从 InputStream 中读取数据。
                    byte[] buffer = new byte[8192];
                    int bytesRead; // 用于存储每次 inputStream.read() 操作实际读取的字节数
                    long bytesRemaining = contentLength; // 剩余字节数量
                    long totalBytesRead = 0; // 记录已发送到客户端的总字节数

                     /* 分块发送数据
                       尝试从 inputStream 中读取数据，最多读取 buffer 的容量或者 bytesRemaining 两者之间较小的值的字节数，
                       并将读取到的数据存放到 buffer 数组中，从索引 0 开始。如果每次实际读取的数据不等于-1（输入流没有更多数据可读了）就继续遍历 */
                    while ((bytesRead = inputStream.read(buffer, 0, (int) Math.min(buffer.length, bytesRemaining))) != -1) { // 先计算=，再计算！=1

                        /* 1.性能优化,防止文件剩余字节小于缓存区，所以重新赋值节省数组
                           2.把刚刚获取到的字节数据数组复制到新数组里去，从buffer数组，从索引0，复制到dataChunk，粘贴起始索引位置，要复制的元素数量 */
                        byte[] dataChunk = new byte[bytesRead];
                        System.arraycopy(buffer, 0, dataChunk, 0, bytesRead);

                         /* 数据块写入 HTTP 响应输出流到响应体，通过主线程返回的http请求建立的
                         长连接里的响应体里的emitter发送数据给客户端形成逐步传输数据 */
                        emitter.send(dataChunk);

                        totalBytesRead += bytesRead;  // 更新已发送的总字节
                        bytesRemaining -= bytesRead; // 更新剩余字节

                        // 如果已发送足够多的字节，则结束
                        if (bytesRemaining <= 0) {
                            break;
                        }

                        // 适当休眠，避免CPU占用过高，休眠5ms
                        try {
                            Thread.sleep(5);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            emitter.completeWithError(e);
                            return;
                        }
                    }

                    // 成功完成数据发送，标记数据流的结束，通知客户端停止等待接受数据
                    emitter.complete();
                    log.debug("文件流传输完成: {}, 总字节数: {}", fileName, totalBytesRead);

                } catch (IOException e) {
                    // 处理客户端中断异常
                    handleStreamException(emitter, e, fileName, "文件流");
                }
            });

            // 返回已完成状态的Future，会让承诺未来完成类变成（已完成状态）实际数据会异步发送
            return CompletableFuture.completedFuture(emitter);

        } catch (Exception e) {
            log.error("准备文件流时出错: {}", fileName, e);
            CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 异步下载文件（带选项）
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行文件下载，避免阻塞请求处理线程
     * 支持自定义选项，如缓冲区大小等
     * </p>
     *
     * @param filename 文件名
     * @param headers  请求头，用于处理Range请求和其他HTTP头信息
     * @param options  下载选项，可包含缓冲区大小等参数
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> downloadFileAsync(String filename, HttpHeaders headers, java.util.Map<String, Object> options) {
        try {
            log.debug("开始异步处理文件下载(带选项，fileTaskExecutor): {}", filename);

            // 获取文件元数据
            FileMetadata metadata = getFileMetadata(filename);

            if (!metadata.isExists()) {
                throw new IOException("文件不存在: " + filename);
            }

            if (!metadata.isReadable()) {
                throw new IOException("无法读取文件: " + filename + "，请检查文件权限");
            }

            // 安全检查: 检查文件扩展名是否在白名单中
            String fileExtension = getFileExtension(filename);
            if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
                throw new IOException("不支持的文件类型: " + fileExtension + "。允许的文件类型: " + String.join(", ", ALLOWED_EXTENSIONS));
            }

            // 构建文件路径
            final Path filePath = this.fileStorageLocation.resolve(filename).normalize();
            final File file = filePath.toFile();

            // 解析Range头信息
            long fileSize = metadata.getFileSize();
            String range = headers.getFirst(HttpHeaders.RANGE);
            long rangeStart = 0;  // 文件开始字节
            long rangeEnd = fileSize - 1; // 文件结束字节

            if (range != null && range.startsWith("bytes=")) {
                String[] ranges = range.substring("bytes=".length()).split("-");
                rangeStart = Long.parseLong(ranges[0]); // 有范围信息的起始字节
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]); // 有范围信息的结束字节
                }
            }

            // 确保范围有效
            if (rangeStart < 0) {
                rangeStart = 0;
            }

            if (rangeEnd >= fileSize) {
                rangeEnd = fileSize - 1;
            }

            /* range里实际内容大概bytes=0=3111，包含0，因为计算机内存第一个存储数据的地址是0，计算机内存的寻址是
            基于地址的。总字节=rangeEnd - rangeStart + 1因为请求范围的字节0也包含在字节里,所以总长度-1等于最大请求字节 */

            // 计算要返回的内容长度
            final long contentLength = rangeEnd - rangeStart + 1;
            final long finalRangeStart = rangeStart;

            // 创建响应发射器
            ResponseBodyEmitter emitter = new ResponseBodyEmitter();

            // 从options中获取缓冲区大小，如果没有指定则使用默认值
            final int bufferSize = options != null && options.containsKey("bufferSize") ?
                                   (int) options.get("bufferSize") : 8192*2; // 默认8KB

            // 从options中获取线程休眠时间，控制发送速度，如果没有指定则使用默认值
            final long sleepTime = options != null && options.containsKey("sleepTime") ?
                                  (long) options.get("sleepTime") : 5; // 默认5毫秒

            log.debug("文件下载使用的缓冲区大小: {} 字节", bufferSize);

            //  主线程调用异步后运行return CompletableFuture.completedFuture(emitter),让子线程来执行异步任务
            CompletableFuture.runAsync(() -> {
                try (InputStream inputStream = new FileInputStream(file)) {
                    // 跳过这个数量的字节
                    long bytesSkipped = inputStream.skip(finalRangeStart);
                    if (bytesSkipped < finalRangeStart) {
                        throw new IOException("无法跳过指定的字节数: " + finalRangeStart);
                    }

                    // 使用指定的缓冲区大小
                    byte[] buffer = new byte[bufferSize]; // 设置缓冲区大小，缓冲区: 创建一个大小为 8192 字节（8KB）的字节数组 buffer。这个缓冲区将用于分块从 InputStream 中读取数据。
                    int bytesRead;
                    long bytesRemaining = contentLength;
                    long totalBytesRead = 0;

                    /* 分块发送数据
                       尝试从 inputStream 中读取数据，最多读取 buffer 的容量或者 bytesRemaining 两者之间较小的值的字节数，
                       并将读取到的数据存放到 buffer 数组中，从索引 0 开始。如果每次实际读取的数据不等于-1（输入流没有更多数据可读了）就继续遍历 */
                    while ((bytesRead = inputStream.read(buffer, 0, (int) Math.min(buffer.length, bytesRemaining))) != -1) {

                        /* 1.性能优化,防止文件剩余字节小于缓存区，所以重新赋值节省数组
                           2.把刚刚获取到的字节数据数组复制到新数组里去，从buffer数组，从索引0，复制到dataChunk，粘贴起始索引位置，要复制的元素数量 */
                        byte[] dataChunk = new byte[bytesRead];
                        System.arraycopy(buffer, 0, dataChunk, 0, bytesRead);

                         /* 数据块写入 HTTP 响应输出流到响应体，通过主线程返回的http请求建立的
                         长连接里的响应体里的emitter发送数据给客户端形成逐步传输数据 */
                        emitter.send(dataChunk);

                        totalBytesRead += bytesRead; // 更新已发送的总字节
                        bytesRemaining -= bytesRead; // 更新剩余字节

                        // 如果已发送足够多的字节，则结束
                        if (bytesRemaining <= 0) {
                            break;
                        }

                        // 适当休眠，避免CPU占用过高，使用指定的休眠时间
                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            emitter.completeWithError(e);
                            return;
                        }
                    }

                    // 成功完成数据发送，标记数据流的结束，通知客户端停止等待接受数据
                    emitter.complete();
                    log.debug("文件下载完成: {}, 总字节数: {}", filename, totalBytesRead);

                } catch (IOException e) {
                    // 处理客户端中断异常
                    handleStreamException(emitter, e, filename, "文件下载");
                }
            });

            // 返回已完成状态的Future，实际数据会异步发送
            return CompletableFuture.completedFuture(emitter);

        } catch (Exception e) {
            log.error("准备文件下载时出错: {}", filename, e);
            CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 获取文件元数据信息
     *
     * @param filename 文件名
     * @return 文件元数据对象
     * @throws IOException 如果文件访问出错
     */
    @Override
    public FileMetadata getFileMetadata(String filename) throws IOException {
        // 安全检查: 防止路径遍历攻击
        if (!isValidFileName(filename)) {
            throw new IOException("无效的文件名: " + filename + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        // 构建文件路径
        Path filePath = this.fileStorageLocation.resolve(filename).normalize();

        // 检查文件路径是否在允许的目录下
        if (!filePath.startsWith(this.fileStorageLocation)) {
            throw new IOException("访问被拒绝: 文件路径不在允许的目录范围内 - " + filePath);
        }
        // 路径转为文件
        File file = filePath.toFile();

        // 初始化元数据
        FileMetadata metadata = new FileMetadata();
        metadata.setFileName(filename);
        metadata.setExists(file.exists() && file.isFile());
        metadata.setReadable(file.canRead());

        if (metadata.isExists() && metadata.isReadable()) {
            // 获取文件大小
            metadata.setFileSize(file.length());

            // 获取内容类型ContentType,让浏览器知道类型类自动处理文件
            String contentType = determineContentType(filePath);
            metadata.setContentType(contentType);
        } else {
            metadata.setFileSize(0);
            metadata.setContentType("application/octet-stream");
        }

        return metadata;
    }

    /**
     * 处理流传输过程中的异常
     *
     * @param emitter  响应体发射器
     * @param e        异常
     * @param filename 文件名
     * @param type     操作类型描述
     */
    private void handleStreamException(ResponseBodyEmitter emitter, IOException e, String filename, String type) {
        // 检查是否是客户端中断连接
        if (e.getMessage() != null && (
                e.getMessage().contains("Connection reset by peer") ||
                e.getMessage().contains("Broken pipe") ||
                e.getMessage().contains("An established connection was aborted"))) {

            log.warn("客户端中断{}连接: {}", type, e.getMessage());
        } else {
            log.error("{}传输出错: {}", type, filename, e);
        }

        // 无论如何都要完成发射器，防止资源泄漏
        emitter.completeWithError(e);
    }

    /**
     * 异步流式传输视频
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行视频流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamVideoAsync(String fileName, HttpHeaders headers) {
        // 直接调用通用的文件流方法
        log.debug("调用视频流异步处理方法，将委托给通用文件流处理方法: {}", fileName);
        return streamFileAsync(fileName, headers);
    }

    @Async("fileTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> downloadFileAsync(String filename, HttpHeaders headers) {
        // 调用带选项的方法，传递默认选项
        log.debug("调用文件下载异步处理方法，将委托给带选项的文件下载处理方法: {}", filename);
        return downloadFileAsync(filename, headers, null);
    }
}
