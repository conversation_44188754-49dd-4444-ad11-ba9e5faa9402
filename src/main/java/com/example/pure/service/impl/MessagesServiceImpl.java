package com.example.pure.service.impl;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.PageResult;
import com.example.pure.constant.MessageConstants;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.mapper.primary.MessagesMapper;
import com.example.pure.mapper.primary.UserProfileMapper;
import com.example.pure.model.dto.MessageDTO;
import com.example.pure.model.dto.PageRequestDTO;
import com.example.pure.model.dto.SystemMessageRequestDTO;
import com.example.pure.model.dto.UnreadCountDTO;
import com.example.pure.model.entity.Message;
import com.example.pure.model.entity.UserMessage;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.service.MessagesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MessagesService 的实现。
 */
@Service
public class MessagesServiceImpl implements MessagesService {

    private final MessagesMapper messagesMapper;
    private final UserProfileMapper userProfileMapper;

    @Autowired
    public MessagesServiceImpl(MessagesMapper messagesMapper, UserProfileMapper userProfileMapper) {
        this.messagesMapper = messagesMapper;
        this.userProfileMapper = userProfileMapper;
    }

    /**
     * 发送系统消息给指定用户。
     *
     * @param messageRequestDTO 包含消息接收者ID、标题、内容和类型的DTO。
     */
    @Override
    @Transactional // 确保消息插入和用户消息插入的原子性
    public void sendSystemMessage(SystemMessageRequestDTO messageRequestDTO) {
        // 1. DTO 转 Message 实体
        Message message = Message.builder()
                .senderId(SecurityConstants.ADMIN_ID) // 系统发送者ID，可以根据实际情况调整
                .targetType("USER") // 目标类型为用户
                .targetId(messageRequestDTO.getRecipientUserId()) // 目标ID为接收者用户ID
                .title(messageRequestDTO.getTitle()) // 消息标题
                .content(messageRequestDTO.getContent()) // 消息内容
                .messageType(messageRequestDTO.getMessageType()) // 消息类型
                .createdTime(LocalDateTime.now()) // 消息创建时间
                .build();

        // 2. 保存 Message 实体到数据库，并获取生成的主键
        messagesMapper.insertMessage(message);
        Long messageId = message.getId(); // 获取插入后返回的主键

        // 3. 创建 UserMessage 实体
        UserMessage userMessage = UserMessage.builder()
                .userId(messageRequestDTO.getRecipientUserId()) // 用户ID
                .messageId(messageId) // 关联的消息ID
                .isRead(false) // 初始状态为未读
                .readTime(null) // 阅读时间初始为null
                .build();

        // 4. 保存 UserMessage 实体到数据库
        messagesMapper.insertUserMessage(userMessage);
    }

    /**
     * 发送评论回复通知给被回复的评论的作者。
     *
     * @param replierUserId       回复者用户ID
     * @param parentCommentAuthorId 被回复的评论的作者ID
     * @param commentContent      评论内容
     * @param videoEpisodesId     视频剧集ID
     */
    @Override
    @Transactional
    public void sendCommentReplyNotification(Long replierUserId, Long parentCommentAuthorId, String commentContent, Long videoEpisodesId) {
        UserProfile replierProfile = userProfileMapper.findUserProfileByUserId(replierUserId);
        String replierUsername = (replierProfile != null && replierProfile.getUsername() != null) ? replierProfile.getUsername() : "用户" + replierUserId;

        String title = replierUsername + " 回复了您的评论";
        // 您可以根据需要向内容中添加更多详细信息，例如指向视频剧集的链接
        // String content = "回复内容：" + commentContent + "\n所属视频剧集ID：" + videoEpisodesId;
        String content = "回复内容：" + commentContent; // 简化版内容，仅包含回复本身

        Message message = Message.builder()
                .senderId(replierUserId) // 发送者是评论回复者
                .targetType("USER") // 目标类型为用户
                .targetId(parentCommentAuthorId) // 目标ID为被回复的评论的作者
                .title(title) // 消息标题
                .content(content) // 消息内容
                .messageType(MessageConstants.MESSAGE_TYPE_NOTIFICATION) // 消息类型
                .createdTime(LocalDateTime.now()) // 消息创建时间
                .build();

        messagesMapper.insertMessage(message);
        Long messageId = message.getId();

        UserMessage userMessage = UserMessage.builder()
                .userId(parentCommentAuthorId) // 消息是给被回复的评论的作者的
                .messageId(messageId)
                .isRead(false)
                .readTime(null)
                .build();

        messagesMapper.insertUserMessage(userMessage);
    }

    /**
     * 获取指定用户的未读消息数。
     *
     * @param userId 用户ID
     * @return 未读消息数 DTO
     */
    @Override
    public UnreadCountDTO getUnreadMessageCount(Long userId) {
        // 调用 mapper 获取未读消息数
        Long unreadCount = messagesMapper.countUnreadMessagesByUserId(userId);
        // 手动构建 DTO 对象
        return UnreadCountDTO.builder()
                .unreadCount(unreadCount != null ? unreadCount.intValue() : 0) // 将 Long 转换为 int
                .build();
    }

    /**
     * 将指定用户的所有未读消息标记为已读。
     *
     * @param userId 用户ID
     */
    @Override
    public void markAllMessagesAsRead(Long userId) {
        // 调用 mapper 将指定用户的所有未读消息标记为已读，并记录当前时间为读取时间
        messagesMapper.markAllMessagesAsReadByUserId(userId, LocalDateTime.now());
    }

    /**
     * 将指定用户的特定消息标记为已读。
     *
     * @param userId 用户ID
     * @param messageId 消息ID
     */
    @Override
    public void markMessageAsRead(Long userId, Long messageId) {
        // 调用 mapper 将指定用户的特定消息标记为已读，并记录当前时间为读取时间
        messagesMapper.markMessageAsReadByUserIdAndMessageId(userId, messageId, LocalDateTime.now());
    }

    /**
     * 获取用户消息列表 (分页)
     *
     * @param userId         用户ID
     * @param pageRequestDTO 分页请求参数
     * @param status         消息状态 ("unread", "read", "all")
     * @return 分页后的消息列表
     */
    @Override
    public PageFinalResult<MessageDTO> getUserMessages(Long userId, PageRequestDTO pageRequestDTO, String status) {
        // 1. 计算分页参数 (offset: 偏移量, limit: 每页数量)
        int offset = pageRequestDTO.getOffset();
        int limit = pageRequestDTO.getPageSize();

        // 2. 调用 Mapper 查询符合条件的消息总数
        Long totalRecords = messagesMapper.countUserMessagesWithDetails(userId, status);

        // 3. 调用 Mapper 查询当前页的消息列表
        List<MessageDTO> messageDTOList = messagesMapper.selectUserMessagesWithDetails(userId, status, offset, limit);

         UserProfile userProfile=userProfileMapper.findUserProfileByUserId(userId);
        for (MessageDTO messageDTO : messageDTOList) {
            messageDTO.setAvatar(userProfile.getAvatar());
            messageDTO.setSenderName(userProfile.getUsername());
        }
        // 4. 封装分页结果 (PageResult 和 PageFinalResult)
        // 使用 PageResult.of 静态工厂方法创建 PageResult 对象，它会自动计算总页数等信息
        PageResult<MessageDTO> pageResult = PageResult.of(
                pageRequestDTO.getPageNum(), // 当前页码
                limit, // 每页数量
                totalRecords // 总记录数
        );

        // 创建 PageFinalResult 对象，用于最终返回给 Controller
        PageFinalResult<MessageDTO> pageFinalResult = new PageFinalResult<>();
        pageFinalResult.setList(messageDTOList); // 设置消息数据列表
        pageFinalResult.setPageResult(pageResult); // 设置分页元数据

        // 返回封装好的分页结果
        return pageFinalResult;
    }

    @Override
    public void deleteAllMessages(Long userId) {
        // 1. 删除 user_messages和messages 表中的记录
        int deletedCount = messagesMapper.deleteReadedMessagesByUserId(userId);
        if (deletedCount == 0) {
            throw new RuntimeException("删除失败");
        }
    }

    @Override
    public void deleteMessageById(Long userId, Long messageId) {
        // 1. 删除 user_messages和messages 表中的记录
        int deletedCount = messagesMapper.deleteMessageById(userId, messageId);
        if (deletedCount == 0) {
            throw new RuntimeException("删除失败");
        }
    }
}
