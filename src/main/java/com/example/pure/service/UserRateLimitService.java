package com.example.pure.service;

import com.example.pure.exception.RateLimitExceededException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.time.Duration;

/**
 * 用户限流服务
 * 处理已认证用户的访问频率限制
 */
@Slf4j
@Service
public class UserRateLimitService {

    private final RedisTemplate<String, Object> redisTemplate;

    // 用户限流配置
    private static final int USER_MINUTE_LIMIT = 60;  // 每分钟限制
    private static final int USER_HOUR_LIMIT = 13;    // 每小时限制
    private static final int USER_DAILY_LIMIT = 30;   // 每天限制

    // Redis key格式
    private static final String USER_RATE_LIMIT_KEY = "rate:user:%s:%s:%s";  // rate:user:${username}:${method}:${period}

    // 时间维度标识
    private static final String MINUTE_SUFFIX = "per_minute";
    private static final String HOUR_SUFFIX = "per_hour";
    private static final String DAILY_SUFFIX = "per_day";

    @Autowired
    public UserRateLimitService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 检查用户访问频率限制
     * 用于已认证用户的接口访问控制
     * @param methodName 方法名称，用于区分不同接口的限制规则
     * @throws RateLimitExceededException 当超过访问限制时抛出
     */
    public void checkUserRateLimit(String methodName) {
        //从安全上下文中获取用户认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        //用户认证信息为空或用户是否认证了，如果是真的已经认证了会的到真就会运行throw，如果取反不运行抛出错误
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RateLimitExceededException("用户未认证");
        }

        String username = authentication.getName();
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        switch (methodName) {
            case "getCurrentUser":
            case "getUser":
            case "getUserWithUserProfile":
                checkMinuteLimit(username, methodName);
                break;

            case "getAllUsers":
            case "updateUser":
            case "updatePassword":
            case "getUsersByPage":
                checkHourAndDailyLimit(username, methodName, today);
                break;

            default:
                log.warn("未知的方法名: {}", methodName);
                throw new RateLimitExceededException("未知的访问方法");
        }
    }

    /**
     * 检查分钟级别限制
     */
    private void checkMinuteLimit(String username, String methodName) {
        //字符串格式化把USER_RATE_LIMIT_KEY里的%s替换为,后面的参数
        String minuteKey = String.format(USER_RATE_LIMIT_KEY, username, methodName,
            MINUTE_SUFFIX + ":" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")));

        checkAndIncrementLimit(minuteKey, USER_MINUTE_LIMIT, 1, TimeUnit.MINUTES, "每分钟");
    }

    /**
     * 检查小时和每日限制
     */
    private void checkHourAndDailyLimit(String username, String methodName, String today) {
        String hourKey = String.format(USER_RATE_LIMIT_KEY, username, methodName,
            HOUR_SUFFIX + ":" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHH")));
        String dailyKey = String.format(USER_RATE_LIMIT_KEY, username, methodName,
            DAILY_SUFFIX + ":" + today);

        checkAndIncrementLimit(hourKey, USER_HOUR_LIMIT, 1, TimeUnit.HOURS, "每小时");
        checkAndIncrementLimit(dailyKey, USER_DAILY_LIMIT, getSecondsUntilMidnight(),
            TimeUnit.SECONDS, "每天");
    }

    /**
     * 检查并递增计数器
     * 原子性操作检查和递增访问次数
     */
    private void checkAndIncrementLimit(String key, int limit, long timeout, TimeUnit timeUnit, String periodName) {
        // 原子性递增计数器
        Long count = redisTemplate.opsForValue().increment(key);

        // 设置过期时间（仅在第一次设置）
        if (count == 1L) {
            redisTemplate.expire(key, timeout, timeUnit);
        }

        // 检查是否超出限制
        if (count > limit) {
            log.warn("用户访问频率超出限制: key={}, count={}, limit={}", key, count, limit);
            throw new RateLimitExceededException(String.format("访问太频繁，%s只能访问%d次", periodName, limit));
        }
    }

    /**
     * 计算到下一个0点的秒数
     */
    private long getSecondsUntilMidnight() {
        LocalDateTime now = LocalDateTime.now();
        //根据当前时间获取明天0点的时间
        LocalDateTime nextMidnight = now.toLocalDate().plusDays(1).atStartOfDay();
        //获取当前时间和目标时间的秒数并返回
        return Duration.between(now, nextMidnight).getSeconds();
    }
}
