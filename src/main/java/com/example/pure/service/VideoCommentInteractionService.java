package com.example.pure.service;

import com.example.pure.common.PageFinalResult;
import com.example.pure.model.dto.CommentLikeInfoDTO;
import com.example.pure.model.dto.CommentLikeRequest;
import com.example.pure.model.dto.VideoCommentRequest;
import com.example.pure.model.dto.VideoCommentsDTO;
import com.example.pure.model.dto.VideoComentsPageRequest;

/**
 * 视频评论互动服务接口。
 * <p>
 * 定义了所有与视频评论相关的业务操作，包括评论的增、删、查以及对评论的点赞/踩等互动功能。
 * </p>
 */
public interface VideoCommentInteractionService {

    /**
     * 创建一个新的视频评论。
     * <p>
     * 如果评论是作为对另一条评论的回复（即 parentCommentId 不为 null），
     * 此方法会负责发布一个事件，以通知被回复的用户。
     * </p>
     *
     * @param videoCommentRequest 包含评论内容、视频ID、用户ID等信息的请求对象。
     */
    void createComment(VideoCommentRequest videoCommentRequest);

    /**
     * 分页获取指定视频的评论列表。
     *
     * @param videoComentsPageRequest 包含视频ID和分页参数（页码、每页大小）的请求对象。
     * @return 包含评论列表和分页信息的 {@link PageFinalResult} 对象。
     */
    PageFinalResult<VideoCommentsDTO> getComments(VideoComentsPageRequest videoComentsPageRequest,Long userId);

    /**
     * 根据评论ID删除一条评论。
     *
     * @param commentId 要删除的评论ID。
     */
    void deleteComment(Long commentId);

    /**
     * 处理对评论的点赞或踩操作。
     * <p>
     * 此方法封装了点赞/取消点赞、踩/取消踩以及从赞到踩（或反之）的完整逻辑。
     * 当用户首次点赞某条评论时，会发布一个事件。
     * </p>
     *
     * @param userId             执行操作的用户ID。
     * @param username           执行操作的用户名（用于发布事件）。
     * @param commentLikeRequest 包含目标评论ID和操作类型（'like' 或 'dislike'）的请求对象。
     */
    void handleCommentLike(Long userId, String username, CommentLikeRequest commentLikeRequest);

    /**
     * 获取指定评论的点赞/踩相关信息。
     * <p>
     * 用于客户端在用户操作后更新UI，显示最新的点赞/踩总数以及当前用户的操作状态。
     * </p>
     *
     * @param commentId 要查询的评论ID。
     * @param userId    当前登录用户的ID (可为null，若为null则用户状态返回'none')。
     * @return 包含点赞数、踩数和当前用户点赞状态的 {@link CommentLikeInfoDTO} 对象。
     */
    CommentLikeInfoDTO getCommentLikeInfo(Long commentId, Long userId);
}
