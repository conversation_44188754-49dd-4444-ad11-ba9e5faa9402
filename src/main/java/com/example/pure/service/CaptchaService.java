package com.example.pure.service;

/**
 * reCAPTCHA 人机验证服务接口。
 * <p>
 * 定义了用于验证客户端 reCAPTCHA 令牌的核心功能。
 * 此服务的设计旨在封装与 Google reCAPTCHA 后端服务的所有交互细节，
 * 为上层业务（如用户登录、注册）提供一个简单、统一的验证入口。
 */
public interface CaptchaService {

    /**
     * 验证用户对 reCAPTCHA 挑战的响应。
     * <p>
     * 这是一个同步阻塞方法，它会向 Google reCAPTCHA 服务器发送验证请求，并等待响应。
     * 该方法被设计为在需要执行人机验证的业务流程中（例如登录、注册）直接调用。
     *
     * @param token 由 reCAPTCHA 前端组件生成并提交到后端的用响应令牌。
     * @throws com.example.pure.exception.BusinessException 如果验证失败（例如，令牌无效、网络超时、Google服务返回失败状态等），
     *                                                      则会抛出业务异常，中断当前操作。
     */
    void captchaVerify(String token);
} 