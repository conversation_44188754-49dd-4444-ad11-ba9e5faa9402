package com.example.pure.service;

import com.example.pure.common.PageFinalResult;
import com.example.pure.model.dto.PageRequestDTO;
import com.example.pure.model.dto.VideoInfoWithEpisodesDto;
import com.example.pure.model.dto.VideoEpisodesDTO;
import com.example.pure.model.entity.VideoInfo;
import com.example.pure.model.entity.VideoType;

import java.util.List;


public interface PureVideoUrlService {


    // 上传视频的主要信息和集数Url
    void uploadVideoWithEpisodes(VideoInfoWithEpisodesDto videoUploadRequest);

    // 上传视频的集数Url
    void uploadVideoEpisodes(VideoInfoWithEpisodesDto videoUploadRequest);

    /**
     * 获取视频的详细信息，包括所有分集以及当前用户的点赞状态。
     *
     * @param title  视频标题
     * @param userId 当前登录用户的ID (如果未登录，则为null)
     * @return 包含视频完整信息的DTO
     */
    VideoInfoWithEpisodesDto getVideoInfoWithEpisodes(String title, Long userId);

    // 获取视频的信息分页获取
    PageFinalResult<VideoInfo> getVideoInfoWithPagination(PageRequestDTO pageRequest);

    //获取视频信息通过类型获取
    PageFinalResult<VideoInfo> getVideoInfoByTypeWithPagination(PageRequestDTO pageRequest);

    // 获取全部视频类型
    List<VideoType> getAllVideoType();

    /**
     * 异步生成并上传雪碧图
     * @param playUrl 播放URL
     * @param videoId 视频ID
     * @param episodeNumber 分集编号
     * @param folderUuid 文件夹UUID
     */
    void generateSpriteAsync(String playUrl, Long videoId, String episodeNumber, String folderUuid);
}
