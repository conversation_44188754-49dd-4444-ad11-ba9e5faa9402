package com.example.pure.service;

import com.example.pure.model.entity.Role;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色服务接口
 * 提供角色相关的业务操作
 */
@Validated
public interface RoleService {
    /**
     * 创建新角色
     */
    Role createRole(@Valid @NotNull(message = "角色信息不能为空") Role role);

    /**
     * 根据ID查找角色
     */
    Role findById(@NotNull(message = "角色ID不能为空") Long id);

    /**
     * 根据角色名称查找角色
     */
    Role findByName(String name);

    /**
     * 获取所有角色
     */
    List<Role> findAll();

    /**
     * 更新角色信息
     */
    void updateRole(@Valid @NotNull(message = "角色信息不能为空") Role role);

    /**
     * 删除角色
     */
    void deleteRole(@NotNull(message = "角色ID不能为空") Long id);

    /**
     * 检查角色名是否已存在
     */
    boolean existsByName(String name);
}
