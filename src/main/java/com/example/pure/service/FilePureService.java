package com.example.pure.service;


import com.example.pure.model.dto.FileMetadata;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;


/**
 * 文件服务接口
 * <p>
 * 提供文件操作相关的方法，包括：
 * - 视频流式播放
 * - 图片查看
 * - 文件下载
 * </p>
 */
public interface FilePureService {

    /**
     * 验证文件名是否合法
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */

    String getFileExtension(String fileName);

    /**
     * 获取视频流（传统实现）
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含视频流的Resource对象
     * @throws IOException 如果文件访问出错
     */
    ResponseEntity<Resource> streamVideo(String fileName, HttpHeaders headers) throws IOException;

    /**
     * 流式传输文件（通用方法，适用于任何文件类型）
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含文件流的Resource对象
     * @throws IOException 如果文件访问出错
     */
    ResponseEntity<Resource> streamFile(String fileName, HttpHeaders headers) throws IOException;

    /**
     * 下载文件（传统实现）
     *
     * @param filename 文件名
     * @param headers  请求头
     * @return 包含文件内容的Resource对象
     * @throws IOException 如果文件访问出错
     */
    ResponseEntity<Resource> downloadFile(String filename, HttpHeaders headers) throws IOException;

    /**
     * 获取文件的MIME类型
     *
     * @param filePath 文件路径
     * @return 文件的MIME类型
     * @throws IOException 如果文件访问出错
     */
    String determineContentType(Path filePath) throws IOException;

    /**
     * 验证文件名是否合法
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */
    boolean isValidFileName(String fileName);

    /**
     * 获取文件元数据信息
     *
     * @param filename 文件名
     * @return 文件元数据，包含内容类型、大小等信息
     * @throws IOException 如果文件访问出错
     */
    FileMetadata getFileMetadata(String filename) throws IOException;

    /**
     * 异步流式传输视频
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行视频流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> streamVideoAsync(String fileName, HttpHeaders headers);

    /**
     * 异步流式传输文件（通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers);

    /**
     * 异步下载文件
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行文件下载，避免阻塞请求处理线程
     * </p>
     *
     * @param filename 文件名
     * @param headers  请求头，用于处理Range请求和其他HTTP头信息
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> downloadFileAsync(String filename, HttpHeaders headers);

    /**
     * 异步下载文件（带选项）
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行文件下载，避免阻塞请求处理线程
     * 支持自定义选项，如缓冲区大小等
     * </p>
     *
     * @param filename 文件名
     * @param headers  请求头，用于处理Range请求和其他HTTP头信息
     * @param options  下载选项，可包含缓冲区大小等参数
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("fileTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> downloadFileAsync(String filename, HttpHeaders headers, java.util.Map<String, Object> options);


}
