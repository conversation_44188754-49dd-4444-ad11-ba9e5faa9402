package com.example.pure.constant;

/**
 * 角色类型枚举
 *
 * <p>定义系统中的基本角色类型，包括：
 * <ul>
 *     <li>ROLE_USER：普通用户角色</li>
 *     <li>ROLE_ADMIN：管理员角色</li>
 * </ul>
 * </p>
 *
 * <p>使用示例：
 * <pre>{@code
 * // 检查角色类型
 * if (RoleType.ROLE_ADMIN.getCode().equals(roleCode)) {
 *     // 管理员操作
 * }
 * }</pre>
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum RoleType {
    /** 普通用户角色 */
    ROLE_USER("ROLE_USER"),
    /** 管理员角色 */
    ROLE_ADMIN("ROLE_ADMIN");

    /** 角色编码 */
    private final String code;

    /**
     * 构造函数
     *
     * @param code 角色编码
     */
    RoleType(String code) {
        this.code = code;
    }

    /**
     * 获取角色编码
     *
     * @return 角色编码
     */
    public String getCode() {
        return code;
    }
}
