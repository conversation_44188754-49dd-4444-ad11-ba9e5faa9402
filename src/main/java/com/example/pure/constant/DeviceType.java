package com.example.pure.constant;

/**
 * 设备类型枚举
 * <p>
 * 定义用户可登录的设备类型
 * </p>
 */
public enum DeviceType {
    /**
     * PC端
     */
    PC,

    /**
     * 安卓设备
     */
    ANDROID,

    /**
     * iOS设备
     */
    IOS,

    /**
     * 平板设备
     */
    TABLET,

    /**
     * 其他未知设备
     */
    UNKNOWN;

    /**
     * 根据User-Agent判断设备类型
     *
     * @param userAgent HTTP请求的User-Agent头
     * @return 设备类型
     */
    public static DeviceType fromUserAgent(String userAgent) {
        if (userAgent == null) {
            return UNKNOWN;
        }

        userAgent = userAgent.toLowerCase();

        if (userAgent.contains("android")) {
            return ANDROID;
        } else if (userAgent.contains("iphone") || userAgent.contains("ipad") || userAgent.contains("ipod")) {
            return IOS;
        } else if (userAgent.contains("windows") || userAgent.contains("macintosh") || userAgent.contains("linux")) {
            return PC;
        } else if (userAgent.contains("tablet")) {
            return TABLET;
        }

        return UNKNOWN;
    }
}
