package com.example.pure.constant;

/**
 * 响应状态码常量类
 *
 * <p>定义了系统中所有的响应状态码，遵循HTTP状态码的基本原则：
 * <ul>
 *     <li>2xx：成功</li>
 *     <li>4xx：客户端错误</li>
 *     <li>5xx：服务器错误</li>
 *     <li>6xx：自定义业务错误</li>
 * </ul>
 * </p>
 *
 * <p>使用示例：
 * <pre>{@code
 * // 在异常处理中使用
 * throw new BusinessException(ResponseCode.INVALID_PARAMETER, "参数错误");
 *
 * // 在响应中使用
 * return Result.error(ResponseCode.UNAUTHORIZED, "未登录");
 * }</pre>
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ResponseCode {

    private ResponseCode() {
        throw new IllegalStateException("常量类不能实例化");
    }

    // ========== 成功状态码 ==========
    /** 操作成功 */
    public static final int SUCCESS = 200;

    // ========== 客户端错误码 (400-499) ==========
    /** 错误的请求 */
    public static final int BAD_REQUEST = 400;
    /** 未认证 */
    public static final int UNAUTHORIZED = 401;
    /** 禁止访问 */
    public static final int FORBIDDEN = 403;
    /** 资源不存在 */
    public static final int NOT_FOUND = 404;
    /** 方法不允许 */
    public static final int METHOD_NOT_ALLOWED = 405;
    /** 客户端关闭请求 */
    public static final int CLIENT_CLOSED_REQUEST = 499;

    // ========== 服务器错误码 (500-599) ==========
    /** 服务器内部错误 */
    public static final int INTERNAL_SERVER_ERROR = 500;
    /** 服务不可用 */
    public static final int SERVICE_UNAVAILABLE = 503;
    /** 数据库错误 */
    public static final int DATABASE_ERROR = 507;

    // ========== 业务错误码 (600+) ==========
    /** 通用业务错误 */
    public static final int BUSINESS_ERROR = 600;
    /** 参数验证错误 */
    public static final int INVALID_PARAMETER = 601;
    /** 唯一键冲突 */
    public static final int DUPLICATE_KEY = 602;

    // ========== 用户相关错误码 (610-619) ==========
    /** 用户不存在 */
    public static final int USER_NOT_FOUND = 610;
    /** 用户已存在 */
    public static final int USER_ALREADY_EXISTS = 611;
    /** 密码错误 */
    public static final int INVALID_PASSWORD = 612;
    /** 账号已锁定 */
    public static final int ACCOUNT_LOCKED = 613;
    /** 账号已禁用 */
    public static final int ACCOUNT_DISABLED = 614;
    /** 用户名已存在 */
    public static final int USERNAME_ALREADY_EXISTS = 615;
    /** 邮箱已存在 */
    public static final int EMAIL_ALREADY_EXISTS = 616;
    /** 无效的凭证 */
    public static final int INVALID_CREDENTIALS = 617;
    /** 用户名必填 */
    public static final int USERNAME_REQUIRED = 618;
    /** 密码必填 */
    public static final int PASSWORD_REQUIRED = 619;

    // ========== 权限相关错误码 (620-629) ==========
    /** 没有权限 */
    public static final int NO_PERMISSION = 620;
    /** 令牌过期 */
    public static final int TOKEN_EXPIRED = 621;
    /** 无效令牌 */
    public static final int INVALID_TOKEN = 622;

    // ========== 业务操作错误码 (630-639) ==========
    /** 操作失败 */
    public static final int OPERATION_FAILED = 630;
    /** 数据不存在 */
    public static final int DATA_NOT_FOUND = 631;
    /** 状态错误 */
    public static final int STATUS_ERROR = 632;
    /** 注册失败 */
    public static final int REGISTRATION_FAILED = 633;
    /** 更新失败 */
    public static final int UPDATE_FAILED = 634;
    /** 删除失败 */
    public static final int DELETE_FAILED = 635;
    /** 邮箱必填 */
    public static final int EMAIL_REQUIRED = 636;
}
