package com.example.pure.actuator;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Redis健康检查指示器
 * 实现Spring Boot Actuator的HealthIndicator接口
 * 用于监控Redis连接状态
 * 集成到/actuator/health端点
 *
 * <AUTHOR> Name
 * @since 1.0.0
 */
@Component
public class  RedisHealthIndicator implements HealthIndicator {

    private final RedisTemplate<String, Object> redisTemplate;

    public RedisHealthIndicator(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 执行Redis健康检查
     * 通过ping命令测试Redis连接是否正常
     *
     * @return Health对象，包含Redis的健康状态和详细信息
     */
    @Override
    public Health health() {
        try {
            RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();
            String pong = new String(connection.ping());
            if ("PONG".equals(pong)) {
                // Redis正常运行
                return Health.up()
                    .withDetail("redis", "Redis is running")
                    .withDetail("ping_response", pong)
                    .build();
            }
            // Redis响应异常
            return Health.down()
                .withDetail("redis", "Redis ping failed")
                .build();
        } catch (Exception e) {
            // Redis连接异常
            return Health.down()
                .withDetail("redis", "Redis is down")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
