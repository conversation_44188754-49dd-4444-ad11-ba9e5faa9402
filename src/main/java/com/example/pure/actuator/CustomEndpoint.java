package com.example.pure.actuator;

import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义Actuator端点
 * 提供应用程序的自定义监控信息
 * 可通过 /actuator/custom 访问
 *
 * <AUTHOR> Name
 * @since 1.0.0
 */
@Component
@Endpoint(id = "custom") // 定义端点ID，访问路径将是/actuator/custom
public class CustomEndpoint {

    /**
     * 获取自定义监控信息
     * 使用@ReadOperation注解标记为只读操作
     * 当发送GET请求到/actuator/custom时会调用此方法
     *
     * @return 包含监控信息的Map
     */
    @ReadOperation
    public Map<String, Object> getCustomInfo() {
        Map<String, Object> info = new HashMap<>();
        // 应用运行状态
        info.put("app_status", "running");
        // 服务器当前时间戳
        info.put("server_time", System.currentTimeMillis());
        // 已使用的内存
        info.put("memory_used", Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory());
        // 总内存
        info.put("memory_total", Runtime.getRuntime().totalMemory());
        return info;
    }
}
