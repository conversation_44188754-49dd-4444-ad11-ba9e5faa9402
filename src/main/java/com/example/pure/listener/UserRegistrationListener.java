package com.example.pure.listener;

import com.example.pure.constant.MessageConstants; // 假设您会在这里或另一个常量类定义消息类型
import com.example.pure.constant.SecurityConstants;
import com.example.pure.event.UserRegisteredEvent;
import com.example.pure.model.dto.SystemMessageRequestDTO;
import com.example.pure.service.MessagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UserRegistrationListener {

    private final MessagesService messagesService;

    @Autowired
    public UserRegistrationListener(MessagesService messagesService) {
        this.messagesService = messagesService;
    }

    @Async // 异步处理发送消息，不阻塞注册流程
    @EventListener
    public void handleUserRegisteredEvent(UserRegisteredEvent event) {
        log.info("接收到用户注册成功事件：用户ID {}，用户名 {}", event.getUserId(), event.getUsername());

        try {
            String title = "欢迎";
            String content = "欢迎您来到并注册我的网站，" + event.getUsername() + "!";

            SystemMessageRequestDTO messageRequest = SystemMessageRequestDTO.builder()
                    .recipientUserId(event.getUserId()) // 消息接收者是新注册的用户
                    .title(title)
                    .content(content)
                    .messageType(MessageConstants.MESSAGE_TYPE_NOTIFICATION) 
                    .build();
            
            // 注意：sendSystemMessage 内部会将 senderId 设置为 SecurityConstants.ADMIN_ID
            messagesService.sendSystemMessage(messageRequest);

            log.info("已为新用户 {} (ID: {}) 发送欢迎消息", event.getUsername(), event.getUserId());

        } catch (Exception e) {
            log.error("为新用户 {} (ID: {}) 发送欢迎消息失败: {}", 
                      event.getUsername(), event.getUserId(), e.getMessage(), e);
            // 根据策略，发送消息失败通常不应影响用户注册的整体成功状态
        }
    }
} 