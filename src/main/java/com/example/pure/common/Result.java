package com.example.pure.common;

import com.example.pure.constant.ResponseCode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 统一响应结果封装类
 *
 * <p>用于统一处理API响应格式，包含状态码、消息和数据。
 * 支持泛型，可以返回任意类型的数据。使用建造者模式实现链式调用。
 * </p>
 *
 * <p>示例：
 * <pre>{@code
 * // 成功响应，带数据
 * Result<User> result = Result.success(user);
 *
 * // 成功响应，带消息
 * Result<Void> result = Result.success("操作成功");
 *
 * // 错误响应
 * Result<Void> result = Result.error(400, "参数错误");
 * }</pre>
 * </p>
 *
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    /**
     * 状态码
     * <p>200: 成功</p>
     * <p>4xx: 客户端错误</p>
     * <p>5xx: 服务器错误</p>
     */
    private int code;

    /**
     * 响应消息
     * 用于描述操作结果
     */
    private String message;

    private Boolean success;

    /**
     * 响应数据
     * 实际返回的数据对象,T会根据传入的数据类型自动判断数据类型
     */
    private T data;

    // 使用注解Json格式化成标准化：ISO 8601 是国际标准格式，所有现代编程语言和框架均支持解析
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant time;


    /**
     * 创建成功响应（无数据）
     *
     * @return 成功响应对象
     */
    public static Result<Void> success() {
        return Result.<Void>builder().time(Instant.now()).code(ResponseCode.SUCCESS).message("API操作成功").success(true).build();
    }

    /**
     * 创建成功响应（带消息）
     *
     * @param message 成功消息
     * @return 成功响应对象
     */
    public static Result<Void> success(String message) {
        return Result.<Void>builder().time(Instant.now()).code(ResponseCode.SUCCESS).message(message).success(true).build();
    }

    /**
     * 创建成功响应（带数据）
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应对象
     */
    public static <T> Result<T> success(T data) {
        return Result.<T>builder().time(Instant.now()).code(ResponseCode.SUCCESS).message("操作成功").success(true).data(data).build();
    }

    /**
     * 创建成功响应（带消息和数据）
     *
     * @param message 成功消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return 成功响应对象
     */
    public static <T> Result<T> success(String message, T data) {
        return Result.<T>builder().time(Instant.now()).code(ResponseCode.SUCCESS).message(message).success(true).data(data).build();
    }


    /**
     * 创建错误响应 (返回Void类型结果)
     *
     * @param code    错误码
     * @param message 错误消息
     * @return 错误响应对象
     */
    public static Result<Void> error(int code, String message) {
        return Result.<Void>builder().time(Instant.now()).code(code).message(message).success(false).build();
    }

    /**
     * 创建错误响应（带数据）
     *
     * @param code    错误码
     * @param message 错误消息
     * @param data    错误相关数据
     * @param <T>     数据类型
     * @return 错误响应对象
     */
    public static <T> Result<T> error(int code, String message, T data) {
        return Result.<T>builder().time(Instant.now()).code(code).message(message).data(data).success(false).build();
    }

    /**
     * 创建指定类型的错误响应，当需要匹配特定返回类型时使用
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     响应数据类型
     * @return 错误响应对象
     */
    public static <T> Result<T> errorTyped(int code, String message) {
        return Result.<T>builder().time(Instant.now()).code(code).message(message).success(false).build();
    }

    /**
     * 设置响应数据
     *
     * @param data 响应数据
     * @return this，用于链式调用
     */
    public Result<T> data(T data) {
        this.data = data;
        return this;
    }

    /**
     * 设置响应消息
     *
     * @param message 响应消息
     * @return this，用于链式调用
     */
    public Result<T> message(String message) {
        this.message = message;
        return this;
    }
}
