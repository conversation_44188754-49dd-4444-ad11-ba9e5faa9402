// src/main/java/com/example/demo13/config/WebMvcConfig.java
package com.example.pure.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring MVC 配置类
 *
 * <p>配置Spring MVC的核心功能，包括：
 * <ul>
 *     <li>跨域资源共享(CORS)配置</li>
 *     <li>静态资源处理配置</li>
 *     <li>视图控制器配置</li>
 *     <li>Swagger UI资源映射</li>
 * </ul>
 * </p>
 *
 * <p>主要特性：
 * <ul>
 *     <li>支持全局CORS配置</li>
 *     <li>自定义静态资源位置</li>
 *     <li>简化URL到视图的映射</li>
 *     <li>集成Swagger文档UI</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 配置跨域资源共享
     *
     * <p>允许跨域请求的详细配置：
     * <ul>
     *     <li>允许所有来源</li>
     *     <li>允许所有HTTP方法</li>
     *     <li>允许所有请求头</li>
     *     <li>允许发送Cookie</li>
     *     <li>预检请求缓存1小时</li>
     * </ul>
     * </p>
     *
     * @param registry CORS配置注册器
     */
    @Override // 如果是实现类的配置的重写方法的话不使用@Bean，直接使用重写的方法来配置库，适合复杂的配置
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    /**
     * 配置静态资源处理
     *
     * <p>配置以下资源的访问路径：
     * <ul>
     *     <li>静态资源文件</li>
     *     <li>Swagger UI资源</li>
     * </ul>
     * </p>
     *
     * @param registry 资源处理器注册器
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");

        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/");
    }

    /**
     * 配置视图控制器
     *
     * <p>配置URL到视图的直接映射：
     * <ul>
     *     <li>首页映射</li>
     *     <li>Swagger UI页面映射</li>
     * </ul>
     * </p>
     *
     * @param registry 视图控制器注册器
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/").setViewName("index");
        registry.addViewController("/swagger-ui/")
                .setViewName("redirect:/swagger-ui/index.html");
    }
}
