package com.example.pure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessageType;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketTransportRegistration;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.springframework.web.socket.server.support.DefaultHandshakeHandler;

import java.security.Principal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * WebSocket配置类
 * <p>
 * 配置WebSocket服务器，启用STOMP协议，提供实时通信功能。
 * 提供了广播消息和点对点消息的支持，主要用于以下场景：
 * 1. 二维码登录的实时状态更新
 * 2. 聊天室功能
 * 3. 实时通知推送
 * </p>
 * <p>
 * 支持的消息模式：
 * - 广播消息：发送到/topic前缀的目的地
 * - 点对点消息：发送到/user前缀的目的地
 * - 应用消息：客户端发送到/app前缀的目的地
 * - 队列消息：发送到/queue前缀的目的地
 * </p>
 */
@Slf4j
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    /**
     * 配置消息代理
     * <p>
     * 启用简单的基于内存的消息代理，将消息从一个客户端传递给另一个客户端
     * </p>
     *
     * @param registry 消息代理注册表
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {

        //双向server<->client(subscribed Address)，推荐server->client（多用户subscribe接收推送）
        // 使用代理(内置的、简单的、基于内存的消息代理)来处理消息，结论:只有server->client需要订阅
        // 客户端订阅地址以这些前缀开头的消息将会路由到消息代理
        registry.enableSimpleBroker(
                "/topic",  // 用于广播消息，一对多推送
                "/queue"   // 用于点对点消息，配合/user一对一推送
        );

        // 设置用户目的地前缀，用于点对点消息，server->client（一对一subscribe接收推送）
        // 例如：向特定用户发送消息：@SendToUser或simpMessagingTemplate.convertAndSendToUser("username", "/queue/messages", message)
        // 客户端订阅：/user/queue/messages,/user一般配合/queue,只有订阅要加这个前缀的user为了让Spring找到user值的session
        registry.setUserDestinationPrefix("/user");

        //单向client->server，推荐client->server
        // 设置应用程序目标前缀，客户端发送消息到服务器端点，只能接受客户端发送的消息用
        // 客户端发送消息的目的地址以这个前缀开头，然后由@MessageMapping注解的方法处理,/app前缀的消息不用订阅也能从客户端发送到服务器
        // 客户端发送 STOMP 消息，应该直接发往消息代理 (Broker)，而是应该被路由到应用程序内部
        registry.setApplicationDestinationPrefixes("/app");
    }

    /**
     * 注册STOMP端点
     * <p>
     * 配置WebSocket服务器端点，客户端通过这些端点连接到WebSocket服务器
     * </p>
     *
     * @param registry 端点注册表
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {

        // 添加两个端点：一个带SockJS支持，一个不带
        // 1. 带SockJS的端点，用于浏览器兼容
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")  // 允许所有来源的跨域请求
                .addInterceptors(httpSessionHandshakeInterceptor()) // 添加拦截器，用于传递HTTP属性
                .setHandshakeHandler(new UserHandshakeHandler()) // 使用自定义握手处理程序
                .withSockJS()
                .setDisconnectDelay(30 * 1000)  // 设置断开连接的延迟时间为30秒
                .setHeartbeatTime(25 * 1000);   // 设置心跳时间为25秒

        // 2. 纯WebSocket端点，用于Postman等工具测试
        registry.addEndpoint("/ws-raw")
                .setAllowedOriginPatterns("*")
                .setHandshakeHandler(new UserHandshakeHandler()); // 使用通用的用户握手处理程序
    }

    /**
     * 配置WebSocket传输选项
     * <p>
     * 设置WebSocket传输参数，如消息大小限制等
     * </p>
     *
     * @param registration WebSocket传输注册表
     */
    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registration) {
        // 增加消息缓冲区大小，解决大消息的问题
        registration.setMessageSizeLimit(128 * 1024);      // 消息大小限制为128KB
        registration.setSendBufferSizeLimit(512 * 1024);   // 发送缓冲区大小为512KB
        registration.setSendTimeLimit(20000);              // 发送超时时间20秒
        registration.setTimeToFirstMessage(30000);         // 等待第一条消息的超时时间为30秒
    }

    /**
     * 配置客户端入站通道
     * <p>
     * 用于添加自定义的通道拦截器，处理入站消息
     * </p>
     */
    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        // 添加用于身份验证和授权的通道拦截器
        registration.interceptors(stompChannelInterceptor());

        // 可以配置线程池
        registration.taskExecutor()
                .corePoolSize(4)  // 核心线程数
                .maxPoolSize(10)  // 最大线程数
                .queueCapacity(25); // 队列容量
    }

    /**
     * 配置客户端出站通道
     * <p>
     * 用于添加自定义的通道拦截器，处理出站消息
     * </p>
     */
    @Override
    public void configureClientOutboundChannel(ChannelRegistration registration) {
        // 配置出站消息的线程池
        registration.taskExecutor()
                .corePoolSize(4)
                .maxPoolSize(10);
    }

    /**
     * 创建WebSocket握手拦截器
     * <p>
     * 拦截WebSocket握手请求，可以传递HTTP会话属性到WebSocket会话
     * </p>
     */
    @Bean
    public HandshakeInterceptor httpSessionHandshakeInterceptor() {
         /* new class() {} 创建一个匿名内部类这个匿名类是new类的子类（复制类，它继承了new的类里所有非private的方法和变量）
         或者实现接口（它继承了new的类所有方法要求重写所有声明方法才能使用，并可以在 {} 中进行定制），他的作用为了简化代码
         只能逻辑比较简单并且只需要一个地方使用的场景 */
        return new HandshakeInterceptor() {
            @Override
            public boolean beforeHandshake(ServerHttpRequest request, org.springframework.http.server.ServerHttpResponse response,
                                          WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
                // 可以在这里设置WebSocket会话属性
                log.debug("WebSocket握手请求: {}", request.getURI());

                // 从URL查询参数中提取用户名
                String query = request.getURI().getQuery();
                if (query != null) {
                    // 解析查询参数
                    String[] params = query.split("&");
                    for (String param : params) {
                        if (param.startsWith("username=")) {
                            String username = param.substring("username=".length());
                            // 将用户名存储在会话属性中，供握手处理程序使用
                            attributes.put("username", username);
                            log.debug("从URL提取的用户名: {}", username);
                            break;
                        }
                    }
                }

                // 如果没有用户名，生成一个随机的访客ID
                if (!attributes.containsKey("username")) {
                    String guestId = "guest-" + UUID.randomUUID().toString().substring(0, 8);
                    attributes.put("username", guestId);
                    log.debug("未找到用户名，生成访客ID: {}", guestId);
                }

                return true; // 允许握手继续
            }

            @Override
            public void afterHandshake(ServerHttpRequest request, org.springframework.http.server.ServerHttpResponse response,
                                      WebSocketHandler wsHandler, Exception exception) {
                // 握手完成后的逻辑
                log.debug("WebSocket握手完成");
            }
        };
    }

    /**
     * 自定义握手处理程序，确保所有WebSocket连接都有一个Principal
     */
    private static class UserHandshakeHandler extends DefaultHandshakeHandler {
        @Override
        protected Principal determineUser(ServerHttpRequest request,
                                         WebSocketHandler wsHandler,
                                         Map<String, Object> attributes) {
            // 从会话属性中获取用户名
            String username = (String) attributes.get("username");
            if (username == null) {
                // 如果握手拦截器没有设置用户名，尝试从URL中获取
                String query = request.getURI().getQuery();
                if (query != null && query.contains("username=")) {
                    username = query.substring(query.indexOf("username=") + 9);
                    if (username.contains("&")) {
                        // 防止用户名后面&号也提取了，提取不包含&新字串到变量
                        username = username.substring(0, username.indexOf("&"));
                    }
                }
            }

            // 如果仍然没有用户名，使用默认的访客名称
            if (username == null || username.isEmpty()) {
                username = "guest-" + UUID.randomUUID().toString().substring(0, 8);
            }

            log.debug("WebSocket连接用户: {}", username);
            return new SimplePrincipal(username);
        }
    }

    /**
     * 创建STOMP通道拦截器
     * <p>
     * 拦截STOMP消息，可以进行身份验证、消息转换等操作
     * </p>
     */
    @Bean
    public ChannelInterceptor stompChannelInterceptor() {
        return new ChannelInterceptor() {
            @Override
            public Message<?> preSend(Message<?> message, MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

                if (accessor != null) {
                    // 获取消息类型
                    if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                        // 处理连接请求
                        Object raw = message.getHeaders().get(SimpMessageHeaderAccessor.NATIVE_HEADERS);
                        if (raw instanceof Map) {
                            // 从STOMP帧头部获取登录信息
                            Map<String, Object> nativeHeaders = (Map<String, Object>) raw;

                            // 尝试从login头部获取用户名
                            if (nativeHeaders.containsKey("login")) {
                                Object loginValue = nativeHeaders.get("login");
                                if (loginValue instanceof List && !((List) loginValue).isEmpty()) {
                                    String username = ((List) loginValue).get(0).toString();
                                    accessor.setUser(new SimplePrincipal(username));
                                    log.debug("STOMP连接用户名(login头部): {}", username);
                                }
                            }

                            // 如果没有login头部，尝试从自定义头部获取
                            else if (nativeHeaders.containsKey("username")) {
                                Object usernameValue = nativeHeaders.get("username");
                                if (usernameValue instanceof List && !((List) usernameValue).isEmpty()) {
                                    String username = ((List) usernameValue).get(0).toString();
                                    accessor.setUser(new SimplePrincipal(username));
                                    log.debug("STOMP连接用户名(username头部): {}", username);
                                }
                            }
                        }

                        // 如果仍然没有用户信息，且不是心跳信息，确保创建一个默认的Principal
                        if (accessor.getUser() == null && !SimpMessageType.HEARTBEAT.equals(accessor.getMessageType())) {
                            // 生成随机访客ID
                            String guestId = "guest-" + UUID.randomUUID().toString().substring(0, 8);
                            accessor.setUser(new SimplePrincipal(guestId));
                            log.debug("为STOMP消息创建默认用户: {}", guestId);
                        }
                    }
                }

                return message;
            }
        };
    }

    /**
     * 简单的Principal实现，用于WebSocket连接认证
     */
    private static class SimplePrincipal implements Principal {
        private final String name;

        public SimplePrincipal(String name) {
            this.name = name;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String toString() {
            return "User: " + name;
        }
    }
}
