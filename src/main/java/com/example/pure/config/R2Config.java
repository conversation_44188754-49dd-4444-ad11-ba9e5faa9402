package com.example.pure.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

import java.net.URI;
/**
 * Cloudflare R2 配置类
 * <p>
 * 负责创建与R2服务交互所需的客户端Bean。
 * 我们需要两个不同的Bean来支持两种上传模式：
 * 1. S3Presigner: 用于生成预签名URL，让客户端直接上传。
 * 2. S3Client: 用于在服务器端直接执行上传、下载、删除等操作。
 */
@Configuration
public class R2Config {

    @Value("${cloudflare.r2.endpoint}")
    private String endpoint;
    @Value("${cloudflare.r2.access-key-id}")
    private String accessKeyId;
    @Value("${cloudflare.r2.secret-access-key}")
    private String secretAccessKey;
    @Value("${cloudflare.r2.region}")
    private String region;

    /**
     * 创建 S3Client Bean
     * <p>
     * 这个客户端用于执行服务器到R2的直接API调用，例如后端中转上传。
     *
     * @return 配置好的 S3Client 实例
     */
    @Bean
    public S3Client s3Client() {
        return S3Client.builder()
                .endpointOverride(URI.create(endpoint)) // 指定R2的端点
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKeyId, secretAccessKey))) // 提供静态凭证
                .region(Region.of(region)) // 指定区域
                .build();
    }

    /**
     * 创建 S3Presigner Bean
     * <p>
     * 这个客户端专门用于生成预签名的URL。
     * 它的配置与S3Client类似，但其功能是签名请求，而不是执行请求。
     *
     * @return 配置好的 S3Presigner 实例
     */
    @Bean
    public S3Presigner s3Presigner() {
        return S3Presigner.builder()
                .endpointOverride(URI.create(endpoint)) // 指定R2的端点
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKeyId, secretAccessKey))) // 提供静态凭证
                .region(Region.of(region)) // 指定区域
                .build();
    }
}
