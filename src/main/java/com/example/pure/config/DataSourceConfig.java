package com.example.pure.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置类
 * <p>
 * 配置主数据源及其事务管理器。
 * </p>
 */
@Configuration
public class DataSourceConfig {

    /**
     * 创建主数据源的配置属性对象
     * <p>
     * 通过 @ConfigurationProperties 注解，将 application.yml 文件中
     * 以 spring.datasource.primary 为前缀的配置项，绑定到 DataSourceProperties 对象上。
     * </p>
     *
     * @return 主数据源的配置属性
     */
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.primary")
    public DataSourceProperties primaryDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * 创建主数据源 Bean
     * <p>
     * 根据 primaryDataSourceProperties() 提供的配置信息，创建 HikariDataSource 实例。
     * 使用 @Primary 注解，将此数据源标记为应用程序的主要数据源。
     * Spring Boot 的 MyBatis 或 JPA 自动配置会优先选择标记为 @Primary 的 DataSource。
     * </p>
     *
     * @return 配置好的主数据源 (HikariDataSource)
     */
    @Bean(name = "primaryDataSource") // 给 Bean 起一个明确的名字
    @Primary
    public DataSource primaryDataSource() {
        // 从 DataSourceProperties 创建 HikariDataSource
        return primaryDataSourceProperties()
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class) // 明确指定使用 HikariCP
                .build();
    }

    /**
     * 创建主数据源的事务管理器
     * <p>
     * 为主数据源配置事务管理器，使其支持声明式事务（例如 @Transactional 注解）。
     * 使用 @Primary 注解，确保这是默认的事务管理器。
     * </p>
     *
     * @param dataSource 主数据源 Bean (通过 @Qualifier 指定或依赖 @Primary)
     * @return 主数据源的事务管理器
     */
    @Bean(name = "primaryTransactionManager")
    @Primary
    public PlatformTransactionManager primaryTransactionManager(DataSource dataSource) {
        // 传入 primaryDataSource() 方法返回的 Bean
        return new DataSourceTransactionManager(dataSource);
    }

    // 注意：由于您明确表示暂时不需要配置第二个数据库，因此这里省略了 secondaryDataSource
    // 及相关事务管理器的配置。当需要时，可以仿照 primary 的方式添加 secondary 的配置，
    // 但不要标记为 @Primary。
}
