package com.example.pure.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.time.format.DateTimeFormatter;

/**
 * Redis配置类
 * 配置Redis的序列化方式、缓存管理器和访问日志等功能
 * 把Java数据转换成Redis存储的byte[]存储或者String存储需要序列化器来序列化，反序列化也是需要。
 *
 * <AUTHOR> Name
 * @version 1.0
 * @since 2024-03-xx
 */
@Configuration
@EnableCaching
public class RedisConfig {

    /**
     * 日期时间格式化模式
     * 用于统一日期时间的序列化格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    /**
     * 配置ObjectMapper用于Redis序列化
     * 支持Java 8日期时间类型和类型信息的序列化
     *
     * @return 配置好的ObjectMapper实例
     */
    @Bean
    public ObjectMapper redisObjectMapper() {
        // ObjectMapper 是一个核心类，用于在 Java 对象和 JSON 之间进行转换（序列化和反序列化）
        ObjectMapper mapper = new ObjectMapper();

        // 配置Java 8日期时间格式
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(
                //现在自定义序列化器的类型
            java.time.LocalDateTime.class,
            //创建序列化器实例，构造函数里DateTimeFormatter的对象，用于定义LocalDateTime对象在序列化时格式按DATETIME_FORMAT
            new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATETIME_FORMAT))
        );
        //注册模块： 将 JavaTimeModule 注册到 ObjectMapper(用于运行JSON序列化和反序列化) 中
        mapper.registerModule(javaTimeModule);

        // 禁用将日期写为时间戳，时间戳为毫秒级不易读
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 启用类型信息，确保正确的反序列化，添加@class支持多态，反序列化时能精确还原对象名称和类型
        mapper.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance,
            ObjectMapper.DefaultTyping.NON_FINAL,
            JsonTypeInfo.As.PROPERTY
        );

        return mapper;
    }

    /**
     * 使用配置好的ObjectMapper创建JSON序列化器
     */
    @Bean
    public GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer() {
        return new GenericJackson2JsonRedisSerializer(redisObjectMapper());
    }

    /**
     * 配置RedisTemplate
     * 最佳配置为键序列化用StringRedisSerializer，值用Jackson2JsonRedisSerializer
     * StringRedisSerializer（仅处理字符串，直接存储 String 类型，键值完全可读，跨语言兼容。）
     * Jackson2JsonRedisSerializer（将对象序列化为 JSON 字符串，可读性强，支持复杂对象，需配置类型信息。）
     * 设置序列化器，使数据可读性更好
     * 支持访问日志等复杂对象的存储
     *
     * @param connectionFactory Redis连接工厂
     * @param jackson2JsonRedisSerializer JSON序列化器
     * @return 配置好的RedisTemplate实例
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(
            RedisConnectionFactory connectionFactory,
            GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();

        template.setConnectionFactory(connectionFactory);

        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        template.setValueSerializer(jackson2JsonRedisSerializer);

        // Hash的key也采用StringRedisSerializer的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        // Hash的value也采用Jackson2JsonRedisSerializer的序列化方式
        template.setHashValueSerializer(jackson2JsonRedisSerializer);

        template.afterPropertiesSet();
        return template;
    }

    /**
     * 配置缓存管理器
     * 设置缓存的默认过期时间等配置
     * 支持访问日志的缓存策略
     *
     * @param connectionFactory Redis连接工厂
     * @param jackson2JsonRedisSerializer JSON序列化器
     * @return 配置好的RedisCacheManager实例
     */
    @Bean
    public RedisCacheManager cacheManager(
            RedisConnectionFactory connectionFactory,
            GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .prefixCacheNameWith("pure:")  // 全局前缀，用于区分不同应用
                .computePrefixWith(cacheName -> cacheName + ":")// 定义缓存名称和缓存键之间的格式,cacheName:key
                .entryTtl(Duration.ofHours(1))  // 默认缓存时间1小时
                .serializeKeysWith(
                    RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())
                )
                .serializeValuesWith(
                    RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer)
                )
                .disableCachingNullValues();  // 禁用空值缓存

        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .transactionAware()
                .build();
    }
}
