package com.example.pure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步处理配置类
 * <p>
 * 配置异步任务执行器，包括：
 * - 文件处理线程池配置
 * - 通用异步任务线程池配置
 * - 异常处理配置
 * </p>
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    /**
     * 文件处理线程池
     * <p>
     * 专门用于处理文件下载、上传和视频流等IO密集型操作的线程池
     * 线程池参数经过优化，适合IO密集型任务
     * </p>
     *
     * @return 配置好的线程池任务执行器
     */
    @Bean(name = "fileTaskExecutor")
    public AsyncTaskExecutor fileTaskExecutor() {
        log.info("创建文件操作异步任务线程池");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数 - 始终保持运行的线程数量
        executor.setCorePoolSize(6);
        // 最大线程数 - 线程池最大容量
        executor.setMaxPoolSize(20);
        // 队列容量 - 当核心线程都在运行时，新任务会进入队列等待
        executor.setQueueCapacity(100);
        // 线程名前缀 - 便于调试和监控
        executor.setThreadNamePrefix("file-async-");
        // 拒绝策略 - 当队列满且所有线程都在运行时的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 空闲线程存活时间 - 超过核心线程数的线程在空闲一段时间后会被销毁
        executor.setKeepAliveSeconds(60);
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池关闭的超时时间
        executor.setAwaitTerminationSeconds(60);
        // 设置异步任务超时时间为30分钟
        executor.setAwaitTerminationMillis(30 * 60 * 1000L);
        // 初始化线程池
        executor.initialize();

        // 使用 DelegatingSecurityContextAsyncTaskExecutor 包装以传播安全上下文
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }

    /**
     * 通用异步任务线程池（默认线程池）
     * <p>
     * 用于处理通用的异步任务
     * 如果@Async注解未指定执行器，则使用此默认线程池
     * </p>
     *
     * @return 配置好的线程池任务执行器
     */
    @Bean("taskExecutor")
    @Override
    public AsyncTaskExecutor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 设置核心线程数
        executor.setCorePoolSize(6);

        // 设置最大线程数
        executor.setMaxPoolSize(10);

        // 设置队列容量
        executor.setQueueCapacity(50);

        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);

        // 设置线程名前缀
        executor.setThreadNamePrefix("async-");

        // 设置拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化线程池
        executor.initialize();

        log.info("通用异步任务线程池初始化完成 - 核心线程数: {}, 最大线程数: {}",
            executor.getCorePoolSize(), executor.getMaxPoolSize());

        // 使用 DelegatingSecurityContextAsyncTaskExecutor 包装以传播安全上下文
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }

    /**
     * 异步任务异常处理器
     * <p>
     * 当异步任务抛出未捕获的异常时，该处理器将被调用
     * </p>
     *
     * @return 异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("执行异步任务出现未捕获异常 - 方法: {}, 参数: {}", method.getName(), params, ex);
        };
    }
}
