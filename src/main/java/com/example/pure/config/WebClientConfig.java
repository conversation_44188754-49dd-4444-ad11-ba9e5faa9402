package com.example.pure.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;


/**
 * WebClient配置类
 * 用于创建和配置WebClient实例
 */
@Configuration
public class WebClientConfig {

    /**
     * 创建一个不带代理的、用于直接连接的 WebClient Bean
     * 这个 Bean 被标记为 @Primary，因此它将是默认的注入选择
     * @return 配置好的直接连接 WebClient 实例
     */
    @Bean("directWebClient")
    @Primary
    public WebClient directWebClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))
                .build();
    }

    /**
     * 创建一个配置了代理的 WebClient Bean
     * @return 配置好的代理连接 WebClient 实例
     */
    @Bean("proxyWebClient")
    public WebClient proxyWebClient() {
        // 1. 配置代理服务器
        HttpClient httpClient = HttpClient.create()
                .proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP)
                        .host("127.0.0.1") // 您的代理主机名
                        .port(7897));     // 您的代理端口号

        // 2. 将配置了代理的 HttpClient 应用到 WebClient
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024)) // 设置最大内存大小为16MB
                .build();
    }
}
