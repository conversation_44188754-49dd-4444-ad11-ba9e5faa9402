package com.example.pure.config;

import com.example.pure.filter.JwtFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Actuator安全配置类
 * 配置Actuator端点的访问权限
 * 使用JWT认证保护Actuator端点
 *
 * <AUTHOR> Name
 * @since 1.0.0
 */
@Configuration
public class ActuatorSecurityConfig {

    private final JwtFilter jwtFilter;

    @Autowired
    public ActuatorSecurityConfig(JwtFilter jwtFilter) {
        this.jwtFilter = jwtFilter;
    }

    /**
     * 配置Actuator端点的安全过滤链
     * 使用@Order(1)确保这个配置在主安全配置之前处理
     *
     * @param http Spring Security的HTTP安全配置对象
     * @return 配置好的SecurityFilterChain
     * @throws Exception 配置过程中的异常
     */
    @Bean
    @Order(1)
    public SecurityFilterChain actuatorSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .requestMatcher(EndpointRequest.toAnyEndpoint())  // 匹配所有Actuator端点
            .authorizeRequests()
                // 允许这些端点无需认证即可访问
                .requestMatchers(EndpointRequest.to(
                    "health",
                    "info",
                    "prometheus",  // 添加prometheus端点
                    "loggers",     // 添加loggers端点
                    "threaddump"   // 添加threaddump端点
                )).permitAll()
                // 其他端点需要ADMIN角色
                .requestMatchers(EndpointRequest.toAnyEndpoint()).hasRole("ADMIN")
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .csrf().disable()  // 禁用CSRF保护
            .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class);  // 添加JWT过滤器
        return http.build();
    }
}
