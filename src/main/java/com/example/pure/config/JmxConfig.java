package com.example.pure.config;

import org.springframework.boot.actuate.autoconfigure.endpoint.jmx.JmxEndpointAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration;

/**
 * JMX配置类
 * 用于禁用JMX相关的自动配置
 * 避免不必要的JMX端点暴露和MBean注册
 *
 * <AUTHOR> Name
 * @since 1.0.0
 */
@Configuration
@EnableAutoConfiguration(exclude = {
    JmxAutoConfiguration.class,           // 禁用Spring Boot的JMX自动配置
    JmxEndpointAutoConfiguration.class    // 禁用Actuator的JMX端点自动配置
})
public class JmxConfig {
    // 类为空，仅通过注解配置来禁用JMX功能
}
