package com.example.pure.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.SpringDocUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.annotation.AuthenticationPrincipal;

/**
 * OpenAPI (Swagger) 配置类
 *
 * <p>配置API文档的展示内容，包括：
 * <ul>
 *     <li>基本信息配置（标题、描述、版本等）</li>
 *     <li>安全方案配置（JWT Bearer认证）</li>
 *     <li>许可证信息</li>
 *     <li>接口分组和排序</li>
 * </ul>
 * </p>
 *
 * <p>主要特性：
 * <ul>
 *     <li>支持OpenAPI 3.0规范</li>
 *     <li>集成JWT认证文档</li>
 *     <li>自定义API文档信息</li>
 *     <li>支持接口分类展示</li>
 * </ul>
 * </p>
 *
 * <p>访问地址：
 * <ul>
 *     <li>Swagger UI: /api/swagger-ui/index.html</li>
 *     <li>OpenAPI描述: /api/v3/api-docs</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class OpenApiConfig {



    static {
        /**
         * 全局配置，让 Springdoc-OpenAPI 忽略所有被 @AuthenticationPrincipal 注解的参数。
         * 这样，在 Controller 方法中通过 @AuthenticationPrincipal 注入的用户信息，
         * 就不会被错误地当作一个需要客户端传递的 API 参数显示在 Swagger UI 上。
         *
         * 这是一个最佳实践，用于将 Spring Security 的内部注入机制与 OpenAPI 的外部文档解耦。
         */
        SpringDocUtils.getConfig().addAnnotationsToIgnore(AuthenticationPrincipal.class);
    }

    /**
     * 配置OpenAPI文档信息，给前端展示说明怎么用API。
     *
     * <p>配置包括：
     * <ul>
     *     <li>JWT Bearer Token认证方案</li>
     *     <li>API基本信息</li>
     *     <li>许可证信息</li>
     *     <li>安全要求配置</li>
     * </ul>
     * </p>
     *
     * @return OpenAPI配置实例
     */
    @Bean
    public OpenAPI openAPI() {
        final String securitySchemeName = "bearerAuth";
        return new OpenAPI()
            .addSecurityItem(new SecurityRequirement().addList(securitySchemeName))
            .components(
                new Components()
                    .addSecuritySchemes(securitySchemeName,
                        new SecurityScheme()
                            .name(securitySchemeName)
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT")
                    )
            )
            .info(new Info()
                .title("Spring Boot REST API")
                .description("Spring Boot REST API with JWT Authentication")
                .version("1.0")
                .license(new License().name("Apache 2.0").url("http://springdoc.org")));
    }
}
