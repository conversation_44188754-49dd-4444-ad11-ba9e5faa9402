package com.example.pure.exception;

import com.example.pure.constant.ResponseCode;
import lombok.Getter;

/**
 * 业务异常类
 * 直接抛出用throw new ex(),工具类必需再service类抛出，所以就是抛出给上一级处理，则function() throws ex 让service类catch再throw,controller同理
 * 一般抛出异常在service类，少部分情况在controller类抛出，util类一定要抛出给service类抓取后再throw
 * <p>
 * 用于封装业务逻辑异常，包含错误码、错误消息和可选的错误数据。
 * 继承RuntimeException以便于全局异常处理，同时通过组合方式支持泛型数据。
 * </p>
 */
@Getter
public class BusinessException extends RuntimeException {
    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误数据
     */
    private final Object data;

    /**
     * 使用默认错误码创建业务异常
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        this(ResponseCode.BUSINESS_ERROR, message); // 返回构造函数BusinessException(int code, String message)
    }

    /**
     * 使用指定错误码创建业务异常
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.data = null;
    }

    /**
     * 使用指定错误码和错误数据创建业务异常
     *
     * @param code    错误码
     * @param message 错误消息
     * @param data    错误数据
     */
    public BusinessException(int code, String message, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    /**
     * 静态工厂方法，使用默认错误码创建业务异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }

    /**
     * 静态工厂方法，使用指定错误码创建业务异常
     *
     * @param code    错误码
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException of(int code, String message) {
        return new BusinessException(code, message);
    }

    /**
     * 静态工厂方法，使用指定错误码和错误数据创建业务异常
     *
     * @param code    错误码
     * @param message 错误消息
     * @param data    错误数据
     * @param <T>     错误数据类型
     * @return 业务异常实例
     */
    public static <T> BusinessException of(int code, String message, T data) {
        return new BusinessException(code, message, data);
    }

    /**
     * 获取错误数据，并尝试转换为指定类型
     *
     * @param clazz 目标类型的Class对象
     * @param <T>   期望的数据类型
     * @return 转换后的错误数据，如果类型不匹配则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getDataAs(Class<T> clazz) {
        if (data == null) {
            return null;
        }
        if (clazz.isInstance(data)) {
            return (T) data;
        }
        return null;
    }
}
