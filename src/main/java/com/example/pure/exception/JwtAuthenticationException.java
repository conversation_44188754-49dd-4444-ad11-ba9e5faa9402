package com.example.pure.exception;

import org.springframework.security.core.AuthenticationException;

/**
 * JWT认证异常
 */
public class JwtAuthenticationException extends AuthenticationException {

    /**
     * 创建JWT认证异常
     *
     * @param msg 错误消息
     */
    public JwtAuthenticationException(String msg) {
        super(msg);
    }

    /**
     * 创建JWT认证异常
     *
     * @param msg 错误消息
     * @param cause 异常原因
     */
    public JwtAuthenticationException(String msg, Throwable cause) {
        super(msg, cause);
    }

    /**
     * 创建JWT过期异常
     *
     * @return JWT过期异常
     */
    public static JwtAuthenticationException expired() {
        return new JwtAuthenticationException("JWT令牌已过期");
    }


    public static JwtAuthenticationException RefreshExpired() {
        return new JwtAuthenticationException("JWT刷新令牌已过期");
    }

    /**
     * 创建JWT篡改异常
     *
     * @return JWT篡改异常
     */
    public static JwtAuthenticationException tampered() {
        return new JwtAuthenticationException("JWT令牌签名无效，可能被篡改");
    }

    /**
     * 创建设备数量超限异常
     *
     * @return 设备数量超限异常
     */
    public static JwtAuthenticationException deviceLimit() {
        return new JwtAuthenticationException("您的账号已在其他设备登录，当前设备已失效");
    }
}
