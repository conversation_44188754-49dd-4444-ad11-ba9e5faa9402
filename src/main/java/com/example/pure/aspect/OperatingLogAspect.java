package com.example.pure.aspect;

import com.example.pure.common.Result;
import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.model.dto.TokenResponse;
import com.example.pure.model.dto.UserDTO;
import com.example.pure.model.entity.OperatingLog;
import com.example.pure.service.GetRegionByIpService;
import com.example.pure.service.OperatingLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * 操作日志切面
 * 自动记录AuthController和UserController的操作
 */
@Slf4j
@Aspect
@Component
public class OperatingLogAspect {

    private final OperatingLogService operatingLogService;
    private final UserMapper userMapper;
    private final GetRegionByIpService  getRegionByIpService;

    @Autowired
    public OperatingLogAspect(OperatingLogService operatingLogService, UserMapper userMapper,GetRegionByIpService getRegionByIpService) {
        this.operatingLogService = operatingLogService;
        this.userMapper = userMapper;
        this.getRegionByIpService = getRegionByIpService;
    }

    /**
     * 定义切点 - AuthController和UserController的所有方法
     */
    @Pointcut("execution(* com.example.pure.controller.AuthController.*(..)) || " +
            "execution(* com.example.pure.controller.UserController.*(..))")
    public void controllerOperations() {
    }

    /**
     * 在目标方法执行成功后记录操作日志
     */
    @AfterReturning(pointcut = "controllerOperations()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            // 获取当前请求
            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取目标方法信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            // 获取方法名方法名
            Method method = signature.getMethod();
            // 控制器名
            String controllerName = joinPoint.getTarget().getClass().getSimpleName();
            String methodName = method.getName();

            //把方法名转换
            String fullMethodName=methodNameConversion(methodName);

            // 获取用户信息
            String username = "未登录用户";
            Long userId = 0L;

            // 判断是否是登录方法
            if ("AuthController".equals(controllerName) && "login".equals(methodName)) {
                // 从登录方法的返回值中获取用户信息
                if (result instanceof Result) {
                    Result<?> resultObj = (Result<?>) result;
                    if (resultObj.getData() instanceof TokenResponse) {
                        TokenResponse tokenResponse = (TokenResponse) resultObj.getData();
                        // 从token中获取用户名
                        username = tokenResponse.getUsername();
                        // 通过用户名查询用户ID
                        UserDTO user = userMapper.findByUsername(username);
                        if (user != null) {
                            userId = user.getId();
                        }
                    }
                }
            } else {
                // 其他方法从SecurityContext获取用户信息
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null && authentication.isAuthenticated() &&
                    !"anonymousUser".equals(authentication.getPrincipal())) {
                    username = authentication.getName();
                    // 通过用户名查询用户ID
                    UserDTO user = userMapper.findByUsername(username);
                    if (user != null) {
                        userId = user.getId();
                    }
                }
            }

            String requestURI = request.getRequestURI();
            String requestMethod = request.getMethod();
            String userIp=getClientIp(request);

            String address=getRegionByIpService.getRegion(userIp);

            // 创建操作日志对象
            OperatingLog operatingLog = new OperatingLog();
            operatingLog.setUserId(userId);
            operatingLog.setIp(userIp);
            operatingLog.setAddress(address);
            operatingLog.setSystemInfo(getClientSystem(request));
            operatingLog.setBrowser(getClientBrowser(request));
            operatingLog.setSummary("用户" + username + "执行了" +fullMethodName);
            operatingLog.setOperatingTime(LocalDateTime.now());

            // 记录操作日志
            operatingLogService.recordOperatingLog(operatingLog);

        } catch (Exception e) {
            // 记录日志时出现异常，不影响正常业务流程
            log.error("记录操作日志异常", e);
        }
    }

    private String methodNameConversion(String methodName) {
        if ("getCurrentUser".equals(methodName)) {
            return "获取当前用户信息";
        } else if ("getUser".equals(methodName)) {
            return "获取用户信息";
        } else if ("getUserWithUserProfile".equals(methodName)) {
            return "获取用户信息和用户资料";
        } else if ("getAllUsers".equals(methodName)) {
            return "获取所有用户信息";
        } else if ("updateUser".equals(methodName)) {
            return "更新用户信息";
        } else if ("updatePassword".equals(methodName)) {
            return "更新用户密码";
        } else if ("getUsersByPage".equals(methodName)) {
            return "分页获取用户信息";
        }else if ("createUser".equals(methodName)){
            return "创建用户";
        } else if ("login".equals(methodName)) {
            return "登陆";
        }else if ("logout".equals(methodName)) {
            return "退出登陆";
        }else if ("refreshToken".equals(methodName)) {
            return "刷新令牌";
        }
        else {
            return "操作的名称还未定义";
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }

        return ipAddress != null ? ipAddress : "未知";
    }

    /**
     * 获取客户端操作系统
     */
    private String getClientSystem(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return "未知";
        }

        if (userAgent.contains("Windows")) {
            return "Windows";
        } else if (userAgent.contains("Mac")) {
            return "MacOS";
        } else if (userAgent.contains("Linux")) {
            return "Linux";
        } else if (userAgent.contains("Android")) {
            return "Android";
        } else if (userAgent.contains("iOS") || userAgent.contains("iPhone") || userAgent.contains("iPad")) {
            return "iOS";
        } else {
            return "其他";
        }
    }

    /**
     * 获取客户端浏览器
     */
    private String getClientBrowser(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return "未知";
        }

        if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            return "Internet Explorer";
        } else if (userAgent.contains("Firefox")) {
            return "Firefox";
        } else if (userAgent.contains("Chrome") && !userAgent.contains("Edg")) {
            return "Chrome";
        } else if (userAgent.contains("Safari") && !userAgent.contains("Chrome")) {
            return "Safari";
        } else if (userAgent.contains("Edg")) {
            return "Edge";
        } else if (userAgent.contains("Opera") || userAgent.contains("OPR")) {
            return "Opera";
        } else {
            return "其他";
        }
    }
}
