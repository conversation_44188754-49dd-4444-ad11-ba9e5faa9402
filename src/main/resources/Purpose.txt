项目目标:
本项目旨在提供一套基于 Spring Boot 和 MyBatis 的后端 API 服务，请加上注释，用于实现核心的消息通知功能。主要包括系统向指定用户发送消息、用户查询未读消息数量、用户查看消息列表以及将消息标记为已读等功能。该 API 服务可以作为网页应用或小程序等客户端的消息通知模块的后端支持，最佳实践完成
核心功能:
系统消息发送: 允许系统或管理员向特定用户发送通知、公告等消息。
未读消息计数: 用户可以实时查询自己当前的未读消息数量。
消息列表查看: 用户可以分页查看自己的消息列表，并可按已读/未读状态筛选。
消息标记已读: 用户可以将单条或所有未读消息标记为已读，从而清除未读计数。
请把这些api按Messgages为命名
文件位置：控制器controller、服务类service、mapper类同名文件夹下、
技术栈:
后端框架: Spring Boot
数据持久化: MyBatis
数据库: MySQL (可替换为其他关系型数据库)
API 协议: HTTP/RESTful JSON
构建工具: gradle
辅助库: Lombok (简化代码)
预期用户:
需要集成消息通知功能的各类应用程序的后端开发团队。
设计方案
1. 整体架构:
采用经典的三层架构：
Controller 层 (API接口层):
负责接收客户端的 HTTP 请求，对请求参数进行初步校验（未来可集成 Bean Validation）。
调用 Service 层处理业务逻辑。
将 Service 层的处理结果返回控制器后使用common文件下下的Result.java（如果要使用分页返回结果，可以请使用common类里的PageResult存放页数总数的数据，查询到的实际数据比如说messenge数据使用PageFinalResult存放到T list这里，还有一个变量把刚刚的PageResult存放到 PageResult pageResult里，然后把PageFinalResult通过Result类返回结果给客户端 ）来封装成统一的JSON响应格式返回给客户端。
使用 @RestController 和相关的 Spring MVC 注解定义 API 端点。
Service 层 (业务逻辑层):
封装核心的业务逻辑，如创建消息、关联用户、更新已读状态、查询数据等。
通过 @Service 注解标记，并使用 @Transactional 注解保证数据操作的原子性和一致性。
如果控制器要返回DTO类的话使用请求的DTO类创建手动bulider转换model下的entity类来进行数据库操作，然后通过手动bulider再转成DTO输出
调用 Mapper 层与数据库进行交互。
Mapper层(数据访问层):
使用 MyBatis 框架，通过接口（Mapper Interface）和 XML 文件（Mapper XML）定义 SQL 语句。
负责执行数据库的 CRUD 操作。
通过 @Mapper 注解标记接口，并由 Spring Boot 自动扫描和代理。

生成的dto类请放在model文件夹下对应的dto或entity类里，以下是mysql表的设计请你转换为java的类生成,使用Lombok来简化model类里的代码
2. 数据库设计:
messages 表: 存储所有消息的元数据，包括发送者（系统或用户）、消息类型、标题、内容、目标信息、创建时间等。
id:本次消息的id，主key
sender_id: 用于区分系统消息和用户消息的发送者。
target_type 和 target_id: 灵活定义消息的接收目标。
created_time:本条messages的创建实践

user_messages 表: 核心关联表，记录用户与消息之间的关系，最重要的是 is_read 状态。
id:主key
user_id: 接收消息的用户。
message_id: 对应的消息id。
is_read: 标记用户是否已读该消息，是实现未读计数和状态切换的关键。
read_time: 记录读取时间
user表 : 存储用户信息，user_messages 表通过 user_id 的联表查询与其关联。这个user表是我自己本来就有的表不需要创建成类

3. API 设计 (RESTful 风格):
功能	HTTP 方法	URL	请求体 (示例)	
系统发送消息给用户	POST	/api/messages/system/send	SystemMessageRequestDTO (包含 recipientUserId, title, content, messageType等)	
获取用户未读消息数	GET	/api/messages/users/{userId}/unread-count	-	UnreadCountDTO ({"unreadCount": 5})
标记用户所有未读为已读	POST	/api/messages/users/{userId}/mark-all-as-read	
标记用户单条消息为已读	POST	/api/messages/users/{userId}/message/{messageId}/mark-as-read	
获取用户消息列表 (分页)	GET	/api/messages/users/{userId}	Query Params: page, size, status (unread/read/all)	 使用分页返回结果，可以请使用common类里的PageResult存放页数总数的数据，查询到的实际数据比如说messenge数据使用PageFinalResult存放到T list这里，还有一个变量把刚刚的PageResult存放到 PageResult pageResult里，然后把PageFinalResult通过Result类返回结果给客户端 