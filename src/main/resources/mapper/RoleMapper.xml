<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.pure.mapper.primary.RoleMapper">
    <!-- 角色结果映射 -->
    <resultMap id="RoleResultMap" type="com.example.pure.model.entity.Role">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createdTime" column="create_time"/>
        <result property="updatedTime" column="update_time"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, name, create_time, update_time
    </sql>

    <!-- 插入角色 -->
    <insert id="insertRole" parameterType="com.example.pure.model.entity.Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO roles (name, create_time, update_time)
        VALUES (#{name}, #{createdTime}, #{updatedTime})
    </insert>

    <!-- 根据ID查找角色 -->
    <select id="findById" resultMap="RoleResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM roles
        WHERE id = #{id}
    </select>

    <!-- 根据角色名查找角色 -->
    <select id="findByName" resultMap="RoleResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM roles
        WHERE name = #{name}
    </select>

    <!-- 查找所有角色 -->
    <select id="findAll" resultMap="RoleResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM roles
        ORDER BY id
    </select>

    <!-- 更新角色 -->
    <update id="updateRole" parameterType="com.example.pure.model.entity.Role">
        UPDATE roles
        <set>
            <if test="name != null">name = #{name},</if>
            update_time = #{updatedTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除角色 -->
    <delete id="deleteRole">
        DELETE FROM roles WHERE id = #{id}
    </delete>

    <!-- 删除角色关联的所有用户 -->
    <delete id="deleteUserRoles">
        DELETE FROM user_roles WHERE role_id = #{roleId}
    </delete>









    <!-- 查询指定用户ID的所有角色 -->
    <select id="findByUserId" resultMap="RoleResultMap">
        SELECT r.*
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>
</mapper>
