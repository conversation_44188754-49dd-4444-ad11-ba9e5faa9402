<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 用户Mapper配置 -->
<!--映射地址路径-->
<mapper namespace="com.example.pure.mapper.primary.UserMapper">
    <!-- 用户结果映射 -->
    <!--结果id和引用结果路径，resultMap详细返回、自定义返回的结果
    允许你显式地、详细地定义数据库列和 Java 对象属性之间的映射关系-->
    <resultMap id="UserResultMap" type="com.example.pure.model.entity.User">
        <!--唯一key映射对应，java端对比SQL端-->
        <id property="id" column="id"/>
        <!--结果映射对应，java端对比SQL端-->
        <result property="username" column="username"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="enabled" column="enabled"/>
        <result property="accountNonExpired" column="account_non_expired"/>
        <result property="accountNonLocked" column="account_non_locked"/>
        <result property="credentialsNonExpired" column="credentials_non_expired"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="refreshTokenExpires" column="refresh_token_expires"/>
    </resultMap>

    <resultMap id="UserDtoResultMap" type="com.example.pure.model.dto.UserDTO">
        <result property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="createdTime" column="created_time"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="password" column="password"/>
    </resultMap>

    <resultMap id="UserWithDetailsResultMap" type="com.example.pure.model.dto.UserWithUserProfileDTO">
        <id property="id" column="u_id"/> <!-- 使用别名避免歧义 -->
        <result property="username" column="u_username"/>
        <result property="createdTime" column="u_created_time"/>
        <result property="updatedTime" column="u_updated_time"/>
        <result property="lastLoginTime" column="u_last_login_time"/>
        <!-- 映射 user_profile表字段 -->
        <result property="email" column="up_email"/>
        <result property="nickname" column="up_nickname"/>
        <result property="avatar" column="up_avatar"/>
        <result property="phone" column="up_phone"/>
        <result property="description" column="up_description"/>
    </resultMap>

    <!-- 通用列，快捷引用全部的列 -->
    <sql id="Base_Column_List">
        id, username, password, email, nickname, avatar, gender, enabled,
        account_non_expired, account_non_locked, credentials_non_expired,
        created_time, updated_time, last_login_time, refresh_token
    </sql>

    <!--Dto-->
    <sql id="Dto_column_List">
            id,
            username,
            password,
            created_time,
            last_login_time,
            updated_time
    </sql>


    <!-- 插入用户，id为mapper.java文件的方法，par...为指定传入类型为User类，Use...启用自动生成主key，
    主key反序列到User对象里对应变量 , keyColumn="id",对应数据库列的名称生成主键后应该从那返回主键值 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.User" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO user (
            username, password, enabled,
            account_non_expired, account_non_locked, credentials_non_expired,
            created_time, updated_time
            <if test="refreshToken != null">
                , refresh_token
            </if>
        ) VALUES (
            #{username}, #{password},  #{enabled},
            #{accountNonExpired}, #{accountNonLocked}, #{credentialsNonExpired},
            NOW(), NOW()
            <if test="refreshToken != null">
                , #{refreshToken}
            </if>
        )
    </insert>

    <!-- 根据ID查找用户普通信息，id为mapper.java的方法，结果映射配置对应映射出来的结果 -->
    <select id="findById" resultMap="UserDtoResultMap">
    <!--refid引用base...保存的列，查询这些列-->
        SELECT <include refid="Dto_column_List">
    </include>
        FROM user
        WHERE id = #{id}
    </select>

    <select id="findByUsername" resultMap="UserDtoResultMap">
        <!--refid引用base...保存的列，查询这些列-->
        SELECT <include refid="Dto_column_List">
    </include>
        FROM user
        WHERE username =#{username}
    </select>


    <select id="findByUserWithPasswordByUsername" resultMap="UserResultMap">
        SELECT *
        FROM user
        WHERE username =#{username}
    </select>

    <select id="findUserWithPasswordByUserId" resultMap="UserResultMap">
        SELECT *
        FROM user
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查找用户全部资料 -->
    <select id="UserWithUserProfileDTOByUsername" resultMap="UserWithDetailsResultMap">
        SELECT
            u.id AS u_id, u.username AS u_username,
            u.created_time AS u_created_time, u.updated_time AS u_updated_time,
            up.email AS up_email,
            up.nickname AS up_nickname,
            up.avatar AS up_avatar,
            up.phone AS up_phone,
            up.description AS up_description
        FROM user u
        LEFT JOIN user_profile up ON u.id = up.id
        WHERE u.username = #{username}
    </select>

    <!-- 查找所有用户 -->
    <select id="findAll" resultMap="UserDtoResultMap">
        SELECT <include refid="Dto_column_List">
    </include>
        FROM user
    </select>


    <!-- 更新 -->
    <update id="update" parameterType="com.example.pure.model.entity.User">
        UPDATE user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="accountNonExpired != null">account_non_expired = #{accountNonExpired},</if>
            <if test="accountNonLocked != null">account_non_locked = #{accountNonLocked},</if>
            <if test="credentialsNonExpired != null">credentials_non_expired = #{credentialsNonExpired},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="refreshTokenExpires != null">refresh_token_expires = #{refreshTokenExpires},</if>
            updated_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteById">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 统计用户名数量 -->
    <select id="countByUsername" resultType="int">
        SELECT COUNT(*) FROM user WHERE username = #{username}
    </select>

    <!-- 更新用户的刷新令牌 -->
    <update id="updateRefreshToken">
        UPDATE user
        SET refresh_token = #{refreshToken},
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 插入用户角色关系 -->
    <insert id="insertUserRole">
        INSERT INTO user_roles (user_id, role_id) VALUES (#{userId}, #{roleId})
    </insert>


    <!-- 删除用户角色关系 -->
    <delete id="deleteUserRoles">
        DELETE FROM user_roles WHERE user_id = #{userId}
    </delete>

    <!-- 手动分页查询用户列表 -->
    <select id="findByPage" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        <!--根据where条件内语句条件智能生成语句-->
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                    <!-- LIKE模糊搜索，CONCAT拼接字符串，%为通配符，匹配所有包含关键字的变量 -->
                    username LIKE CONCAT('%', #{keyword}, '%')
                    OR nickname LIKE CONCAT('%', #{keyword}, '%')
                    OR email LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        <!-- 使用参数化的ORDER BY语句（根据值来选择一条语句查询），避免SQL注入 -->
        <choose>
            <when test="orderBy == 'createdTime'">ORDER BY created_time</when>
            <when test="orderBy == 'updatedTime'">ORDER BY updated_time</when>
            <when test="orderBy == 'lastLoginTime'">ORDER BY last_login_time</when>
            <when test="orderBy == 'id'">ORDER BY id</when>
            <when test="orderBy == 'username'">ORDER BY username</when>
            <when test="orderBy == 'email'">ORDER BY email</when>
            <when test="orderBy == 'nickname'">ORDER BY nickname</when>
            <when test="orderBy == 'gender'">ORDER BY gender</when>
            <when test="orderBy == 'score'">ORDER BY score</when>
            <otherwise>ORDER BY id</otherwise>
        </choose>
        <choose>
            <when test="orderDirection == 'asc'">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计符合条件的     <when test="orderBy == 'id'">id</when>
                   用户总数 -->
    <select id="countUsers" resultType="int">
        <!-- 查询所有行 -->
        SELECT COUNT(*)
        FROM user
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                    username LIKE CONCAT('%', #{keyword}, '%')
                    OR nickname LIKE CONCAT('%', #{keyword}, '%')
                    OR email LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
    </select>
</mapper>
