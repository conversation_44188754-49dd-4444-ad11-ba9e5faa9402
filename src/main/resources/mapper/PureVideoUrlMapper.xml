<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.PureVideoUrlMapper">

    <resultMap id="VideoInfoResultMap" type="com.example.pure.model.entity.VideoInfo">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="coverImageUrl" column="cover_image_url"/>
        <result property="description" column="description"/>
        <result property="createTime" column="created_time"/>
        <result property="updateTime" column="updated_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        id, title, cover_image_url, description, created_time, updated_time
    </sql>

    <!-- 插入视频信息 -->
    <insert id="insertVideoInfo" parameterType="com.example.pure.model.entity.VideoInfo"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO video_info(
            title, cover_image_url, description, created_time, updated_time
        )
        VALUES (
            #{title}, #{coverImageUrl}, #{description}, NOW(), NOW()
        )
    </insert>

    <!-- 根据标题查找视频信息 -->
    <select id="findVideoInfoByTitle" resultMap="VideoInfoResultMap">
        SELECT <include refid="Base_Column_List"/> FROM video_info WHERE title = #{title} LIMIT 1
    </select>

    <!--查找视频信息通过主题-->
    <select id="findVideoInfoWithPagination" resultMap="VideoInfoResultMap">
        SELECT <include refid="Base_Column_List"/> FROM video_info
        <if test="keyword != null and keyword != ''">
            WHERE title LIKE CONCAT('%', #{keyword}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countVideoInfo" resultType="int">
        SELECT COUNT(*) FROM video_info
        <if test="keyword != null and keyword != ''">
            WHERE title LIKE CONCAT('%', #{keyword}, '%')
        </if>
    </select>

    <!-- 根据类型查询视频信息 -->
    <select id="findVideoInfoByTypeWithPagination" resultMap="VideoInfoResultMap">
        SELECT vi.id,vi.title,vi.cover_image_url,vi.description FROM video_info vi
            INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id
            INNER JOIN video_type vt ON vitl.type_id =vt.id
        WHERE vt.name = #{keyword}
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据类型查询这个类型的视频总数 -->
    <select id="countVideoInfoByType" resultType="int">
        SELECT COUNT(*) FROM video_info vi
        INNER JOIN video_info_type_link vitl ON vi.id =vitl.video_info_id
        INNER JOIN video_type vt ON vitl.type_id =vt.id
        WHERE vt.name = #{keyword}
    </select>

</mapper>
