<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.OperatingLogMapper">

    <!-- 操作日志结果映射 -->
    <resultMap id="OperatingLogResultMap" type="com.example.pure.model.entity.OperatingLog">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="ip" column="ip"/>
        <result property="address" column="address"/>
        <result property="systemInfo" column="system_info"/>
        <result property="browser" column="browser"/>
        <result property="summary" column="summary"/>
        <result property="operatingTime" column="operating_time"/>
    </resultMap>

    <!-- 插入操作日志 -->
    <insert id="insertOperatingLog" parameterType="com.example.pure.model.entity.OperatingLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO operating_log (
            user_id, ip, address, system_info, browser, summary, operating_time
        ) VALUES (
            #{userId}, #{ip}, #{address}, #{systemInfo}, #{browser}, #{summary}, #{operatingTime}
        )
    </insert>

    <!-- 根据用户ID查询操作日志 -->
    <select id="findOperatingLogsByUserId" resultMap="OperatingLogResultMap">
        SELECT * FROM operating_log
        WHERE user_id = #{userId}
        ORDER BY operating_time DESC
        LIMIT #{offset},#{limit}
    </select>

    <!-- 统计用户操作日志数量 -->
    <select id="countOperatingLogsByUserId" resultType="int">
        SELECT COUNT(*) FROM operating_log WHERE user_id = #{userId}
    </select>

</mapper>
