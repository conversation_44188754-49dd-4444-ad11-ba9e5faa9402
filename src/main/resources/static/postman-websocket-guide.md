# WebSocket测试指南 - Postman

本指南介绍如何使用Postman测试WebSocket功能。

## 准备工作

1. 确保应用已启动并运行在`http://localhost:8080`
2. 确保安装了最新版本的Postman (支持WebSocket功能)

## 重要提示

为了解决之前的STOMP帧不完整问题，我们进行了以下更改：

1. 新增了专用于原生WebSocket测试的端点：`/ws-raw`
2. 保留了带SockJS支持的端点：`/ws`
3. 修改了WebSocket配置以提高兼容性

## 使用Postman连接WebSocket

### 连接到WebSocket服务器

1. 打开Postman
2. 点击左上角的"New"按钮
3. 选择"WebSocket Request"
4. 在URL字段中输入`ws://localhost:8080/ws-raw`（注意使用新的端点）
5. 点击"Connect"按钮进行连接

### 发送STOMP帧的正确格式

使用Postman时，需要按照STOMP协议的精确格式发送消息。以下是正确的格式（注意每行格式和结尾的空行与NULL字符）：

#### 连接帧

```
CONNECT
accept-version:1.2
host:localhost
login:postman-user

```

> 注意：上面有一个空行，在Postman中需要在最后添加NULL字符（\0）

#### 订阅帧

```
SUBSCRIBE
id:sub-1
destination:/topic/messages

```

> 注意：上面有一个空行，在Postman中需要在最后添加NULL字符（\0）

#### 发送消息帧

```
SEND
destination:/app/message
content-type:application/json

{"type":"text","content":"Hello from Postman","sender":"postman-user"}
```

> 注意：消息内容前有一个空行，在消息内容后需要添加NULL字符（\0）

### 添加NULL字符（\0）的方法

在Postman中添加NULL字符有几种方法：

1. 在Windows上，按下Alt+0然后松开
2. 复制并粘贴一个NULL字符
3. 在某些版本的Postman中，可以在消息末尾直接输入`\0`

### 订阅主题

连接成功后，使用上述SUBSCRIBE帧格式订阅以下主题：

1. 广播消息: `/topic/messages`
2. 回显消息: `/topic/echo`
3. 聊天消息: `/topic/chat`
4. 通知消息: `/topic/notifications`
5. 房间消息: `/topic/room/room1`
6. 私人消息: `/user/queue/messages`

### 发送消息

可以发送以下类型的消息（使用上述SEND帧格式）：

1. 广播消息: 目标地址为 `/app/message`
2. 回显消息: 目标地址为 `/app/echo`
3. 聊天消息: 目标地址为 `/app/chat`
4. 房间消息: 目标地址为 `/app/room/room1`
5. 私人消息: 目标地址为 `/app/private/user1`

### 使用REST API触发WebSocket消息

除了直接使用WebSocket发送消息外，还可以使用REST API触发WebSocket消息推送:

1. 发送广播消息:
   - `GET http://localhost:8080/api/ws-test/send/Hello+World`

2. 发送通知:
   - `GET http://localhost:8080/api/ws-test/notify`

3. 发送私人消息:
   - `GET http://localhost:8080/api/ws-test/send-to-user/postman-user?message=Hello+from+REST+API`

4. 发送房间消息:
   - `GET http://localhost:8080/api/ws-test/send-to-room/room1?message=Room+message+from+REST+API`

### 断开连接

完成测试后，发送以下消息断开连接:

```
DISCONNECT
receipt:bye

```

> 注意：上面有一个空行，在Postman中需要在最后添加NULL字符（\0）

## 故障排除

1. 如果仍然收到"Incomplete STOMP frame"错误：
   - 确保每个STOMP帧后都有NULL字符（\0）
   - 确保头部和正文之间有一个空行
   - 尝试使用我们提供的HTML测试页面确认服务器工作正常

2. 如果连接失败：
   - 确保应用程序正在运行
   - 检查是否使用了正确的端点（/ws-raw）
   - 确认没有防火墙或代理阻止WebSocket连接

3. 为简化测试，可访问内置的测试页面：
   - 访问 `http://localhost:8080/stomp-test.html`

## 备注

1. 每个STOMP帧必须以NULL字符（\0）结尾，这是STOMP协议的要求
2. 头部和正文之间必须有一个空行
3. 不要在命令或头部名称周围加引号
4. 头部键值对之间使用冒号（:）而不是等号（=） 