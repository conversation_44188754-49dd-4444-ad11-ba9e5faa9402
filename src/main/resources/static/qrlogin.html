<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码登录</title>
    <script>
        // 配置基础URL
        const BASE_URL = 'http://localhost:8080';  // 根据实际部署环境修改
    </script>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f7f7;
            color: #333;
            display: flex;
            justify-content: center;
        }
        
        .container {
            max-width: 500px;
            width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        
        h1 {
            color: #3f51b5;
            margin-bottom: 30px;
        }
        
        .qr-container {
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
            background: #fcfcfc;
            width: 250px;
            height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .qr-image {
            max-width: 200px;
            max-height: 200px;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.pending {
            background: #fff9c4;
            color: #fbc02d;
        }
        
        .status.scanned {
            background: #b3e5fc;
            color: #0288d1;
        }
        
        .status.confirmed {
            background: #c8e6c9;
            color: #388e3c;
        }
        
        .status.expired {
            background: #ffcdd2;
            color: #d32f2f;
        }
        
        .btn {
            background: #3f51b5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #303f9f;
        }
        
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            flex-direction: column;
            visibility: hidden;
        }
        
        .user-info {
            margin-top: 20px;
            text-align: left;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
            display: none;
        }
        
        .user-info p {
            margin: 5px 0;
        }
        
        .timer {
            position: absolute;
            bottom: 5px;
            right: 5px;
            font-size: 12px;
            color: #757575;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>二维码扫码登录</h1>
        
        <div class="qr-container" id="qrContainer">
            <img id="qrImage" class="qr-image" src="" alt="二维码加载中...">
            <div class="overlay" id="qrOverlay">
                <div id="overlayText"></div>
            </div>
            <div class="timer" id="timer"></div>
        </div>
        
        <div class="status pending" id="statusText">
            等待扫描...
        </div>
        
        <div class="user-info" id="userInfo">
            <h3>登录成功</h3>
            <p><strong>用户名:</strong> <span id="username"></span></p>
            <p><strong>昵称:</strong> <span id="nickname"></span></p>
            <p><strong>邮箱:</strong> <span id="email"></span></p>
        </div>
        
        <button class="btn" id="refreshBtn">刷新二维码</button>
    </div>
    
    <!-- 引入SockJS和STOMP库 -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stomp-websocket@2.3.4-next/lib/stomp.min.js"></script>
    
    <script>
        // DOM 元素
        const qrImage = document.getElementById('qrImage');
        const qrContainer = document.getElementById('qrContainer');
        const qrOverlay = document.getElementById('qrOverlay');
        const overlayText = document.getElementById('overlayText');
        const statusText = document.getElementById('statusText');
        const userInfo = document.getElementById('userInfo');
        const username = document.getElementById('username');
        const nickname = document.getElementById('nickname');
        const email = document.getElementById('email');
        const refreshBtn = document.getElementById('refreshBtn');
        const timer = document.getElementById('timer');
        
        // 二维码状态和ID
        let qrId = null;
        let expireTime = null;
        let timerInterval = null;
        let stompClient = null;
        let reconnecting = false;
        
        // 更新状态样式
        function updateStatus(status, message) {
            statusText.className = `status ${status.toLowerCase()}`;
            statusText.textContent = message;
        }
        
        // 显示二维码覆盖层
        function showOverlay(text) {
            overlayText.textContent = text;
            qrOverlay.style.visibility = 'visible';
        }
        
        // 隐藏二维码覆盖层
        function hideOverlay() {
            qrOverlay.style.visibility = 'hidden';
        }
        
        // 更新倒计时
        function updateTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
            }
            
            timerInterval = setInterval(() => {
                if (!expireTime) return;
                
                const now = new Date();
                const diff = expireTime - now;
                
                if (diff <= 0) {
                    clearInterval(timerInterval);
                    updateStatus('expired', '二维码已过期，请刷新');
                    showOverlay('已过期');
                    return;
                }
                
                const seconds = Math.ceil(diff / 1000);
                timer.textContent = `${seconds}秒后过期`;
            }, 1000);
        }
        
        // 生成二维码
        async function generateQRCode() {
            try {
                // 清除之前的定时器
                if (timerInterval) {
                    clearInterval(timerInterval);
                }
                
                // 重置状态
                updateStatus('pending', '等待扫描...');
                hideOverlay();
                userInfo.style.display = 'none';
                
                // 请求新的二维码
                const response = await fetch(BASE_URL + '/api/qrlogin/create');
                const data = await response.json();
                
                if (!response.ok || !data.success) {
                    throw new Error(data.error || '生成二维码失败');
                }
                
                // 更新二维码图片
                qrImage.src = data.data.qrContent;
                
                // 保存二维码ID和过期时间
                qrId = data.data.qrId;
                expireTime = new Date(data.data.expireTime);
                
                // 更新倒计时
                updateTimer();
                
                // 初始化WebSocket连接
                initWebSocket();
                
                // 启动轮询
                startPolling();
            } catch (error) {
                console.error('生成二维码失败:', error);
                updateStatus('expired', '生成二维码失败，请重试');
                showOverlay('生成失败');
            }
        }
        
        // 初始化WebSocket连接
        function initWebSocket() {
            // 如果已连接，先断开
            if (stompClient && stompClient.connected) {
                stompClient.disconnect();
            }
            
            // 创建SockJS连接
            const socket = new SockJS(BASE_URL + '/ws');
            stompClient = Stomp.over(socket);
            
            // 连接WebSocket服务器
            stompClient.connect({}, frame => {
                console.log('WebSocket已连接:', frame);
                
                // 订阅当前二维码状态的主题
                stompClient.subscribe(`/topic/qrlogin/${qrId}`, message => {
                    const statusData = JSON.parse(message.body);
                    handleStatusUpdate(statusData);
                });
                
                // 发送一条消息以订阅二维码状态
                stompClient.send("/app/qrlogin/subscribe", {}, JSON.stringify({ qrId }));
                
                reconnecting = false;
            }, error => {
                console.error('WebSocket连接失败:', error);
                
                // 如果连接失败且不是正在重连中，尝试重连
                if (!reconnecting) {
                    reconnecting = true;
                    setTimeout(initWebSocket, 3000);
                }
            });
        }
        
        // 启动轮询检查二维码状态
        function startPolling() {
            // 每3秒检查一次状态
            const pollInterval = setInterval(async () => {
                try {
                    // 如果二维码已过期或不存在，停止轮询
                    if (!qrId || (expireTime && new Date() > expireTime)) {
                        clearInterval(pollInterval);
                        return;
                    }
                    
                    const response = await fetch(BASE_URL + '/api/qrlogin/status/' + qrId);
                    
                    // 如果请求失败，可能是二维码已失效
                    if (!response.ok) {
                        clearInterval(pollInterval);
                        updateStatus('expired', '二维码已失效，请刷新');
                        showOverlay('已失效');
                        return;
                    }
                    
                    const data = await response.json();
                    handleStatusUpdate(data);
                    
                    // 如果状态是已确认或已过期，停止轮询
                    if (data.status === 'CONFIRMED' || data.status === 'EXPIRED' || data.expired) {
                        clearInterval(pollInterval);
                    }
                } catch (error) {
                    console.error('获取状态失败:', error);
                }
            }, 3000);
        }
        
        // 处理状态更新
        function handleStatusUpdate(data) {
            console.log('状态更新:', data);
            
            if (data.expired) {
                updateStatus('expired', '二维码已过期，请刷新');
                showOverlay('已过期');
                clearInterval(timerInterval);
                return;
            }
            
            switch (data.status) {
                case 'PENDING':
                    updateStatus('pending', '等待扫描...');
                    hideOverlay();
                    break;
                    
                case 'SCANNED':
                    updateStatus('scanned', '已扫描，请在手机上确认');
                    hideOverlay();
                    break;
                    
                case 'CONFIRMED':
                    updateStatus('confirmed', '登录成功');
                    showOverlay('登录成功');
                    
                    // 显示用户信息
                    if (data.userInfo) {
                        username.textContent = data.userInfo.username || 'N/A';
                        nickname.textContent = data.userInfo.nickname || 'N/A';
                        email.textContent = data.userInfo.email || 'N/A';
                        userInfo.style.display = 'block';
                    }
                    
                    // 保存令牌到本地存储
                    if (data.accessToken) {
                        localStorage.setItem('accessToken', data.accessToken);
                    }
                    if (data.refreshToken) {
                        localStorage.setItem('refreshToken', data.refreshToken);
                    }
                    
                    // 3秒后跳转到首页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                    break;
                    
                case 'CANCELED':
                    updateStatus('expired', '登录已取消，请刷新二维码');
                    showOverlay('已取消');
                    break;
                    
                case 'EXPIRED':
                    updateStatus('expired', '二维码已过期，请刷新');
                    showOverlay('已过期');
                    clearInterval(timerInterval);
                    break;
                    
                default:
                    break;
            }
        }
        
        // 点击刷新按钮
        refreshBtn.addEventListener('click', generateQRCode);
        
        // 页面加载时生成二维码
        window.addEventListener('load', generateQRCode);
    </script>
</body>
</html> 