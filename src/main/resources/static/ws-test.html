<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .left-panel, .right-panel {
            flex: 1;
        }
        .connection-status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .message-box {
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 4px;
        }
        .received {
            background-color: #e2f0fb;
            border-left: 4px solid #4a9bd1;
        }
        .sent {
            background-color: #f0f7e6;
            border-left: 4px solid #8bc34a;
        }
        .system {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        form {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea, button {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4a9bd1;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #357ab7;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>WebSocket测试</h1>
    
    <div class="connection-status disconnected" id="status">
        未连接
    </div>
    
    <div class="container">
        <div class="left-panel">
            <h2>连接设置</h2>
            
            <form id="connect-form">
                <div>
                    <label for="username">用户名:</label>
                    <input type="text" id="username" value="user1">
                </div>
                
                <button type="submit" id="connect">连接</button>
                <button type="button" id="disconnect" disabled>断开连接</button>
            </form>
            
            <h2>发送消息</h2>
            
            <form id="send-form">
                <div>
                    <label for="destination-type">目标类型:</label>
                    <select id="destination-type">
                        <option value="broadcast">广播消息 (/app/message)</option>
                        <option value="echo">回显消息 (/app/echo)</option>
                        <option value="chat">聊天消息 (/app/chat)</option>
                        <option value="room">房间消息 (/app/room/{roomId})</option>
                        <option value="private">私人消息 (/app/private/{username})</option>
                    </select>
                </div>
                
                <div id="roomIdContainer" style="display: none;">
                    <label for="roomId">房间ID:</label>
                    <input type="text" id="roomId" value="room1">
                </div>
                
                <div id="recipientContainer" style="display: none;">
                    <label for="recipient">接收者:</label>
                    <input type="text" id="recipient" value="user2">
                </div>
                
                <div>
                    <label for="message-type">消息类型:</label>
                    <select id="message-type">
                        <option value="text">文本消息</option>
                        <option value="notification">通知消息</option>
                        <option value="command">命令消息</option>
                    </select>
                </div>
                
                <div>
                    <label for="message">消息内容:</label>
                    <textarea id="message" rows="3">Hello, WebSocket!</textarea>
                </div>
                
                <button type="submit" disabled>发送消息</button>
            </form>
        </div>
        
        <div class="right-panel">
            <h2>消息记录</h2>
            <div class="message-box" id="messages"></div>
            
            <button type="button" id="clear-messages">清空消息</button>
        </div>
    </div>
    
    <!-- 引入SockJS和STOMP客户端库 -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.5.0/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    
    <script>
        // DOM元素
        const statusElement = document.getElementById('status');
        const connectForm = document.getElementById('connect-form');
        const sendForm = document.getElementById('send-form');
        const messagesElement = document.getElementById('messages');
        const usernameInput = document.getElementById('username');
        const messageInput = document.getElementById('message');
        const connectButton = document.getElementById('connect');
        const disconnectButton = document.getElementById('disconnect');
        const sendButton = sendForm.querySelector('button[type="submit"]');
        const clearButton = document.getElementById('clear-messages');
        const destinationTypeSelect = document.getElementById('destination-type');
        const messageTypeSelect = document.getElementById('message-type');
        const roomIdContainer = document.getElementById('roomIdContainer');
        const roomIdInput = document.getElementById('roomId');
        const recipientContainer = document.getElementById('recipientContainer');
        const recipientInput = document.getElementById('recipient');
        
        // WebSocket客户端
        let stompClient = null;
        let subscription = null;
        
        // 显示消息
        function showMessage(message, type) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', type);
            
            // 时间戳
            const timestamp = new Date().toLocaleTimeString();
            
            // 格式化消息内容
            let content = '';
            if (typeof message === 'object') {
                content = `<pre>${JSON.stringify(message, null, 2)}</pre>`;
            } else {
                content = message;
            }
            
            messageElement.innerHTML = `<strong>${timestamp}</strong>: ${content}`;
            messagesElement.appendChild(messageElement);
            messagesElement.scrollTop = messagesElement.scrollHeight;
        }
        
        // 连接WebSocket
        function connect(event) {
            event.preventDefault();
            
            const username = usernameInput.value.trim();
            if (!username) {
                alert('请输入用户名');
                return;
            }
            
            // 创建SockJS和STOMP客户端
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            // 配置STOMP客户端
            stompClient.connect({username: username}, frame => {
                // 更新状态
                statusElement.textContent = `已连接 - ${username}`;
                statusElement.classList.remove('disconnected');
                statusElement.classList.add('connected');
                
                // 启用/禁用按钮
                connectButton.disabled = true;
                disconnectButton.disabled = false;
                sendButton.disabled = false;
                
                // 显示系统消息
                showMessage(`连接成功! Session ID: ${frame.headers['user-name']}`, 'system');
                
                // 订阅广播消息
                stompClient.subscribe('/topic/messages', message => {
                    const messageBody = JSON.parse(message.body);
                    showMessage(messageBody, 'received');
                });
                
                // 订阅回显消息
                stompClient.subscribe('/topic/echo', message => {
                    showMessage(message.body, 'received');
                });
                
                // 订阅聊天消息
                stompClient.subscribe('/topic/chat', message => {
                    const messageBody = JSON.parse(message.body);
                    showMessage(messageBody, 'received');
                });
                
                // 订阅通知消息
                stompClient.subscribe('/topic/notifications', message => {
                    const messageBody = JSON.parse(message.body);
                    showMessage(messageBody, 'received');
                });
                
                // 订阅房间消息
                stompClient.subscribe('/topic/room/room1', message => {
                    const messageBody = JSON.parse(message.body);
                    showMessage(messageBody, 'received');
                });
                
                // 订阅私人消息
                stompClient.subscribe('/user/queue/messages', message => {
                    const messageBody = JSON.parse(message.body);
                    showMessage(messageBody, 'received');
                });
                
                // 发送连接通知
                stompClient.send('/app/message', {}, 
                    JSON.stringify({
                        type: 'connect',
                        content: `${username} 已连接`,
                        sender: username,
                        timestamp: new Date().getTime()
                    })
                );
            }, error => {
                console.error('连接错误:', error);
                statusElement.textContent = `连接失败: ${error}`;
                statusElement.classList.remove('connected');
                statusElement.classList.add('disconnected');
                showMessage(`连接失败: ${error}`, 'system');
            });
        }
        
        // 断开WebSocket连接
        function disconnect() {
            if (stompClient !== null) {
                // 发送断开连接通知
                const username = usernameInput.value.trim();
                stompClient.send('/app/message', {}, 
                    JSON.stringify({
                        type: 'disconnect',
                        content: `${username} 已断开连接`,
                        sender: username,
                        timestamp: new Date().getTime()
                    })
                );
                
                // 断开连接
                stompClient.disconnect();
                stompClient = null;
                
                // 更新状态
                statusElement.textContent = '未连接';
                statusElement.classList.remove('connected');
                statusElement.classList.add('disconnected');
                
                // 启用/禁用按钮
                connectButton.disabled = false;
                disconnectButton.disabled = true;
                sendButton.disabled = true;
                
                showMessage('已断开连接', 'system');
            }
        }
        
        // 发送消息
        function sendMessage(event) {
            event.preventDefault();
            if (!stompClient) {
                alert('未连接到WebSocket服务器');
                return;
            }
            
            const destinationType = destinationTypeSelect.value;
            const messageType = messageTypeSelect.value;
            const messageContent = messageInput.value.trim();
            const username = usernameInput.value.trim();
            
            if (!messageContent) {
                alert('请输入消息内容');
                return;
            }
            
            let destination = '';
            let messageData = null;
            
            switch (destinationType) {
                case 'broadcast':
                    destination = '/app/message';
                    messageData = {
                        type: messageType,
                        content: messageContent,
                        sender: username,
                        timestamp: new Date().getTime()
                    };
                    break;
                    
                case 'echo':
                    destination = '/app/echo';
                    messageData = messageContent;
                    break;
                    
                case 'chat':
                    destination = '/app/chat';
                    messageData = {
                        type: messageType,
                        content: messageContent,
                        sender: username,
                        timestamp: new Date().getTime()
                    };
                    break;
                    
                case 'room':
                    const roomId = roomIdInput.value.trim();
                    destination = `/app/room/${roomId}`;
                    messageData = {
                        type: messageType,
                        content: messageContent,
                        sender: username,
                        timestamp: new Date().getTime()
                    };
                    break;
                    
                case 'private':
                    const recipient = recipientInput.value.trim();
                    destination = `/app/private/${recipient}`;
                    messageData = {
                        type: messageType,
                        content: messageContent,
                        sender: username,
                        timestamp: new Date().getTime()
                    };
                    break;
            }
            
            // 发送消息
            if (destinationType === 'echo') {
                stompClient.send(destination, {}, messageData);
            } else {
                stompClient.send(destination, {}, JSON.stringify(messageData));
            }
            
            // 显示发送的消息
            showMessage(`发送到 ${destination}: ${JSON.stringify(messageData, null, 2)}`, 'sent');
            
            // 清空消息输入框
            messageInput.value = '';
        }
        
        // 清空消息记录
        function clearMessages() {
            messagesElement.innerHTML = '';
        }
        
        // 更新目标类型相关的表单项
        function updateDestinationTypeUI() {
            const destinationType = destinationTypeSelect.value;
            
            // 显示/隐藏房间ID输入框
            if (destinationType === 'room') {
                roomIdContainer.style.display = 'block';
            } else {
                roomIdContainer.style.display = 'none';
            }
            
            // 显示/隐藏接收者输入框
            if (destinationType === 'private') {
                recipientContainer.style.display = 'block';
            } else {
                recipientContainer.style.display = 'none';
            }
        }
        
        // 注册事件监听器
        connectForm.addEventListener('submit', connect);
        disconnectButton.addEventListener('click', disconnect);
        sendForm.addEventListener('submit', sendMessage);
        clearButton.addEventListener('click', clearMessages);
        destinationTypeSelect.addEventListener('change', updateDestinationTypeUI);
        
        // 初始化UI
        updateDestinationTypeUI();
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            if (stompClient !== null) {
                disconnect();
            }
        });
    </script>
</body>
</html> 