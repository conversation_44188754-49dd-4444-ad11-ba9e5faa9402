<!DOCTYPE html>
<html>
<head>
    <title>STOMP WebSocket 测试</title>
    <meta charset="UTF-8">
    <!-- Import Element Plus style -->
    <link rel="stylesheet" href="//unpkg.com/element-plus/dist/index.css" />
    <style>
        body {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        .app-container {
            margin-top: 20px;
        }

        .message-box {
            height: 100%;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'SF Mono', SFMono-Regular, Consolas, Monaco, monospace;
            font-size: 14px;
            line-height: 1.6;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #ebeef5;
        }

        .spacing{
            height: 60px;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px 0;
            border-bottom: 1px solid #ebeef5;
        }

        .log-entry.debug {
            color: #909399;
        }

        .log-entry.error {
            color: #f56c6c;
        }

        .log-entry.success {
            color: #67c23a;
        }

        .log-entry.received {
            color: #409eff;
        }

        .chat-message {
            padding: 10px 16px;
            margin-bottom: 12px;
            border-radius: 12px;
            max-width: 80%;
            position: relative;
            line-height: 1.5;
        }

        .chat-message.sent {
            background-color: #409eff;
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }

        .chat-message.received {
            background-color: #f2f3f5;
            color: #303133;
            align-self: flex-start;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 500px;
            overflow-y: auto;
            padding: 16px;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #ebeef5;
        }

        .timestamp {
            font-size: 12px;
            color: #c0c4cc;
            margin-top: 4px;
            text-align: right;
        }

        .message-text {
            word-break: break-word;
        }

        pre {
            background-color: #303133;
            color: white;
            padding: 16px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: 'SF Mono', SFMono-Regular, Consolas, Monaco, monospace;
            font-size: 14px;
            margin: 12px 0;
        }

        .control-section {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
        }

        .el-collapse-item__content {
            padding-bottom: 20px;
        }

        .el-card {
            margin-bottom: 20px;
        }

        .footer-actions {
            margin-top: 16px;
        }

        .w-100 {
            width: 100%;
        }
    </style>

    <!-- Import Vue 3 -->
    <script src="//unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Import Element Plus -->
    <script src="//unpkg.com/element-plus"></script>
    <!-- Import Element Plus Chinese translations -->
    <script src="//unpkg.com/element-plus/dist/locale/zh-cn.min.js"></script>
</head>
<body>
    <div id="app">
        <el-config-provider :locale="zhCn">
            <el-container direction="vertical">
                <el-header>
                    <h1>STOMP WebSocket 测试</h1>
                    <el-alert
                        title="提示：我们提供两个WebSocket端点"
                        type="info"
                        :closable="false"
                        description="
                            /ws - 带SockJS支持的标准端点
                            /ws-raw - 纯WebSocket端点，适用于Postman等工具
                        ">
                    </el-alert>
                </el-header>


                <el-main class="app-container">
                    <div class="spacing"></div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-card shadow="hover">
                                <template #header>
                                    <div class="card-header">
                                        <h3>连接设置</h3>
                                    </div>
                                </template>

                                <el-form label-position="top">
                                    <el-form-item label="用户名">
                                        <el-input
                                            v-model="username"
                                            placeholder="请输入用户名"
                                            :disabled="isConnected">
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item label="端点">
                                        <el-select
                                            v-model="endpoint"
                                            class="w-100"
                                            :disabled="isConnected">
                                            <el-option label="ws (带SockJS)" value="ws"></el-option>
                                            <el-option label="ws-raw (纯WebSocket)" value="ws-raw"></el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item>
                                        <el-button
                                            type="primary"
                                            @click="connect"
                                            :disabled="isConnected">
                                            连接
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            @click="disconnect"
                                            :disabled="!isConnected">
                                            断开连接
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                            </el-card>

                            <el-card shadow="hover">
                                <template #header>
                                    <div class="card-header">
                                        <h3>发送消息</h3>
                                    </div>
                                </template>

                                <el-form label-position="top">
                                    <el-form-item label="目标地址">
                                        <el-input
                                            v-model="destination"
                                            placeholder="请输入目标地址"
                                            :disabled="!isConnected">
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item label="消息内容 (JSON)">
                                        <el-input
                                            v-model="message"
                                            type="textarea"
                                            :rows="4"
                                            placeholder="请输入JSON格式消息"
                                            :disabled="!isConnected">
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item>
                                        <el-button
                                            type="primary"
                                            @click="sendMessage"
                                            :disabled="!isConnected">
                                            发送消息
                                        </el-button>
                                        <el-button
                                            type="success"
                                            @click="sendPrivateMessage"
                                            :disabled="!isConnected">
                                            准备私信
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                            </el-card>

                            <el-card shadow="hover">
                                <template #header>
                                    <div class="card-header">
                                        <h3>订阅主题</h3>
                                    </div>
                                </template>

                                <el-form label-position="top">
                                    <el-form-item label="订阅地址">
                                        <el-input
                                            v-model="subscription"
                                            placeholder="请输入订阅地址"
                                            :disabled="!isConnected">
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item>
                                        <el-button
                                            type="primary"
                                            @click="subscribe"
                                            :disabled="!isConnected || isSubscribed">
                                            订阅
                                        </el-button>
                                        <el-button
                                            type="warning"
                                            @click="unsubscribe"
                                            :disabled="!isSubscribed">
                                            取消订阅
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                            </el-card>
                        </el-col>

                        <el-col :span="16">
                            <el-card shadow="hover" style="min-height: 600px;">
                                <template #header>
                                    <div class="card-header">
                                        <el-tabs v-model="activeTab">
                                            <el-tab-pane label="系统日志" name="logs"></el-tab-pane>
                                            <el-tab-pane label="消息对话" name="chat"></el-tab-pane>
                                            <el-tab-pane label="STOMP帧格式" name="format"></el-tab-pane>
                                        </el-tabs>
                                    </div>
                                </template>

                                <div v-show="activeTab === 'logs'">
                                    <div class="message-box" ref="consoleRef">
                                        <div
                                            v-for="(entry, index) in logEntries"
                                            :key="index"
                                            :class="['log-entry', entry.type]">
                                            <strong>{{ entry.timestamp }}</strong>: <span v-html="entry.message"></span>
                                        </div>
                                    </div>
                                    <div class="footer-actions">
                                        <el-button type="info" @click="clearLogs">清除日志</el-button>
                                    </div>
                                </div>

                                <div v-show="activeTab === 'chat'" class="chat-container" ref="chatRef">
                                    <div
                                        v-for="(msg, index) in chatMessages"
                                        :key="index"
                                        :class="['chat-message', msg.sentByMe ? 'sent' : 'received']">
                                        <div class="message-text" v-html="msg.content"></div>
                                        <div class="timestamp">{{ msg.timestamp }}</div>
                                    </div>
                                </div>

                                <div v-show="activeTab === 'format'">
                                    <el-alert
                                        title="STOMP帧格式 (Postman)"
                                        type="info"
                                        :closable="false"
                                        description="以下是在Postman中使用STOMP协议的正确格式示例">
                                    </el-alert>

                                    <h4>连接帧 (必须有一个空行和结尾的NULL字符):</h4>
                                    <pre>CONNECT
accept-version:1.2
host:localhost
login:postman-user

\0</pre>

                                    <h4>订阅帧:</h4>
                                    <pre>SUBSCRIBE
id:sub-1
destination:/topic/messages

\0</pre>

                                    <h4>发送帧:</h4>
                                    <pre>SEND
destination:/app/message
content-type:application/json

{"type":"text","content":"Hello","sender":"postman-user"}
\0</pre>

                                    <h4>发送私信消息:</h4>
                                    <pre>SEND
destination:/app/private-message
content-type:application/json

{"recipient":"someUsername","content":"你好，这是一条私信","type":"private"}
\0</pre>

                                    <el-alert
                                        title="关于用户身份(Principal)"
                                        type="warning"
                                        :closable="false"
                                        style="margin-top: 20px;"
                                        description="在WebSocket通信中，服务器需要知道消息发送者的身份。有以下几种方式提供身份信息:">
                                    </el-alert>

                                    <ol style="margin-top: 10px; line-height: 1.6;">
                                        <li><strong>连接时提供用户名</strong>: 在WebSocket URL中添加 <code>?username=yourName</code> 参数</li>
                                        <li><strong>STOMP连接帧中提供</strong>: 在CONNECT帧中添加 <code>login:yourName</code> 头部</li>
                                        <li><strong>消息中包含发送者信息</strong>: 在消息JSON中添加 <code>"sender":"yourName"</code> 字段</li>
                                    </ol>

                                    <p style="margin-top: 10px;">如果没有提供用户标识，系统会自动生成一个临时访客ID。</p>

                                    <h4>用户特定订阅 (接收私信):</h4>
                                    <pre>SUBSCRIBE
id:user-messages
destination:/user/queue/private-messages

\0</pre>
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </el-main>
            </el-container>
        </el-config-provider>
    </div>

    <!-- Import SockJS -->
    <script src="//unpkg.com/sockjs-client@1/dist/sockjs.min.js"></script>
    <!-- Import STOMP -->
    <script src="//unpkg.com/stompjs@2.3.3/lib/stomp.min.js"></script>

    <script>
    // 使用Vue 3的<script setup>语法需要再应用外写一个<script>标签
    // 因为浏览器不直接支持<script setup>语法，所以使用选项式API创建应用
    const { createApp, ref, reactive, toRefs, nextTick, onMounted } = Vue;

    const app = createApp({
        setup() {
            // 状态定义
            const state = reactive({
                // 连接信息
                username: 'testuser',
                endpoint: 'ws',
                isConnected: false,
                stompClient: null,

                // 订阅信息
                subscription: '/topic/messages',
                currentSubscription: null,
                isSubscribed: false,

                // 消息信息
                destination: '/app/message',
                message: JSON.stringify({
                    type: 'text',
                    content: '测试消息',
                    sender: 'testuser'
                }, null, 2),

                // UI状态
                activeTab: 'logs',
                logEntries: [],
                chatMessages: []
            });

            // DOM引用
            const consoleRef = ref(null);
            const chatRef = ref(null);

            // 国际化
            const zhCn = ElementPlusLocaleZhCn;

            // 日志函数
            const log = (message, type = 'info') => {
                state.logEntries.push({
                    message,
                    type,
                    timestamp: new Date().toLocaleTimeString()
                });

                // 滚动到底部
                nextTick(() => {
                    if (consoleRef.value) {
                        consoleRef.value.scrollTop = consoleRef.value.scrollHeight;
                    }
                });
            };

            // 添加聊天消息
            const addChatMessage = (message, sentByMe = false) => {
                let content = '';
                try {
                    // 如果是对象，格式化显示
                    if (typeof message === 'object') {
                        if (message.content) {
                            content = message.content;
                            const senderInfo = message.sender ? `<strong>${message.sender}</strong>: ` : '';
                            content = senderInfo + content;
                        } else {
                            content = JSON.stringify(message, null, 2);
                        }
                    } else {
                        content = message;
                    }
                } catch (e) {
                    content = String(message);
                }

                state.chatMessages.push({
                    content,
                    sentByMe,
                    timestamp: new Date().toLocaleTimeString()
                });

                // 滚动到底部
                nextTick(() => {
                    if (chatRef.value) {
                        chatRef.value.scrollTop = chatRef.value.scrollHeight;
                    }
                });
            };

            // 连接到STOMP服务器
            const connect = () => {
                const username = state.username.trim() || 'anonymous';
                const endpoint = state.endpoint;

                log(`尝试连接到 ${endpoint} 端点...`);

                try {
                    // 创建WebSocket连接
                    let socket;
                    if (endpoint === 'ws') {
                        // 使用SockJS
                        socket = new SockJS(`/${endpoint}`);
                    } else {
                        // 使用原生WebSocket
                        socket = new WebSocket(`ws://localhost:8080/${endpoint}?username=${username}`);
                    }

                    // 创建STOMP客户端
                    state.stompClient = Stomp.over(socket);

                    // 打开STOMP帧日志
                    state.stompClient.debug = (str) => {
                        log(`STOMP: ${str}`, 'debug');
                    };

                    // 连接到STOMP服务器
                    state.stompClient.connect(
                        { username: username },
                        (frame) => {
                            log(`已连接! ${frame}`, 'success');
                            state.isConnected = true;

                            // 自动订阅默认主题
                            subscribe();
                        },
                        (error) => {
                            log(`连接错误: ${error}`, 'error');
                            disconnect();
                        }
                    );
                } catch (e) {
                    log(`连接异常: ${e.message}`, 'error');
                }
            };

            // 断开STOMP连接
            const disconnect = () => {
                if (state.stompClient !== null) {
                    if (state.currentSubscription) {
                        unsubscribe();
                    }

                    state.stompClient.disconnect();
                    state.stompClient = null;
                    state.isConnected = false;
                    log('已断开连接');
                }
            };

            // 发送STOMP消息
            const sendMessage = () => {
                const destination = state.destination.trim();
                let messageText = state.message.trim();

                if (!destination) {
                    ElementPlus.ElMessage.error('请输入目标地址');
                    return;
                }

                try {
                    // 如果是JSON字符串，解析它
                    let message = messageText;
                    try {
                        const jsonObj = JSON.parse(messageText);
                        message = jsonObj;

                        // 添加到聊天界面
                        addChatMessage(jsonObj, true);
                    } catch (e) {
                        log('消息不是有效的JSON，将作为纯文本发送', 'warn');
                        // 添加到聊天界面
                        addChatMessage(messageText, true);
                    }

                    log(`发送消息到 ${destination}: ${messageText}`);
                    state.stompClient.send(destination, {}, typeof message === 'string' ? message : JSON.stringify(message));
                } catch (e) {
                    log(`发送消息失败: ${e.message}`, 'error');
                }
            };

            // 订阅STOMP主题
            const subscribe = () => {
                const subscription = state.subscription.trim();

                if (!subscription) {
                    ElementPlus.ElMessage.error('请输入订阅地址');
                    return;
                }

                try {
                    log(`订阅主题: ${subscription}`);
                    state.currentSubscription = state.stompClient.subscribe(subscription, (message) => {
                        try {
                            const body = JSON.parse(message.body);
                            log(`接收消息: ${JSON.stringify(body, null, 2)}`, 'received');

                            // 添加到聊天界面
                            addChatMessage(body, false);
                        } catch (e) {
                            log(`接收消息: ${message.body}`, 'received');

                            // 添加到聊天界面
                            addChatMessage(message.body, false);
                        }
                    });

                    state.isSubscribed = true;
                    log(`成功订阅 ${subscription}`);
                } catch (e) {
                    log(`订阅失败: ${e.message}`, 'error');
                }
            };

            // 取消订阅
            const unsubscribe = () => {
                if (state.currentSubscription) {
                    state.currentSubscription.unsubscribe();
                    log('已取消订阅');
                    state.currentSubscription = null;
                    state.isSubscribed = false;
                }
            };

            // 清除日志
            const clearLogs = () => {
                state.logEntries = [];
                log('日志已清除');
            };

            // 添加一个发送私信的快捷方式
            const sendPrivateMessage = () => {
                // 确保已连接
                if (!state.isConnected) {
                    ElementPlus.ElMessage.error('请先连接WebSocket');
                    return;
                }
                
                // 设置私信消息格式
                state.destination = '/app/private-message';
                state.message = JSON.stringify({
                    recipient: "someUsername", // 修改为实际接收者用户名
                    content: "你好，这是一条私信",
                    type: "private",
                    sender: state.username
                }, null, 2);
                
                // 提示用户
                ElementPlus.ElMessage.success('已准备私信消息模板，点击"发送消息"按钮发送');
            };

            // 初始日志
            onMounted(() => {
                log('测试页面已加载，请点击"连接"按钮开始测试');
            });

            return {
                ...toRefs(state),
                zhCn,
                consoleRef,
                chatRef,
                connect,
                disconnect,
                sendMessage,
                subscribe,
                unsubscribe,
                clearLogs,
                sendPrivateMessage
            };
        }
    });

    // 使用 Element Plus
    app.use(ElementPlus);

    // 挂载应用
    app.mount('#app');
    </script>
</body>
</html>
