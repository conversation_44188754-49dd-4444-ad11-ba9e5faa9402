<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端扫码登录</title>
    <script>
        // 配置基础URL
        const BASE_URL = 'http://localhost:8080';  // 根据实际部署环境修改
    </script>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f7f7;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            max-width: 400px;
            width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #3f51b5;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .qr-info {
            margin: 20px auto;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
            background: #fcfcfc;
            text-align: left;
        }
        
        .qr-info p {
            margin: 5px 0;
            color: #5c5c5c;
        }
        
        .login-info {
            margin: 20px 0;
            padding: 15px;
            background: #e8f5e9;
            border-radius: 4px;
            border-left: 4px solid #43a047;
        }
        
        .device-info {
            font-size: 14px;
            color: #757575;
            margin-bottom: 15px;
        }
        
        .btn-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 45%;
            border: none;
        }
        
        .btn-confirm {
            background: #4caf50;
            color: white;
        }
        
        .btn-cancel {
            background: #f44336;
            color: white;
        }
        
        .scan-form {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #3f51b5;
        }
        
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn-scan {
            background: #3f51b5;
            color: white;
            width: 100%;
            margin-top: 10px;
        }
        
        .hidden {
            display: none;
        }
        
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            color: white;
        }
        
        .alert-success {
            background: #4caf50;
        }
        
        .alert-error {
            background: #f44336;
        }
    </style>
</head>
<body>
    <!-- 扫描表单 -->
    <div class="scan-form" id="scanForm">
        <h1>扫描二维码登录</h1>
        <p>在这里模拟扫描二维码的过程</p>
        
        <div class="form-group">
            <label for="qrId">二维码ID</label>
            <input type="text" id="qrId" class="form-control" placeholder="请输入二维码ID">
        </div>
        
        <div class="form-group">
            <label for="token">用户令牌</label>
            <input type="text" id="token" class="form-control" placeholder="请输入用户令牌">
        </div>
        
        <button id="scanBtn" class="btn btn-scan">扫描二维码</button>
        
        <div id="scanAlert" class="alert hidden"></div>
    </div>
    
    <!-- 确认登录容器 -->
    <div class="container hidden" id="confirmContainer">
        <h1>确认登录</h1>
        
        <div class="device-info">
            检测到来自新设备的登录请求
        </div>
        
        <div class="qr-info">
            <p><strong>二维码ID:</strong> <span id="confirmQrId"></span></p>
            <p><strong>请求时间:</strong> <span id="requestTime"></span></p>
        </div>
        
        <div class="login-info">
            您正在登录Web网站，请确认是否为本人操作
        </div>
        
        <div class="btn-container">
            <button id="cancelBtn" class="btn btn-cancel">取消</button>
            <button id="confirmBtn" class="btn btn-confirm">确认登录</button>
        </div>
        
        <div id="confirmAlert" class="alert hidden"></div>
    </div>
    
    <script>
        // DOM 元素
        const scanForm = document.getElementById('scanForm');
        const confirmContainer = document.getElementById('confirmContainer');
        const qrIdInput = document.getElementById('qrId');
        const tokenInput = document.getElementById('token');
        const confirmQrIdSpan = document.getElementById('confirmQrId');
        const requestTimeSpan = document.getElementById('requestTime');
        const scanBtn = document.getElementById('scanBtn');
        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const scanAlert = document.getElementById('scanAlert');
        const confirmAlert = document.getElementById('confirmAlert');
        
        // 当前二维码ID
        let currentQrId = null;
        
        // 显示警告框
        function showAlert(element, message, isError = false) {
            element.textContent = message;
            element.classList.remove('hidden', 'alert-success', 'alert-error');
            element.classList.add(isError ? 'alert-error' : 'alert-success');
            
            // 3秒后隐藏
            setTimeout(() => {
                element.classList.add('hidden');
            }, 3000);
        }
        
        // 扫描二维码
        async function scanQRCode() {
            const qrId = qrIdInput.value.trim();
            const token = tokenInput.value.trim();
            
            if (!qrId || !token) {
                showAlert(scanAlert, '请输入二维码ID和用户令牌', true);
                return;
            }
            
            try {
                // 发送扫描请求
                const response = await fetch(BASE_URL + '/api/qrlogin/scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        qrId: qrId,
                        token: token
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok || !data.success) {
                    showAlert(scanAlert, data.error || '扫描失败，请重试', true);
                    return;
                }
                
                // 扫描成功，显示确认界面
                currentQrId = qrId;
                confirmQrIdSpan.textContent = qrId;
                requestTimeSpan.textContent = new Date().toLocaleString();
                
                scanForm.classList.add('hidden');
                confirmContainer.classList.remove('hidden');
                
                showAlert(confirmAlert, '已扫描二维码，请确认登录');
            } catch (error) {
                console.error('扫描失败:', error);
                showAlert(scanAlert, '网络错误，请重试', true);
            }
        }
        
        // 确认或取消登录
        async function handleConfirmation(confirmed) {
            if (!currentQrId) {
                showAlert(confirmAlert, '无效的二维码ID', true);
                return;
            }
            
            try {
                // 发送确认请求
                const response = await fetch(BASE_URL + '/api/qrlogin/confirm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        qrId: currentQrId,
                        confirmed: confirmed
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok || !data.success) {
                    showAlert(confirmAlert, data.error || '操作失败，请重试', true);
                    return;
                }
                
                // 操作成功
                showAlert(confirmAlert, confirmed ? '已确认登录' : '已取消登录');
                
                // 3秒后返回扫描界面
                setTimeout(() => {
                    currentQrId = null;
                    qrIdInput.value = '';
                    confirmContainer.classList.add('hidden');
                    scanForm.classList.remove('hidden');
                }, 3000);
            } catch (error) {
                console.error('操作失败:', error);
                showAlert(confirmAlert, '网络错误，请重试', true);
            }
        }
        
        // 绑定事件
        scanBtn.addEventListener('click', scanQRCode);
        confirmBtn.addEventListener('click', () => handleConfirmation(true));
        cancelBtn.addEventListener('click', () => handleConfirmation(false));
        
        // 从URL参数中提取二维码ID
        window.addEventListener('load', () => {
            const params = new URLSearchParams(window.location.search);
            const qrId = params.get('qrId');
            
            if (qrId) {
                qrIdInput.value = qrId;
            }
        });
    </script>
</body>
</html> 