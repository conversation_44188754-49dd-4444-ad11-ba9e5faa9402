# ========================
# 服务器配置
# ========================


server:
  port: 8080  # 应用服务端口
  compression:  # 响应压缩配置
    enabled: true  # 启用压缩
    mime-types: application/json,application/xml,text/html,text/plain  # 压缩类型
    min-response-size: 1024  # 最小压缩大小（字节）
  tomcat:  # Tomcat连接池配置
    max-threads: 200  # 最大工作线程数
    min-spare-threads: 10  # 最小空闲线程
    max-connections: 10000  # 最大连接数
    accept-count: 100  # 等待队列长度
    connection-timeout: 30000  # 连接超时(ms)
    uri-encoding: UTF-8

# ========================
# Spring核心配置
# ========================
spring:
  profiles:
    active: dev  # 激活的开发环境
  jmx:
    enabled: false  # 禁用JMX监控
  mvc:
    async:
      request-timeout: 1800000  # 30 minutes in milliseconds

  # ========================
  # 数据源配置
  # ========================
  datasource:
    primary:
      url: jdbc:mysql://************:3306/pure?useUnicode=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai  # 主数据库连接URL
      username: hao
      password: 12345678
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: HikariPool-Primary # 连接池名称
        maximum-pool-size: 10
        minimum-idle: 5
        idle-timeout: 300000
        connection-timeout: 20000
        max-lifetime: 1200000
    secondary:
      url: ******************************************************************************************************** # 次数据库连接URL
      username: hao
      password: 12345678
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: HikariPool-Secondary # 连接池名称
        maximum-pool-size: 10 # 可以根据需要调整次数据库的连接池大小
        minimum-idle: 5
        idle-timeout: 300000
        connection-timeout: 20000
        max-lifetime: 1200000

  # ========================
  # 安全配置
  # ========================
  security:
    user:  # 内存中的默认用户
      name: admin  # 管理员用户名
      password: admin  # 管理员密码

  # ========================
  # 文件上传配置
  # ========================
  servlet:
    multipart:
      max-file-size: 10GB  # 单个文件最大尺寸
      max-request-size: 10GB  # 请求最大尺寸
      enabled: true

  # ========================
  # Redis配置
  # ========================
  redis:
    host: localhost  # Redis主机
    port: 6379  # Redis端口
    database: 2  # 使用的数据库编号
    timeout: 10000  # 连接超时(ms)
    lettuce:  # Lettuce连接池
      pool:
        max-active: 8  # 最大活跃连接
        max-wait: -1  # 最大等待时间(ms)
        max-idle: 8  # 最大空闲连接
        min-idle: 0  # 最小空闲连接

  # ========================
  # 缓存配置
  # ========================
  cache:
    type: redis  # 使用Redis缓存
    redis:
      time-to-live: 3600000  # 缓存过期时间(ms)
      cache-null-values: true  # 是否缓存空值

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: fxyopjthfslpbbie
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    protocol: smtp
    default-encoding: UTF-8


# 验证码配置
verification:
  code:
    length: 6  # 验证码长度
    expiration: 360  # 验证码有效期(秒)

# ========================
# MyBatis配置
# ========================
mybatis:
  mapper-locations: classpath:mapper/*.xml  # XML映射文件路径
  type-aliases-package: com.example.pure.model  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换
    cache-enabled: true  # 启用二级缓存
    lazy-loading-enabled: true  # 启用延迟加载
    aggressive-lazy-loading: false  # 禁用侵略性延迟加载
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 日志实现

# ========================
# JWT配置
# ========================
jwt:
  secret: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa  # 64位HMAC密钥
  expiration: 604800  # 访问令牌有效期(秒)
  refresh-expiration: 1209600  # 刷新令牌有效期(秒)

# ========================
# 二维码登录配置
# ========================
qrlogin:
  redis:
    expire-seconds: 300  # 二维码在Redis中的过期时间(秒)
  timeout:
    display: 120  # 二维码显示的有效期(秒)
    cleanup: 300  # 二维码清理的超时时间(秒)


cloudflare:
  r2:
    endpoint: https://076ef3ccd15e2e7a449f7d712317824b.r2.cloudflarestorage.com
    access-key-id: 7a11146fa887bbe862d0c7b5c44acf8e
    secret-access-key: c613fd21bcee56149c83971c55880b5130ef7332845e2230b317fc1d3cceae31
    bucket: mysite
    region: auto # R2 的 region 固定为 auto
    presign-duration-minutes: 10 # 预签名URL的有效时间（分钟）
    public-url: https://hao8.fun

sprite:
  generation:
    path: d:/upload/video_sprites
# ========================
# API文档配置 ()
# ========================
springdoc:
  swagger-ui:
    path: /swagger-ui.html  # Swagger UI路径
  api-docs:
    path: /v3/api-docs  # OpenAPI文档路径
  packages-to-scan: com.example.pure.controller  # 扫描的控制器包
  #匹配的API路径
  paths-to-match: >
    /api/user/**,/api/auth/**,/api/verification/**,/api/files/image/**,
    /api/view/images/**,/api/user-profile/**,/api/messages/**,/api/video/interaction/**,
    /api/videoUrl/**,/api/messages/**,/oauth/**,/upload-general/**,/api/view/images/**,
    /api/qrcode/**,/api/qrlogin/**,/api/user-profile/**,/api/ws-test/** ,/api/video/comments/**

# ========================
# Knife4j增强配置,增强SpringDoc文档展示
# ========================
knife4j:
  enable: true  # 启用Knife4j
  setting:
    language: zh-CN  # 中文界面
    swagger-model-name: 实体类列表  # 模型名称
    enable-footer: false  # 禁用页脚
  basic:
    enable: false  # 禁用基础认证
    username: admin  # 文档访问账号
    password: admin  # 文档访问密码
  openapi:
    title: Spring Boot REST API  # 文档标题
    description: Spring Boot REST API with JWT Authentication  # 文档描述
    email: <EMAIL>  # 联系人邮箱
    version: 1.0  # API版本
    license: Apache 2.0  # 许可证
    license-url: https://springdoc.org  # 许可证链接
    terms-of-service: https://springdoc.org  # 服务条款
    group:  # 分组配置
      test1:
        group-name: 分组名称
        api-rule: package
        api-rule-resources:
          - com.example.pure.controller

# ========================
# 日志配置
# ========================
logging:
  level:
    root: INFO  # 根日志级别
    com.example.pure: DEBUG  # 应用包日志级别
    org.springframework.security: DEBUG  # 安全日志级别
    org.springframework.web: DEBUG  # Web日志级别
    org.mybatis: DEBUG  # MyBatis日志
    com.example.pure.mapper: DEBUG  # Mapper接口日志
    com.example.demo13.security: DEBUG  # 安全模块日志
    org.springframework.messaging: DEBUG
    org.springframework.web.socket: DEBUG
    org.springframework.web.socket.messaging: TRACE  # TRACE 提供更详细的信息
    com.example.demo13.controller.WebSocketTestController: DEBUG # 为你的控制器启用 DEBUG 日志

  file:
    name: logs/application.log  # 日志文件路径
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n"  # 控制台格式
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"  # 文件格式

# ========================
# Actuator监控配置
# ========================
management:
  endpoints:
    enabled-by-default: true  # 默认启用所有端点
    web:
      exposure:
        include: "*"  # 暴露所有Web端点
      base-path: /actuator  # 端点基础路径
    jmx:
      enabled: false  # 禁用JMX端点

  endpoint:
    health:
      show-details: always  # 显示健康详情
      show-components: always  # 显示组件详情
    prometheus:
      enabled: true  # 启用Prometheus端点
    loggers:
      enabled: true  # 启用日志级别端点
    threaddump:
      enabled: true  # 启用线程转储端点

  info:
    env:
      enabled: true  # 显示环境信息
    git:
      mode: full  # 完整Git信息
    java:
      enabled: true  # 显示Java信息

  metrics:
    tags:
      application: ${spring.application.name}  # 指标应用标签
    export:
      prometheus:
        enabled: true  # 启用Prometheus导出

  health:
    defaults:
      enabled: true  # 启用默认健康检查

file:
  storage:
    location:
      download: D:/AAA
      upload: D:/upload
    max-size: ********** #1GB

# ========================
# 应用信息配置
# ========================
info:
  app:
    name: Demo13 Application  # 应用名称
    description: Spring Boot Demo Application  # 应用描述
    version: 1.0.0  # 应用版本
    java:
      version: ${java.version}  # Java版本(自动获取)
    spring-boot:
      version: ${spring-boot.version}  # Spring Boot版本(自动获取)

# reCAPTCHA configuration
recaptcha:
  secret-key: "6LenmWYrAAAAAHuqQflQix2NMN5oYYd30mwHJsGP"

#配置雪花算法的两个参数
snowflake:
  datacenter-id: 1
